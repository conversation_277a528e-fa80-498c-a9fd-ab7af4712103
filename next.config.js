// Injected content via Sen<PERSON> wizard below
const { withSentryConfig } = require('@sentry/nextjs');

/** @type {import('next').NextConfig} */
module.exports = {
  transpilePackages: ['antd-mobile', '@tanstack/query-core', 'mqtt'],
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'new-gateway.limajituan.com',
        pathname: '/mars/static/**',
      },
      {
        protocol: 'https',
        hostname: 'new-gateway-lima.newtest.senthink.com',
        pathname: '/mars/static/**',
      },
      {
        protocol: 'https',
        hostname: 'thirdwx.qlogo.cn',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'gateway.qa.lima.senthink.com:9990',
        pathname: '/mars/static/**',
      },
    ],
    // mobile
    deviceSizes: [640, 720, 750, 780, 822, 824, 828, 856, 860],
  },
};

module.exports = withSentryConfig(
  module.exports,
  {
    // For all available options, see:
    // https://github.com/getsentry/sentry-webpack-plugin#options

    // Suppresses source map uploading logs during build
    silent: true,
    org: 'sentry',
    project: 'lima-h5',
    url: 'https://sentry.senthink.com',
  },
  {
    // For all available options, see:
    // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

    // Upload a larger set of source maps for prettier stack traces (increases build time)
    widenClientFileUpload: true,

    // Transpiles SDK to be compatible with IE11 (increases bundle size)
    transpileClientSDK: true,

    // Routes browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers. (increases server load)
    // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
    // side errors will fail.
    tunnelRoute: '/monitoring',

    // Hides source maps from generated client bundles
    hideSourceMaps: true,

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,
  }
);
