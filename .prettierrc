{"printWidth": 100, "tabWidth": 2, "singleQuote": true, "proseWrap": "never", "semi": false, "trailingComma": "none", "overrides": [{"files": ".prettier<PERSON>", "options": {"parser": "json"}}], "plugins": ["prettier-plugin-organize-imports", "prettier-plugin-package<PERSON><PERSON>"], "importOrder": ["^react$", "^react-dom$", "^react(.*)", "^next(.*)", "^antd-mobile(.*)", "<THIRD_PARTY_MODULES>", "^@/components/(.*)", "^@/lib/(.*)", "^@/server/(.*)", "^@/utils/(.*)", "^@/styles/(.*)", "^@/(.*)", "^[./]"], "importOrderSortSpecifiers": true}