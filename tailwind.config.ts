import type { Config } from 'tailwindcss';
import plugin from 'tailwindcss/plugin';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/islands/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: 'var(--adm-color-primary)',
        error: 'var(--adm-color-danger)',
        warning: 'var(--adm-color-warning)',
        success: 'var(--adm-color-success)',
        'color-text': 'var(--adm-color-text)',
        'color-secondary': 'var(--adm-color-text-secondary)',
        'color-weak': 'var(--adm-color-weak)',
        'color-light': 'var(--adm-color-light)',
        'color-border': 'var(--adm-color-border)',
        'color-box': 'var(--adm-color-box)',
        'color-background': 'var(--color-background)',
      },
      keyframes: {
        cursor: {
          from: {
            opacity: '1',
          },
          '60%': {
            opacity: '1',
          },
          '80%': {
            opacity: '0',
          },
          to: {
            opacity: '0',
          },
        },
        slideUpAndFade: {
          from: { opacity: '0', transform: 'translateY(2px)' },
          to: { opacity: '1', transform: 'translateY(0)' },
        },
        slideRightAndFade: {
          from: { opacity: '0', transform: 'translateX(-2px)' },
          to: { opacity: '1', transform: 'translateX(0)' },
        },
        slideDownAndFade: {
          from: { opacity: '0', transform: 'translateY(-2px)' },
          to: { opacity: '1', transform: 'translateY(0)' },
        },
        slideLeftAndFade: {
          from: { opacity: '0', transform: 'translateX(2px)' },
          to: { opacity: '1', transform: 'translateX(0)' },
        },
        // 充电动画
        chargeTop: {
          from: { transform: 'translateY(0)' },
          to: { transform: 'translateY(-170px)' },
        },
        chargeBottom: {
          from: { transform: 'translateY(0)' },
          to: { transform: 'translateY(170px)' },
        },
      },
      animation: {
        slideUpAndFade: 'slideUpAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
        slideRightAndFade: 'slideRightAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
        slideDownAndFade: 'slideDownAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
        slideLeftAndFade: 'slideLeftAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
        chargeTop: 'chargeTop 5s linear infinite',
        chargeBottom: 'chargeBottom 5s linear infinite',
      },
      backgroundImage: {
        'gradient-129': 'linear-gradient(129deg, var(--tw-gradient-stops))',
        'gradient-135': 'linear-gradient(135deg, var(--tw-gradient-stops))',
      },
    },
    // font-size 设计稿单行按以下行高，多行1.5
    fontSize: {
      '3xs': ['12px', '17px'],
      '2xs': ['13px', '18px'],
      xs: ['14px', '19px'],
      sm: ['15px', '21px'],
      base: ['16px', '22px'],
      lg: ['17px', '24px'],
      xl: ['18px', '25px'],
      '2xl': ['20px', '28px'],
      '3xl': ['24px', '31px'],
      '4xl': ['28px', '39px'],
      '5xl': ['32px', '41px'],
      '6xl': ['44px', '56px'],
      '7xl': ['48px', '54px'],
      '8xl': ['56px', '72px'],
    },
  },
  plugins: [
    plugin(function ({ addUtilities }) {
      addUtilities({
        '.scrollbar-none': {
          /* IE and Edge */
          '-ms-overflow-style': 'none',

          /* Firefox */
          'scrollbar-width': 'none',

          /* Safari and Chrome */
          '&::-webkit-scrollbar': {
            display: 'none',
          },
        },
      });
    }),
  ],
};

export default config;
