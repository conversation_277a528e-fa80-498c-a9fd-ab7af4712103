{"name": "lima-h5", "version": "0.1.0", "private": true, "scripts": {"build": "cp .env.$APP_ENV.sample .env.production && next build", "build-dev": "next build", "dev": "next dev", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,css,md}\"", "lint": "next lint", "lint:format": "prettier --cache --check .", "prepare": "husky install", "start": "next start", "test": "jest"}, "browserslist": ["ChromeAndroid 64", "iOS >= 12"], "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@react-spring/web": "^9.7.3", "@sentry/nextjs": "^7.108.0", "@tanstack/react-query": "^5.13.4", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@uiw/react-color-wheel": "^2.0.3", "aliyun_numberauthsdk_web": "^2.1.11", "antd-mobile": "^5.34.0", "antd-mobile-icons": "^0.3.0", "classnames": "^2.3.2", "common-hook": "^1.1.0", "common-screw": "^1.3.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.4.3", "js-cookie": "^3.0.5", "modern-screenshot": "^4.4.37", "mqtt": "^5.7.2", "next": "14.1.4", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.10", "react": "^18", "react-dom": "^18", "react-infinite-scroll-component": "^6.1.0", "sass": "^1.85.1", "scroll-into-view-if-needed": "^3.1.0", "valtio": "^1.13.0"}, "devDependencies": {"@amap/amap-jsapi-types": "^0.0.13", "@commitlint/cli": "^18.2.0", "@commitlint/config-conventional": "^18.1.0", "@types/crypto-js": "^4.2.1", "@types/jest": "^29.5.12", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.1", "eslint-config-prettier": "^9.0.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.0.2", "lodash-es": "^4.17.21", "postcss": "^8", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.4.1", "typescript": "^5"}}