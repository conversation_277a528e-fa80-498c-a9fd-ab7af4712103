// 11为手机号
export const PHONEREGEX = /^1[3-9]\d{9}$/;

// 6-26位字母或数字组合
export const PASSWORDREGEX = /^[a-zA-Z0-9]{6,26}$/;

// ^[a-zA-Z0-9._%+-] 邮箱用户名部分，可以包含字母、数字、点、下划线、百分号、加号和减号
// @[a-zA-Z0-9.-] 邮箱域名部分，可以包含字母、数字、点和横岗
// \.[a-zA-Z]{2,}$ 邮箱顶级域名部分，必须以点开头，后面跟着至少两个字母
export const EMAILREGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export const IDCARDREGEX = /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/;
