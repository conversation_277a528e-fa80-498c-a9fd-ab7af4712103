/*
 * @Date: 2025-02-20 10:36:53
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-03-12 10:22:46
 */
import md5 from 'crypto-js/md5'

// encrypt password
export function encryptPassword(password: string) {
  return md5(password).toString().toUpperCase()
}

export function encryptCode(_: string) {
  return md5(encryptPassword(_ + 'null') + 'Senthink2017')
    .toString()
    .toUpperCase()
}

// @ts-ignore
const addZero = (m) => (m < 10 && m >= 0 ? '0' + m : m)
// @ts-ignore
export const timestampToDate = (timestamp) => {
  if (!timestamp) return null
  const time = new Date(timestamp)
  const y = time.getFullYear()
  const M = time.getMonth() + 1
  const d = time.getDate()
  const h = time.getHours()
  const m = time.getMinutes()
  const s = time.getSeconds()
  return (
    y + '-' + addZero(M) + '-' + addZero(d) + ' ' + addZero(h) + ':' + addZero(m) + ':' + addZero(s)
  )
}

const isEmptyObject = (obj: any): boolean => {
  if (typeof obj === 'object' && Object.keys(obj).length === 0) {
    return true
  }
  return false
}

export const isNil = (o: unknown): boolean =>
  o === null || o === undefined || o === '' || isEmptyObject(o)

export const toEnumArray = (data = {}, id: string = 'value', label: string = 'label') => {
  if (isNil(data)) return []
  // @ts-ignore
  const arr = Object.keys(data).map((key) => ({ [id]: key, [label]: data[key] }))
  return arr
}

// 时间ms转成分钟，四舍五入
export function formatTime(timeInMilliseconds: number) {
  return Math.round(timeInMilliseconds / 1000 / 60)
}

export const toImg = (img: string): string => process.env.NEXT_PUBLIC_APP_URL + '/mars/static' + img

export const toContent = (str: string = ''): string =>
  // @ts-ignore
  str.replace(/\/API_BASE/g, process.env.NEXT_PUBLIC_APP_URL)

export const toPhpImg = (img: string): string => process.env.NEXT_PUBLIC_PHP_URL + img

export const toPhpContent = (str: string = ''): string =>
  // @ts-ignore
  str.replace(/\/ueditor/g, process.env.NEXT_PUBLIC_PHP_URL + '/ueditor')

export const toMarksArray = (data = []) =>
  // @ts-ignore
  Object.keys(data).map((key) => ({ value: parseInt(key) + 1, label: data[key] }))

/**
 * 当前时间是否处于该时间段内
 * @param withinTime 08:30-17:00
 */
export const isWithinTime = (withinTime:string) => {
  if(!withinTime) {
    return false
  }
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  const currentTimeInMinutes = currentHour * 60 + currentMinute;

  const [startTime, endTime] = withinTime.split('-');

  const [startHour, startMinute] = startTime.split(':').map(Number);
  const [endHour, endMinute] = endTime.split(':').map(Number);

  const startTotalMinutes = startHour * 60 + startMinute;
  const endTotalMinutes = endHour * 60 + endMinute;

  return currentTimeInMinutes >= startTotalMinutes && currentTimeInMinutes < endTotalMinutes;
}
