import type { Metadata, Viewport } from 'next';
import { NativeProvider } from '@/lib/native';
import Providers from '@/lib/providers';

import '@/styles/index.css';

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

export const viewport: Viewport = {
  viewportFit: 'cover',
  userScalable: false,
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className="min-h bg-color-background">
        <Providers>
          <NativeProvider>{children}</NativeProvider>
        </Providers>
      </body>
    </html>
  );
}
