'use client';

import './page.scss';
import { Button, Input, Toast} from 'antd-mobile';
import Page from '@/components/Page';
import {IDCARDREGEX} from "@/utils";
import {useState, useTransition} from "react";
import {useRouter} from "next/navigation";
import {id2MetaVerify} from '@/server/actions/me';

// 设置
export default function MetaVefify() {
  const router = useRouter();
  const [isPending, startTransitionSignup] = useTransition();
  const [identifyNum, setIdentifyNum] = useState('');
  const [userName, setUserName] = useState('');

  const handleSubmit = async () => {
    const id2MetaVerifyData = {
      userName,
      identifyNum
    }
    const verifyStatus = await id2MetaVerify(id2MetaVerifyData)
    if(verifyStatus) {
      Toast.show('实名认证完成');
      router.back();
    } else {
      Toast.show('实名认证失败，请重新检查信息');
    }

  }

  return (
    <Page title="实名认证">
      <div className="space-y-3 px-3 py-6 bg-white metaVerify_container">
        <div className="px-1 text-black ">
          <div className="title text-2xl mb-3">实名认证</div>
          <div className="text-xs text-gray-500">
            根据《移动互联网应用程序信息服务管理规定》需进行实名认证后才能使用服务
          </div>
          <div className="form">
            <Input
              placeholder="请输入您的姓名"
              className="form_input"
              value={userName}
              onChange={(val) => {
                setUserName(val);
              }}
            />
              <div>
                <Input
                  placeholder="请输入您的身份证号"
                  className="form_input form_input_password"
                  value={identifyNum}
                  onChange={(val) => {
                    setIdentifyNum(val);
                  }}
                />
              </div>
          </div>

          <div className="button">
              <Button
                block
                shape="rounded"
                color="primary"
                className="button_item"
                onClick={() => handleSubmit()}
                disabled={!userName || !IDCARDREGEX.test(identifyNum)}
              >
                认证
              </Button>
            <div className="text-gray-500 text-center mt-3" style={{fontSize: '10px'}}>
              点击认证即代表您已阅读并同意 <span className="text-blue-400">立马科技实名相关协议</span>
            </div>
          </div>
        </div>
      </div>
    </Page>
  );
}
