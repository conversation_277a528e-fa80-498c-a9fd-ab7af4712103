'use client';

import React from 'react';
import Page from '@/components/Page';
import { useRouter } from 'next/navigation';
import List from '@/components/List';
import cookies from 'js-cookie';

// 设置
export default function MeSetting() {
  const router = useRouter();

  return (
    <Page title="关于立马">
      <div className="my-12 w-full text-center">
        <div className="mb-5 h-[80px] w-full  bg-[url('/images/G/<EMAIL>')] bg-contain bg-center bg-no-repeat " />
        <span className="text-xl text-color-weak">{cookies.get('buildVersion')!}</span>
      </div>
      <div className="space-y-3 px-3 py-10" style={{ paddingTop: '0.75rem' }}>
        <List
          items={[
            {
              title: '微博',
              value: '@立马电动车',
            },
          ]}
        />
        <List
          items={[
            {
              title: '微信公众号',
              value: 'lima2023',
            },
            {
              title: '官网网址',
              value: 'http://wwww.shanghailima.com',
            },
            {
              title: '联系方式',
              value: '4008818777',
              onClick: () => window.jsBridgeHelper?.sendMessage('phoneCall', '4008818777'),
            },
          ]}
        />
        <div
          className="px-3 text-center text-xs"
          style={{ position: 'fixed', bottom: '30px', left: 0, width: '100%' }}
        >
          {/* <div>
            《<span className="underline">软件许可及服务协议</span>》
          </div> */}
          <div className="mt-2 text-color-weak">© 2016立马车业集团有限公司</div>
        </div>
      </div>
    </Page>
  );
}
