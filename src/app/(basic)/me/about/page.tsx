'use client';

import React from 'react';
import { Toast, Dialog } from 'antd-mobile';
import Page from '@/components/Page';
import List from '@/components/List';
import { getAppUp } from '@/server/actions/me';
import { useNative } from '@/lib/native';
import cookies from 'js-cookie';
import { toImg } from '@/utils/utils';

// 设置
export default function MeSetting() {
  const { isAndroid } = useNative();

  const toCheck = async () => {
    const data: any = await getAppUp(isAndroid ? 'android' : 'ios', cookies.get('versionCode')!);
    if (data?.error) {
      Toast.show(data.error);
      return;
    }
    if (data.isNew) {
      Toast.show('已经是最新版本');
    } else {
      Dialog.confirm({
        title: '检查更新',
        content: '发现新版本，是否要进行更新？',
        confirmText: '立即更新',
        onAction: (action) => {
          if (action.key === 'confirm') {
            const url = isAndroid ? toImg(data?.appVesion.srcUrl) : data?.appVesion.srcUrl;
            window.jsBridgeHelper.sendMessage('appUpdate', {
              url,
              fileSize: data?.appVesion.fileSize,
            });
          }
        },
      });
    }
  };
  return (
    <Page title="关于">
      <div className="my-12 w-full text-center">
        <div className="mb-5 h-[80px] w-full  bg-[url('/images/G/<EMAIL>')] bg-contain bg-center bg-no-repeat " />
        <span className="text-xl text-color-weak">{cookies.get('buildVersion')!}</span>
      </div>
      <div className="space-y-3 px-3 py-10" style={{ paddingTop: '0.75rem' }}>
        <List
          items={[
            {
              title: '检查更新',
              arrow: true,
              onClick: toCheck,
            },
          ]}
        />
        <List
          items={[
            {
              title: '用户协议',
              href: '/me/about/agreement',
            },
            {
              title: '隐私政策',
              href: '/me/about/privacy',
            },
            {
              title: '关于立马',
              href: `/me/about/lima`,
            },
          ]}
        />
      </div>
    </Page>
  );
}
