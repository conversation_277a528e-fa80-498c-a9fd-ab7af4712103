// @ts-nocheck

'use client';

import React, { useEffect, useState, useTransition } from 'react';
import { Toast, Dialog } from 'antd-mobile';
import Page from '@/components/Page';
import List from '@/components/List';
import Switch from '@/components/Switch';
import Button from '@/components/Button';
import { getPushSet, getNoticeSet } from '@/server/actions/me';
import { useLogout } from '@/hooks/useLogout';
import { useQueryClient } from '@tanstack/react-query';

// 设置
export default function MeSetting() {
  const [isPending, startTransition] = useTransition();
  const [mileState, setMileState] = useState(0);
  const [cache, setCache] = useState(0);
  const [detail, setDetail] = useState({});
  const queryClient = useQueryClient();
  const logout = useLogout();

  useEffect(() => {
    startTransition(async () => {
      const data: any = await getNoticeSet();
      console.log('[ detail ] >', data);
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      setDetail(data);
      setMileState(!!data.mileState);
    });
  }, []);

  const toSwitch = (value: boolean) => {
    startTransition(async () => {
      const result = await getPushSet({ mileState: value ? 1 : 0 });
      if (result?.error) {
        Toast.show(result.error);
        return;
      }
      setMileState(value);
      Toast.show('操作成功');
    });
  };

  const toLogOut = () => {
    Dialog.confirm({
      content: '确定退出登录？',
      onAction: (action) => {
        if (action.key === 'confirm') {
          queryClient.clear();
          logout();
        }
      },
    });
  };

  const toClearCache = () => {
    setCache(0);
    Toast.show('清空缓存成功');
  };

  return (
    <Page title="设置">
      <div className="space-y-3 px-3 py-6" style={{ paddingTop: '0.75rem' }}>
        <List
          items={[
            {
              title: '账号管理',
              href: 'setting/account',
            },
            {
              title: '推送设置',
              href: 'setting/push',
            },
          ]}
        />
        <List
          items={[
            {
              title: '里程',
              description: '开启后车况页显示剩余里程，关闭显示总里程',
              value: <Switch checked={mileState} onChange={toSwitch} />,
            },
          ]}
        />
        <List
          items={[
            {
              title: '清空缓存',
              value: cache + ' K',
              arrow: true,
              onClick: toClearCache,
            },
          ]}
        />
        <div
          className="  px-3  "
          style={{ position: 'fixed', bottom: '30px', left: 0, width: '100%' }}
        >
          <Button block type="default" onClick={toLogOut}>
            退出登录
          </Button>
        </div>
      </div>
    </Page>
  );
}
