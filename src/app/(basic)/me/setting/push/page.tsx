// @ts-nocheck

'use client';

import React, { useEffect, useState, useTransition } from 'react';
import { Toast } from 'antd-mobile';
import Page from '@/components/Page';
import List from '@/components/List';
import Switch from '@/components/Switch';
import Button from '@/components/Button';
import { getNoticeSet, getPushSet } from '@/server/actions/me';

// 设置
export default function MeSetting() {
  const [isPending, startTransition] = useTransition();
  const [list, setList] = useState({});

  useEffect(() => {
    toInit();
  }, []);

  const toInit = () => {
    startTransition(async () => {
      const data: any = await getNoticeSet();
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      console.log('[ data ] >', data);
      setList(data);
    });
  };

  const toSwitch = (type: string, value: boolean) => {
    console.log('[ value ] >', type);
    console.log('[ value ] >', value);
    startTransition(async () => {
      const result: any = await getPushSet({ [type]: value ? 1 : 0 });
      if (result?.error) {
        Toast.show(result.error);
        return;
      }
      toInit();
    });
  };

  return (
    <Page title="推送设置">
      <div className="space-y-3 px-3 py-10" style={{ paddingTop: '0.75rem' }}>
        <List
          items={[
            {
              title: '接收推送通知',
              value: (
                <Switch
                  checked={list.mainSwitch}
                  onChange={(value) => toSwitch('mainSwitch', value)}
                />
              ),
            },
          ]}
        />
        {list.mainSwitch ? (
          <List
            items={[
              {
                title: '车辆通知',
                value: <Switch checked={list.dvc} onChange={(value) => toSwitch('dvc', value)} />,
              },
              {
                title: '系统通知',
                value: (
                  <Switch checked={list.notify} onChange={(value) => toSwitch('notify', value)} />
                ),
              },
              {
                title: '活动信息',
                value: (
                  <Switch
                    checked={list.activity}
                    onChange={(value) => toSwitch('activity', value)}
                  />
                ),
              },
              {
                title: '其他通知',
                value: (
                  <Switch checked={list.other} onChange={(value) => toSwitch('other', value)} />
                ),
              },
            ]}
          />
        ) : null}
      </div>
    </Page>
  );
}
