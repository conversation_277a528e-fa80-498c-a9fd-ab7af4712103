'use client';

import { Toast, Dialog } from 'antd-mobile';
import Page from '@/components/Page';
import { ExclamationCircleFill } from 'antd-mobile-icons';
import { useRouter } from 'next/navigation';
import List from '@/components/List';
import Button from '@/components/Button';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { toBindThird } from '@/server/actions/me';
import { getThirdBindStatus, getUserInfo } from '@/server/api/me';
import { useNative } from '@/lib/native';

// 设置
export default function MeSetting() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { hasWechat } = useNative();

  const { data: bindState } = useQuery({
    queryKey: ['me', 'bindState'],
    queryFn: getThirdBindStatus,
  });

  const { data: userInfo } = useQuery({
    queryKey: ['userinfo'],
    queryFn: getUserInfo,
  });

  const toLogOff = () => {
    Dialog.confirm({
      header: (
        <ExclamationCircleFill
          style={{
            fontSize: 64,
            color: 'var(--adm-color-warning)',
          }}
        />
      ),
      title: '温馨提示',
      content:
        '注销账号是不可恢复的操作，请您谨慎操作，一旦注销成功，您将无法登录或使用账号内的信息，且当前账号没有绑定任何车辆时才可注销。',
      confirmText: '确定注销',
      onAction: (action) => {
        if (action.key === 'confirm') {
          router.push(`/user/log-off?phone=${userInfo?.loginName}`);
        }
      },
    });
  };

  const handleBind = (loginType: string, openidKey: string) => {
    window.jsBridgeHelper?.sendMessage(loginType).then(async (res: any) => {
      if (!res) return;
      const result = await toBindThird({
        openid: res[openidKey],
        nickname: res.nickname,
        thirdType: res.type,
        unionid: res.wxUnionid || '',
        img: res.img || '',
        type: 0,
        phone: userInfo?.loginName,
      });
      if (result.error) {
        Toast.show(result.error);
      } else {
        Toast.show('绑定成功');
        queryClient.invalidateQueries({ queryKey: ['me', 'bindState'] });
      }
    });
  };

  return (
    <Page title="账号管理">
      <div className="space-y-3 px-3 py-10" style={{ paddingTop: '0.75rem' }}>
        <List
          items={[
            {
              title: '手机号',
              value: userInfo?.loginName || '请完善',
              arrow: true,
              onClick: () =>
                userInfo?.loginName
                  ? router.push(`/user/edit-phone?phone=${userInfo?.loginName}`)
                  : router.push(`/user/bind-phone`),
            },
          ]}
        />

        <List
          items={[
            {
              title: '微信',
              value: bindState?.weixin ? (
                '已绑定'
              ) : (
                <span className="text-[#2FB8FF]" onClick={() => handleBind('wxLogin', 'wxOpenid')}>
                  绑定
                </span>
              ),
              arrow: !bindState?.weixin,
              show: hasWechat,
            },
            {
              title: 'Apple',
              value: bindState?.apple ? (
                '已绑定'
              ) : (
                <span
                  className="text-[#2FB8FF]"
                  onClick={() => handleBind('appleLogin', 'appleOpenid')}
                >
                  绑定
                </span>
              ),
              arrow: !bindState?.apple,
            },
          ]}
        />
        <List
          items={[
            {
              title: '修改密码',
              href: `/user/edit-password?phone=${userInfo?.loginName}`,
            },
          ]}
        />
        <div className="px-3" style={{ position: 'fixed', bottom: '30px', left: 0, width: '100%' }}>
          <Button block type="default" onClick={toLogOff}>
            注销账号
          </Button>
        </div>
      </div>
    </Page>
  );
}
