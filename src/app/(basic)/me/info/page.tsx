// @ts-nocheck

'use client';

import React, { useState, useTransition } from 'react';
import Image from 'next/image';
import { Picker, Dialog, Input, DatePicker, CascadePicker, Toast, ActionSheet } from 'antd-mobile';
import List from '@/components/List';
import Page from '@/components/Page';
import { toEditInfo } from '@/server/actions/me';
import { getUserInfo } from '@/server/api/me';
import { timestampToDate, toEnumArray, toImg } from '@/utils';
import type { PickerColumn } from 'antd-mobile/es/components/picker';
import { cityTree } from '@/utils/pca';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {useRouter} from "next/navigation";

export default function Info() {
    const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [visible, setVisible] = useState(false);

  const { data } = useQuery({ queryKey: ['userInfo'], queryFn: getUserInfo });
  const queryClient = useQueryClient();

  const sexEnum = { 0: '男', 1: '女' };

  const toEdit = (type: string, value: string) => {
    startTransition(async () => {
      const data: any = await toEditInfo({ [type]: value });
      console.log('[ data ] >', data);
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      queryClient.invalidateQueries({ queryKey: ['userInfo'] });
    });
  };

  const toInput = (type: string, value: string, name: string) => {
    Dialog.confirm({
      content: (
        <div className="flex flex-col items-center justify-center px-2 text-xl">
          {name}
          <Input
            id="inputData"
            defaultValue={value}
            maxLength={type === 'idCard' ? 18 : 12}
            placeholder={'请输入' + name}
            className="mb-2  mt-3 rounded px-2"
            style={{ border: '1px solid #e5e5e5', height: 40 }}
          />
        </div>
      ),
      onAction: (action) => {
        if (action.key === 'confirm') {
          const value: string = document.getElementById('inputData').value;

          if (type === 'idCard' && !/^\d{17}[\dX]$/.test(value)) {
            Toast.show({ content: '请填写正确的身份证号', position: 'top' });
            throw new Error();
          }
          if (value) {
            toEdit(type, value);
          } else {
            Toast.show({ content: '请填写信息', position: 'top' });
            throw new Error();
          }
        }
      },
    });
  };

  // 头像
  const handleAction = (action: any) => {
    console.log('[ action.key ] >', action.key);
    setVisible(false);

    window.jsBridgeHelper
      ?.sendMessage('chooseImage', { type: action.key })
      .then((res: string | null) => {
        if (res) {
          toEdit('logo', toImg(res));
        }
      });
  };

  // 跳转实名认证
  const jumpToMetaVerify = (realName: string|null) =>  {
    if(realName) {
      Toast.show('已实名认证')
    } else {
      router.push( `/me/metaVerify`)
    }
  }

  return (
    <Page title="个人信息">
      <div className="space-y-3 px-3 py-6">
        <List
          items={[
            {
              title: '头像',
              arrow: true,
              onClick: () => setVisible(true),
              value: (
                <div className="relative h-[40px] w-[40px] overflow-hidden rounded-full">
                  <Image
                    priority
                    fill
                    quality={25}
                    alt="头像"
                    sizes="40px"
                    src={data?.logo || '/images/G/<EMAIL>'}
                    className="object-cover"
                  />
                </div>
              ),
            },
            {
              title: '昵称',
              arrow: true,
              value: data?.nickName || '请完善',
              onClick: () => toInput('nickName', data?.nickName, '昵称'),
            },
          ]}
        />

        <List
          items={[
            // TODO 暂不开启
            // {
            //   title: '实名信息',
            //   arrow: !data?.realName,
            //   value: data?.realName ? '已认证' : '前往认证',
            //   onClick: () => jumpToMetaVerify(data?.realName || null),
            // },
            {
              title: '手机号',
              value: data?.loginName || '请完善',
              href: `/user/edit-phone?phone=${data?.loginName}`,
            },
            {
              title: '性别',
              arrow: true,
              onClick: (_, valueChildRef) => {
                valueChildRef.current?.open();
              },
              value: (
                <Picker
                  columns={[toEnumArray(sexEnum) as PickerColumn]}
                  value={[data?.sex?.toString()]}
                  cancelText="取消"
                  onConfirm={(_) => toEdit('sex', _[0])}
                >
                  {() => sexEnum[data?.sex] || '请完善'}
                </Picker>
              ),
            },
            {
              title: '生日',
              arrow: true,
              onClick: (_, valueChildRef) => valueChildRef.current?.open(),
              value: (
                <DatePicker
                  title="时间选择"
                  min={new Date(Date.now() - 100 * 365 * 24 * 60 * 60 * 1000)}
                  max={new Date()}
                  value={data?.birth ? new Date(parseInt(data?.birth)) : null}
                  onConfirm={(val) => toEdit('birth', val.getTime())}
                >
                  {() =>
                    data?.birth ? timestampToDate(parseInt(data?.birth)).split(' ')[0] : '请完善'
                  }
                </DatePicker>
              ),
            },
            {
              title: '真实姓名',
              arrow: true,
              value: data?.realName || '请完善',
              onClick: () => toInput('realName', data?.realName, '真实姓名'),
            },
            {
              title: '身份证号',
              arrow: true,
              value: data?.idCard || '请完善',
              onClick: () => toInput('idCard', data?.idCard, '身份证号'),
            },
            {
              title: '所在城市',
              arrow: true,
              value: data?.address || '请完善',

              onClick: (_, valueChildRef) => valueChildRef.current?.open(),
              value: (
                <CascadePicker
                  options={cityTree}
                  value={data?.address ? data.address.split(' ') : []}
                  cancelText="取消"
                  onConfirm={(_) => toEdit('address', _.join(' '))}
                >
                  {() => data?.address || '请完善'}
                </CascadePicker>
              ),
            },
          ]}
        />
        <ActionSheet
          cancelText="取消"
          visible={visible}
          actions={[
            { text: '拍照', key: 0 },
            { text: '从相册选择', key: 1 },
          ]}
          onClose={() => setVisible(false)}
          onAction={handleAction}
        />
      </div>
    </Page>
  );
}
