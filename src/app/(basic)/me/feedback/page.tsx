'use client';

import { useRouter } from 'next/navigation';
import { useState, useTransition } from 'react';
import { Text<PERSON><PERSON>, ImageUploader, ActionSheet, SpinLoading, Toast } from 'antd-mobile';
import { AddOutline, CloseCircleFill } from 'antd-mobile-icons';
import type { Action } from 'antd-mobile/es/components/action-sheet';
import { ImageUploadItem } from 'antd-mobile/es/components/image-uploader';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { toImg } from '@/utils';
import { addFeedback } from '@/server/actions/me';

async function mockUpload(file: File) {
  await new Promise((resolve) => setTimeout(resolve, 1000));
  return {
    url: URL.createObjectURL(file),
  };
}

const items = [
  {
    key: 0,
    name: '服务',
  },
  {
    key: 2,
    name: '产品',
  },
  {
    key: 1,
    name: 'APP',
  },
  {
    key: 3,
    name: '其他',
  },
];

export default function Feedback({ searchParams }: { searchParams: { from: string | undefined } }) {
  const router = useRouter();
  const [activeKey, setActiveKey] = useState<number>(0);
  const [content, setContent] = useState<string>('');
  const [visible, setVisible] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState<ImageUploadItem[]>([]);
  const [isPending, startTransition] = useTransition();
  const { from } = searchParams;

  const handleUpload = (action: Action) => {
    setVisible(false);
    setUploading(true);
    window.jsBridgeHelper
      ?.sendMessage('chooseImage', { type: action.key })
      .then((res: string | null) => {
        setUploading(false);
        if (res) {
          setFileList((fileList) => [...fileList, { url: toImg(res), extra: res }]);
        }
      });
  };

  const handleBack = () => {
    if (from === 'handbook') {
      window.jsBridgeHelper?.sendMessage('webBrowse', {
        navBar: true,
        url: `${process.env.NEXT_PUBLIC_PHP_URL}/home/<USER>/index`,
      });
    }
    router.back();
  };

  // 提交意见反馈
  const handleSumbit = () => {
    if (!content) {
      Toast.show('请填写您的问题或建议');
      return;
    }
    startTransition(async () => {
      const result = await addFeedback({
        type: activeKey,
        content: content,
        img: fileList.map((item) => item.extra).join(','),
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        Toast.show('提交成功');
        handleBack();
      }
    });
  };

  return (
    <Page title="建议与反馈" onBack={handleBack}>
      <div className="px-3 py-4">
        <div className="py-2 text-sm text-color-weak">请选择您要反馈的问题</div>

        <div className="mt-3 grid grid-cols-2 gap-2">
          {items.map((item) => (
            <div
              key={item.key}
              className={`flex h-14 items-center justify-center rounded-xl bg-white ${
                activeKey === item.key ? 'text-primary' : ''
              }`}
              onClick={() => setActiveKey(item.key)}
            >
              {item.name}
            </div>
          ))}
        </div>
        <div className="mt-4 rounded-xl bg-white px-4 py-3">
          <div className="mb-2 text-sm">备注</div>
          <TextArea
            placeholder="请填写您的问题或建议"
            rows={2}
            showCount
            maxLength={100}
            value={content}
            onChange={(value) => {
              setContent(value);
            }}
          />
        </div>
        <div className="mt-4 rounded-xl bg-white px-4 py-3">
          <div className="mb-2 text-sm">上传图片（{fileList.length}/3）</div>
          <ImageUploader
            value={fileList}
            onChange={setFileList}
            multiple
            maxCount={3}
            upload={mockUpload}
            style={{ '--gap-horizontal': '16px' }}
            disableUpload
            deletable={false}
            renderItem={(originNode, file) => (
              <div className="relative">
                {originNode}
                <span
                  className="absolute right-0 top-0 z-10 -translate-y-1/3 translate-x-1/3 text-color-weak"
                  onClick={() => {
                    setFileList(fileList.filter((item) => item.url !== file.url));
                  }}
                >
                  <CloseCircleFill fontSize={20} />
                </span>
              </div>
            )}
          >
            <span
              className="relative flex h-20 w-20 items-center justify-center rounded bg-color-box"
              onClick={() => {
                setVisible(true);
              }}
            >
              {uploading ? (
                <span className="flex flex-col items-center text-color-weak">
                  <SpinLoading style={{ '--size': '24px' }} />
                  <span className="mt-2 text-3xs">上传中...</span>
                </span>
              ) : (
                <span className="text-color-weak">
                  <AddOutline fontSize={32} />
                </span>
              )}
            </span>
          </ImageUploader>
        </div>
        <ActionSheet
          cancelText="取消"
          visible={visible}
          actions={[
            { text: '拍摄', key: 0 },
            {
              text: '从相册选择',
              key: 1,
            },
          ]}
          onClose={() => setVisible(false)}
          onAction={handleUpload}
        />
        <Button block type="primary" className="mt-6" onClick={handleSumbit} loading={isPending}>
          确定
        </Button>
      </div>
    </Page>
  );
}
