'use client';

import React, { useEffect, useState, useTransition } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Tabs, Toast, Image, ActionSheet } from 'antd-mobile';
import type { Action } from 'antd-mobile/es/components/action-sheet';
import Page from '@/components/Page';
import { timestampToDate, toPhpImg } from '@/utils';
import { getNewsCollect, getStoreCollect } from '@/server/actions/me';
import { useNative } from '@/lib/native';

export default function Message({ searchParams }: { searchParams: { [key: string]: string } }) {
  const router = useRouter();
  const pathname = usePathname();
  const [isPending, startTransition] = useTransition();
  const activeKey = searchParams.tab || '1';
  const [list, setList] = useState([]);

  const [visible, setVisible] = useState(false);
  const [currentItem, setCurrentItem] = useState<any>();
  const { safeArea } = useNative();

  useEffect(() => {
    if (activeKey === '1') {
      setList([]);
      window.jsBridgeHelper?.sendMessage('queryLocation').then((res: any) => {
        if (res.code === '200') {
          startTransition(async () => {
            const data: any = await getStoreCollect(res.data.longitude, res.data.latitude);
            console.log('[ data ] >', data);
            if (data?.error) {
              Toast.show(data.error);
              return;
            }
            setList(data || []);
          });
        }
      });
    } else {
      startTransition(async () => {
        setList([]);
        const data: any = await getNewsCollect();
        if (data?.error) {
          Toast.show(data.error);
          return;
        }
        setList(data || []);
      });
    }
  }, [activeKey]);

  const tabItems = [
    { key: '1', name: '网点' },
    { key: '2', name: '资讯' },
  ];

  // 拨号
  const handlePhoneCall = (e: any, phone: string) => {
    e.stopPropagation();
    window.jsBridgeHelper?.sendMessage('phoneCall', phone);
  };
  // 导航
  const handleAction = (action: Action) => {
    setVisible(false);
    window.jsBridgeHelper?.sendMessage(action.key, {
      lat: currentItem.storeGpsLat,
      lon: currentItem.storeGpsLng,
    });
  };

  const onChange = (key: string) => {
    const updatedSearchParams = new URLSearchParams(searchParams);
    updatedSearchParams.set('tab', key);
    router.push(`${pathname}?${updatedSearchParams.toString()}`);
  };

  return (
    <Page title="我的收藏" navBgColor="#fff" onBack={() => router.push('/me')}>
      <div className="relative">
        <div className="sticky z-10 bg-white" style={{ top: 44 + safeArea.top }}>
          <Tabs activeKey={activeKey} onChange={onChange}>
            {tabItems.map((_: any, index: any) => (
              <Tabs.Tab title={_.name} key={_.key} />
            ))}
          </Tabs>
        </div>
        <div className="space-y-3 px-3 py-3">
          {activeKey === '1' &&
            (list || []).map((item: any) => (
              <div
                className="flex rounded-xl bg-white p-3"
                key={item.id}
                onClick={() => router.push(`/vehicle/1/store/${item.id}`)}
              >
                <div className="mr-3">
                  <Image src="/images/<EMAIL>" alt="门店" width={80} height={80} />
                </div>
                <div className="flex-1">
                  <div className="h-20 space-y-1 border-b border-[#eee] pt-1">
                    <div className="text-sm font-medium">{item.storeName}</div>
                    <div className="text-3xs text-color-weak">营业时间：{item.businessTime}</div>
                    <div className="flex w-full items-center">
                      <span className="line-clamp-1 flex-1 text-3xs text-color-weak">
                        {item.province}
                        {item.city}
                        {item.area}
                        {item.address}
                      </span>
                      <span className="text-2xs font-medium">{item.distance} km</span>
                    </div>
                  </div>
                  <div className="mt-2 grid h-8 grid-cols-2 gap-3">
                    <div
                      className="flex items-center justify-center rounded bg-color-background"
                      onClick={(e) => {
                        e.stopPropagation();
                        setVisible(true);
                        setCurrentItem(item);
                      }}
                    >
                      <Image alt="导航" src="/images/<EMAIL>" width={24} height={24} />
                      <span className="ml-1.5 text-sm">导航</span>
                    </div>
                    <div
                      className="flex items-center justify-center rounded bg-color-background"
                      onClick={(e) => handlePhoneCall(e, item.storeTelephone)}
                    >
                      <Image
                        alt="电话"
                        src="/images/<EMAIL>"
                        width={24}
                        height={24}
                      />
                      <span className="ml-1.5 text-sm">电话</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}

          {activeKey === '2' &&
            list.map((_: any) => (
              <div
                key={_.id}
                className=" mb-6  w-full overflow-hidden rounded-2xl bg-white"
                onClick={() => router.push(`/news/${_.id}`)}
              >
                <Image
                  src={toPhpImg(_.image)}
                  alt="img"
                  style={{ width: '100%', height: 'auto' }}
                />
                <div className="p-4">
                  <div className="line-clamp-1 text-2xl">{_.title}</div>
                  <div className="mb-2 mt-2 line-clamp-2 text-xs text-color-weak">
                    {_.introduce}{' '}
                  </div>
                  <div className="text-right text-2xs text-[#C9CDD3]">
                    {timestampToDate(_.gmtRelease)}
                  </div>
                </div>
              </div>
            ))}
          {list.length === 0 && (
            <div className="w-full pt-20 text-center">
              <div className="h-[300px] w-full  bg-[url('/images/empty-info.png')] bg-contain bg-center bg-no-repeat " />
              <span className="text-xl text-color-weak">暂无收藏</span>
            </div>
          )}
          <ActionSheet
            cancelText="取消"
            visible={visible}
            actions={[
              { text: '腾讯地图', key: 'txLauncher' },
              {
                text: '百度地图',
                key: 'bdLauncher',
              },
              {
                text: '高德地图',
                key: 'amapLauncher',
              },
            ]}
            onClose={() => setVisible(false)}
            onAction={handleAction}
          />
        </div>
      </div>
    </Page>
  );
}
