'use client';

import React, { useEffect, useState, useTransition } from 'react';
import {
  Tabs,
  Badge,
  Tag,
  Input,
  Divider,
  List,
  SwipeAction,
  Dialog,
  Image,
  Toast,
} from 'antd-mobile';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { PHONEREGEX, toImg } from '@/utils';
import { getShareList, shareAdd, shareDel } from '@/server/actions/share';

export default function Share({ params }: { params: { id: string } }) {
  const [isPending, startTransition] = useTransition();
  const [add, setAdd] = useState(false);
  const [list, setList] = useState([]);
  const [phone, setPhone] = useState('');
  const { id } = params;

  useEffect(() => toList(), [id]);

  const toList = () => {
    startTransition(async () => {
      const data: any = await getShareList(id);
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      setList(data.list);
      console.log('[ data ] >', data);
    });
  };

  const toAdd = () => {
    startTransition(async () => {
      const data: any = await shareAdd({ deviceNo: id, phone });
      console.log('[ data ] >', data);
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      setAdd(false);
      setPhone('');
      toList();
    });
  };

  const toDel = (subUserId: any) => {
    startTransition(async () => {
      const data: any = await shareDel({ deviceNo: id, subUserId });
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      toList();
    });
  };
  const titleNum: string = list.length > 0 ? ` (${list.length}/5)` : '';
  return add ? (
    <Page title="添加账号">
      <div className="space-y-3 px-3 py-6">
        <div className="mb-4 mt-10 rounded-lg bg-white px-4 py-3">
          <Input
            placeholder="请输入手机号"
            inputMode="numeric"
            maxLength={11}
            value={phone}
            onChange={(value) => setPhone(value)}
          />
        </div>
        <Button
          data-track="共享-添加账号-确定"
          block
          type="primary"
          onClick={toAdd}
          disabled={!PHONEREGEX.test(phone)}
        >
          确定
        </Button>
      </div>
    </Page>
  ) : (
    <Page title={'分享账号管理' + titleNum} style={{ background: '#fff' }}>
      {list.length === 0 && (
        <div className="flex  flex-col items-center px-12">
          <div className="h-[400px] w-full  bg-[url('/images/empty-content.png')] bg-contain bg-center bg-no-repeat " />

          <div className="mb-5 text-[#908F94]">未添加账号</div>
          <Button block type="primary" onClick={() => setAdd(true)}>
            立即添加
          </Button>
        </div>
      )}

      {list.length > 0 && (
        <div className="space-y-3 px-3 py-6">
          <List>
            {list.map((_: any) => (
              <SwipeAction
                key={_.subUserId}
                rightActions={[{ key: 'delete', text: '删除', color: 'danger' }]}
                onAction={() => toDel(_.subUserId)}
              >
                <List.Item
                  prefix={
                    <Image
                      alt="img"
                      src={
                        _.logo && _.logo !== 'null' ? _.logo : '/images/G/<EMAIL>'
                      }
                      style={{ borderRadius: 25 }}
                      fit="cover"
                      width={50}
                      height={50}
                    />
                  }
                  style={{
                    marginBottom: 15,
                    background:
                      'linear-gradient(180deg, #EEF1F4 0%, rgba(238, 241, 244, 0.00) 117.86%)',
                  }}
                  extra={
                    !_.state && (
                      <div
                        className="text-2xs text-[#2FB8FF]"
                        style={{
                          padding: '4px 8px',
                          borderRadius: '8px',
                          background: 'rgba(185, 214, 255, 0.20)',
                        }}
                      >
                        邀请中
                      </div>
                    )
                  }
                  description={_.phone}
                >
                  {_.nickName}
                </List.Item>
              </SwipeAction>
            ))}
          </List>
          {list.length < 5 && (
            <Button block type="primary" onClick={() => setAdd(true)}>
              + 添加账号
            </Button>
          )}
        </div>
      )}
    </Page>
  );
}
