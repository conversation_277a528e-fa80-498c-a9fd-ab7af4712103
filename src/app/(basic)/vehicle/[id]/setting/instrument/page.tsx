'use client';

import { useMemo } from 'react';
import Image from 'next/image';
import { Slider, Toast } from 'antd-mobile';
import Page from '@/components/Page';
import List from '@/components/List';
import type { SliderProps } from 'antd-mobile';
import * as AspectRatio from '@radix-ui/react-aspect-ratio';
import { setVcuVehicleSetting } from '@/server/actions/vehicle';
import { getVehicleSetting } from '@/server/api/vehicle';
import { useQuery, useQueryClient } from '@tanstack/react-query';

const marks = () => {
  const marks: SliderProps['marks'] = {};
  for (let i = 0; i <= 6; i += 1) {
    if (i !== 0 && i !== 6) {
      marks[i] = '';
    } else {
      marks[i] = i;
    }
  }
  return marks;
};

const ybEnums = [
  {
    icon: '/images/C-7/icon-yb-white.svg',
    image: '/images/C-7/lima_yb_pic_white.png',
    key: 0,
  },
  {
    icon: '/images/C-7/icon-yb-blue.svg',
    image: '/images/C-7/lima_yb_pic_blue.png',
    key: 3,
  },
  {
    icon: '/images/C-7/icon-yb-green.svg',
    image: '/images/C-7/lima_yb_pic_green.png',
    key: 2,
  },
  {
    icon: '/images/C-7/icon-yb-red.svg',
    image: '/images/C-7/lima_yb_pic_red.png',
    key: 1,
  },
];

export default function Instrument({ params }: { params: { id: string } }) {
  const queryClient = useQueryClient();
  const { data: vehicleSetting } = useQuery({
    queryKey: ['vehicleSetting', params.id],
    queryFn: () => getVehicleSetting<Record<string, any>>(params.id),
  });

  const checkedImage = useMemo(() => {
    const index = ybEnums.findIndex((item) => item.key === vehicleSetting?.bumpDvcSet.meterColor);
    return ybEnums[index > -1 ? index : 0].image;
  }, [vehicleSetting]);

  const handleSubmit = async (key: string, value: any) => {
    queryClient.setQueryData(['vehicleSetting', params.id], {
      ...vehicleSetting,
      bumpDvcSet: {
        ...vehicleSetting?.bumpDvcSet,
        [key]: value,
      },
    });

    const result = await setVcuVehicleSetting({
      commandType: 1,
      vehicleSetType: 4,
      deviceNo: params.id,
      meterColor: vehicleSetting?.bumpDvcSet.meterColor,
      meterBrightness: vehicleSetting?.bumpDvcSet.meterBrightness,
      [key]: value,
    });
    if (result?.error) {
      Toast.show(result.error);
      queryClient.setQueryData(['vehicleSetting', params.id], vehicleSetting);
    }
  };

  return (
    <Page title="仪表设置">
      <div className="space-y-3 px-3 py-6">
        <div className="relative rounded-2xl bg-white p-4">
          <div className="relative w-full">
            <AspectRatio.Root ratio={638 / 604}>
              <Image src={checkedImage || ''} alt="仪表盘" fill priority />
            </AspectRatio.Root>
          </div>
          <div className="absolute bottom-0 left-14 right-14 flex h-16 items-center justify-center space-x-1 rounded-t-2xl bg-white">
            {ybEnums.map((item) => (
              <span
                key={item.key}
                className="relative inline-flex h-8 w-8 items-center justify-center"
                onClick={() => {
                  handleSubmit('meterColor', item.key);
                }}
              >
                <Image alt="图标" src={item.icon} fill />
                {item.key === vehicleSetting?.bumpDvcSet.meterColor && (
                  <Image
                    alt="checked"
                    src="/images/C-7/icon-checked.svg"
                    width={16}
                    height={16}
                    className="absolute left-1/2 top-1/2 mt-[1px] -translate-x-1/2 -translate-y-1/2"
                  />
                )}
              </span>
            ))}
          </div>
        </div>
        <List
          items={[
            {
              title: '背光设置',
              value: (
                <div className="text-3xs text-color-weak">
                  当前亮度{vehicleSetting?.bumpDvcSet.meterBrightness}
                </div>
              ),
              footer: (
                <div className="px-6">
                  <Slider
                    step={1}
                    max={6}
                    marks={marks()}
                    value={vehicleSetting?.bumpDvcSet.meterBrightness}
                    onAfterChange={(value) => {
                      handleSubmit('meterBrightness', value);
                    }}
                  />
                </div>
              ),
            },
          ]}
        />
      </div>
    </Page>
  );
}
