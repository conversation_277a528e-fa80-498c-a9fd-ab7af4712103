'use client';

import { <PERSON><PERSON>, <PERSON>lide<PERSON>, Toast } from 'antd-mobile';
import type { SliderProps } from 'antd-mobile';
import Page from '@/components/Page';
import List from '@/components/List';
import { setVehicleSetting, setVcuVehicleSetting } from '@/server/actions/vehicle';
import { getVehicleDetail, getVehicleSetting } from '@/server/api/vehicle';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { state } from '@/store';
import { useBLEClient } from '@/lib/ble';
import { postBluetoothData } from '@/server/api/home';

const marks = () => {
  const marks: SliderProps['marks'] = {};
  for (let i = 0; i <= 100; i += 1) {
    if (i !== 0 && i !== 100) {
      marks[i] = '';
    } else {
      marks[i] = i;
    }
  }
  return marks;
};

export default function Soundeffects({ params }: { params: { id: string } }) {
  const queryClient = useQueryClient();
  const bleClient = useBLEClient();

  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id),
    enabled: !!params.id,
  });
  const isVcu = detail?.equiInfos.some((item) => item.equiNo.startsWith('V'));

  const { data: vehicleSetting } = useQuery({
    queryKey: ['vehicleSetting', params.id],
    queryFn: () => getVehicleSetting<Record<string, any>>(params.id),
  });

  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3);

  const handleSubmit = async (key: string, value: any) => {
    console.log(key, value);
    if (bleClient.isConnected() && !state.hasBleLimit) {
      bleClient.setVehicleSound(
        {
          soundProtect: vehicleSetting?.bumpDvcSet.soundProtect,
          soundUnprotect: vehicleSetting?.bumpDvcSet.soundUnprotect,
          soundUnlock: vehicleSetting?.bumpDvcSet.soundUnlock,
          soundLock: vehicleSetting?.bumpDvcSet.soundLock,
          soundTurnLight: vehicleSetting?.bumpDvcSet.soundTurnLight,
          soundWarn: vehicleSetting?.bumpDvcSet.soundWarn,
          soundAlarm: vehicleSetting?.bumpDvcSet.soundAlarm,
          soundAlarmVolume: vehicleSetting?.bumpDvcSet.soundAlarmVolume,
          soundWarnVolume: vehicleSetting?.bumpDvcSet.soundWarnVolume,
          [key]: value,
        },
        (result: number[]) => {
          queryClient.setQueryData(['vehicleSetting', params.id], {
            ...vehicleSetting,
            bumpDvcSet: {
              ...vehicleSetting?.bumpDvcSet,
              soundProtect: result[0],
              soundUnprotect: result[1],
              soundUnlock: result[2],
              soundLock: result[3],
              soundTurnLight: result[4],
              soundWarn: result[5],
              soundAlarm: result[6],
              soundAlarmVolume: result[7],
              soundWarnVolume: result[8],
            },
          });
          postBluetoothData({
            deviceNo: params.id,
            blueName: bleClient.deviceInfo().deviceName,
            blueAddress: bleClient.deviceInfo().deviceMac,
            vcuNo: bleClient.deviceInfo().deviceSN,
            type: 19,
            soundProtect: result[0],
            soundUnprotect: result[1],
            soundUnlock: result[2],
            soundLock: result[3],
            soundTurnLight: result[4],
            soundWarn: result[5],
            soundAlarm: result[6],
            soundAlarmVolume: result[7],
            soundWarnVolume: result[8],
          });
        }
      );
      return;
    } else if (!deviceNetWorkModule) {
      Toast.show('请先连接蓝牙');
      return;
    }

    queryClient.setQueryData(['vehicleSetting', params.id], {
      ...vehicleSetting,
      bumpDvcSet: {
        ...vehicleSetting?.bumpDvcSet,
        [key]: value,
      },
    });

    const result = isVcu
      ? await setVcuVehicleSetting({
          commandType: 1,
          vehicleSetType: 6,
          deviceNo: params.id,
          soundSceneMode: 1,
          soundUnlock: vehicleSetting?.bumpDvcSet.soundUnlock,
          soundLock: vehicleSetting?.bumpDvcSet.soundLock,
          soundAlarm: vehicleSetting?.bumpDvcSet.soundAlarm,
          soundTurnLight: vehicleSetting?.bumpDvcSet.soundTurnLight,
          soundAlarmVolume: vehicleSetting?.bumpDvcSet.soundAlarmVolume,
          soundWarnVolume: vehicleSetting?.bumpDvcSet.soundWarnVolume,
          [key]: value,
        })
      : await setVehicleSetting({
          commandType: 1,
          vehicleSetType: 6,
          deviceNo: params.id,
          soundUnlock: vehicleSetting?.bumpDvcSet.soundUnlock,
          soundLock: vehicleSetting?.bumpDvcSet.soundLock,
          soundProtect: vehicleSetting?.bumpDvcSet.soundProtect,
          soundUnprotect: vehicleSetting?.bumpDvcSet.soundUnprotect,
          soundAlarm: vehicleSetting?.bumpDvcSet.soundAlarm,
          soundTurnLight: vehicleSetting?.bumpDvcSet.soundTurnLight,
          soundWarn: vehicleSetting?.bumpDvcSet.soundWarn,
          soundAlarmVolume: vehicleSetting?.bumpDvcSet.soundAlarmVolume,
          soundWarnVolume: vehicleSetting?.bumpDvcSet.soundWarnVolume,
          [key]: value,
        });
    if (result?.error) {
      Toast.show(result.error);
      queryClient.setQueryData(['vehicleSetting', params.id], vehicleSetting);
    }
  };

  return (
    <Page title="音效设置">
      <div className="space-y-3 px-3 py-6">
        <List
          items={[
            {
              title: '开电门',
              onClick: (_, valueChildRef) => {
                valueChildRef.current?.open();
              },
              value: (
                <Picker
                  columns={[
                    [
                      { label: 'LimaStarting Up', value: 1 },
                      { label: 'LimaSUX', value: 2 },
                      { label: 'LimaSUR', value: 3 },
                      { label: 'Close', value: 0 },
                    ],
                  ]}
                  cancelText="取消"
                  value={[vehicleSetting?.bumpDvcSet.soundUnlock]}
                  onConfirm={(value) => {
                    handleSubmit('soundUnlock', value[0]);
                  }}
                >
                  {(_) => {
                    return <span>{_[0]?.label}</span>;
                  }}
                </Picker>
              ),
              arrow: true,
              show: detail?.scheme.includes('turn_on_switch'),
            },
            {
              title: '关电门',
              onClick: (_, valueChildRef) => {
                valueChildRef.current?.open();
              },
              value: (
                <Picker
                  columns={[
                    [
                      { label: 'LimaShut Down', value: 1 },
                      { label: 'LimaSDX', value: 2 },
                      { label: 'LimaSDR', value: 3 },
                      { label: 'Close', value: 0 },
                    ],
                  ]}
                  cancelText="取消"
                  value={[vehicleSetting?.bumpDvcSet.soundLock]}
                  onConfirm={(value) => {
                    handleSubmit('soundLock', value[0]);
                  }}
                >
                  {(_) => {
                    return <span>{_[0]?.label}</span>;
                  }}
                </Picker>
              ),
              arrow: true,
              show: detail?.scheme.includes('turn_off_switch'),
            },
            {
              title: '设防',
              onClick: (_, valueChildRef) => {
                valueChildRef.current?.open();
              },
              value: (
                <Picker
                  columns={[
                    [
                      { label: 'LimaLockSound', value: 1 },
                      { label: 'LimaLSX', value: 2 },
                      { label: 'LimaLSR', value: 3 },
                      { label: 'Close', value: 0 },
                    ],
                  ]}
                  cancelText="取消"
                  value={[vehicleSetting?.bumpDvcSet.soundProtect]}
                  onConfirm={(value) => {
                    handleSubmit('soundProtect', value[0]);
                  }}
                >
                  {(_) => {
                    return <span>{_[0]?.label}</span>;
                  }}
                </Picker>
              ),
              arrow: true,
              show: detail?.scheme.includes('sound_protect'),
            },
            {
              title: '撤防',
              onClick: (_, valueChildRef) => {
                valueChildRef.current?.open();
              },
              value: (
                <Picker
                  columns={[
                    [
                      { label: 'LimaUnLockSound', value: 1 },
                      { label: 'LimaUSX', value: 2 },
                      { label: 'LimaUSR', value: 3 },
                      { label: 'Close', value: 0 },
                    ],
                  ]}
                  cancelText="取消"
                  value={[vehicleSetting?.bumpDvcSet.soundUnprotect]}
                  onConfirm={(value) => {
                    handleSubmit('soundUnprotect', value[0]);
                  }}
                >
                  {(_) => {
                    return <span>{_[0]?.label}</span>;
                  }}
                </Picker>
              ),
              arrow: true,
              show: detail?.scheme.includes('sound_unprotect'),
            },
            {
              title: '车辆报警',
              onClick: (_, valueChildRef) => {
                valueChildRef.current?.open();
              },
              value: (
                <Picker
                  columns={[
                    [
                      { label: 'LimaAlarmTone', value: 1 },
                      { label: 'LimaATX', value: 2 },
                      { label: 'LimaATR', value: 3 },
                      { label: 'Close', value: 0 },
                    ],
                  ]}
                  cancelText="取消"
                  value={[vehicleSetting?.bumpDvcSet.soundAlarm]}
                  onConfirm={(value) => {
                    handleSubmit('soundAlarm', value[0]);
                  }}
                >
                  {(_) => {
                    return <span>{_[0]?.label}</span>;
                  }}
                </Picker>
              ),
              arrow: true,
              show: detail?.scheme.includes('vehicle_alarm'),
            },
            {
              title: '转向灯',
              onClick: (_, valueChildRef) => {
                valueChildRef.current?.open();
              },
              value: (
                <Picker
                  columns={[
                    [
                      { label: 'LimaTurnTone', value: 1 },
                      { label: 'LimaTTX', value: 2 },
                      { label: 'LimaTTR', value: 3 },
                      { label: 'Close', value: 0 },
                    ],
                  ]}
                  cancelText="取消"
                  value={[vehicleSetting?.bumpDvcSet.soundTurnLight]}
                  onConfirm={(value) => {
                    handleSubmit('soundTurnLight', value[0]);
                  }}
                >
                  {(_) => {
                    return <span>{_[0]?.label}</span>;
                  }}
                </Picker>
              ),
              arrow: true,
              show: detail?.scheme.includes('turn_light'),
            },
            {
              title: '提示音',
              onClick: (_, valueChildRef) => {
                valueChildRef.current?.open();
              },
              value: (
                <Picker
                  columns={[
                    [
                      { label: 'LimaWarningTone', value: 1 },
                      { label: 'LimaWTX', value: 2 },
                      { label: 'LimaWTR', value: 3 },
                      { label: 'Close', value: 0 },
                    ],
                  ]}
                  cancelText="取消"
                  value={[vehicleSetting?.bumpDvcSet.soundWarn]}
                  onConfirm={(value) => {
                    handleSubmit('soundWarn', value[0]);
                  }}
                >
                  {(_) => {
                    return <span>{_[0]?.label}</span>;
                  }}
                </Picker>
              ),
              arrow: true,
              show: detail?.scheme.includes('sound_warn'),
            },
          ]}
        />
        <List
          items={[
            {
              title: '报警音量',
              value: (
                <div className="text-3xs text-color-weak">
                  当前音量{vehicleSetting?.bumpDvcSet.soundAlarmVolume}
                </div>
              ),
              footer: (
                <Slider
                  marks={marks()}
                  value={vehicleSetting?.bumpDvcSet.soundAlarmVolume}
                  onAfterChange={(value) => {
                    handleSubmit('soundAlarmVolume', value);
                  }}
                />
              ),
              show: detail?.scheme.includes('alarm_volume'),
            },
          ]}
        />
        <List
          items={[
            {
              title: '提示音量',
              value: (
                <div className="text-3xs text-color-weak">
                  当前音量{vehicleSetting?.bumpDvcSet.soundWarnVolume}
                </div>
              ),
              footer: (
                <Slider
                  marks={marks()}
                  value={vehicleSetting?.bumpDvcSet.soundWarnVolume}
                  onAfterChange={(value) => {
                    handleSubmit('soundWarnVolume', value);
                  }}
                />
              ),
              show: detail?.scheme.includes('tip_volume'),
            },
          ]}
        />
      </div>
    </Page>
  );
}
