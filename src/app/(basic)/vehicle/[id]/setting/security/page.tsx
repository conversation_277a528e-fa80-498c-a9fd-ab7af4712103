'use client';

import { useTransition } from 'react';
import { Toast } from 'antd-mobile';
import Page from '@/components/Page';
import List from '@/components/List';
import Switch from '@/components/Switch';
import Slider from '@/components/Slider';
import { setVehicleSetting, setVcuVehicleSetting } from '@/server/actions/vehicle';
import { postBluetoothData } from '@/server/actions/home';
import { getVehicleDetail, getVehicleSetting } from '@/server/api/vehicle';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useNative } from '@/lib/native';
import { useBLEClient } from '@/lib/ble';

export default function Security({ params }: { params: { id: string } }) {
  const bleClient = useBLEClient();
  const [isPending, startTransition] = useTransition();
  const { clientId } = useNative();

  const queryClient = useQueryClient();

  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id),
    enabled: !!params.id,
  });
  const isVcu = detail?.equiInfos.some((item) => item.equiNo.startsWith('V'));

  const { data: vehicleSetting } = useQuery({
    queryKey: ['vehicleSetting', params.id],
    queryFn: () => getVehicleSetting<Record<string, any>>(params.id),
  });

  // 报警音开发、报警灵敏度
  const handleSubmit = async (key: string, value: any) => {
    queryClient.setQueryData(['vehicleSetting', params.id], {
      ...vehicleSetting,
      bumpDvcSet: {
        ...vehicleSetting?.bumpDvcSet,
        [key]: value,
      },
    });
    if (bleClient.isConnected()) {
      if (key === 'securityAlarmSensitivityState') {
        bleClient.setAlarmVoiceSwitch(value);
      } else {
        bleClient.setAlarmVoice(value);
      }
      // 上报数据
      await postBluetoothData({
        deviceNo: params.id,
        blueName: bleClient.deviceInfo().deviceName,
        blueAddress: bleClient.deviceInfo().deviceMac,
        vcuNo: bleClient.deviceInfo().deviceSN,
        type: 1,
        securityAlarmSensitivityState: vehicleSetting?.bumpDvcSet.securityAlarmSensitivityState,
        securityAlarmSensitivity: vehicleSetting?.bumpDvcSet.securityAlarmSensitivity,
        [key]: value,
      });
    } else if (detail?.communicateTypes.includes(2) || detail?.communicateTypes.includes(3)) {
      startTransition(async () => {
        const result = isVcu
          ? await setVcuVehicleSetting({
              commandType: 1,
              vehicleSetType: 5,
              deviceNo: params.id,
              securityAlarmSensitivity: vehicleSetting?.bumpDvcSet.securityAlarmSensitivity,
              [key]: value,
            })
          : await setVehicleSetting({
              commandType: 1,
              vehicleSetType: 5,
              deviceNo: params.id,
              securityAlarmSensitivityState:
                vehicleSetting?.bumpDvcSet.securityAlarmSensitivityState,
              securityAlarmSensitivity: vehicleSetting?.bumpDvcSet.securityAlarmSensitivity,
              [key]: value,
            });
        if (result?.error) {
          Toast.show(result.error);
          queryClient.setQueryData(['vehicleSetting', params.id], vehicleSetting);
        }
      });
    } else {
      Toast.show('请先连接蓝牙');
    }
  };

  return (
    <Page title="安防设置">
      <div className="space-y-3 px-3 py-6">
        <List
          items={[
            {
              key: 1,
              title: '报警音开关',
              value: (
                <Switch
                  checked={vehicleSetting?.bumpDvcSet.securityAlarmSensitivityState === 1}
                  onChange={(checked) =>
                    handleSubmit('securityAlarmSensitivityState', checked ? 1 : 0)
                  }
                />
              ),
              show: detail?.scheme.includes('alarm_voice_state'),
            },
            {
              key: 2,
              title: '报警灵敏度',
              footer: (
                <div className="px-6">
                  <Slider
                    max={2}
                    marks={[
                      {
                        value: 0,
                        label: '免打扰',
                      },
                      {
                        value: 1,
                        label: '标准',
                      },
                      {
                        value: 2,
                        label: '警戒',
                      },
                    ]}
                    defaultValue={[vehicleSetting?.bumpDvcSet.securityAlarmSensitivity]}
                    onValueCommit={(value) => handleSubmit('securityAlarmSensitivity', value[0])}
                  />
                </div>
              ),
              show:
                detail?.scheme.includes('alarm_sensitivity') &&
                (vehicleSetting?.bumpDvcSet.securityAlarmSensitivityState === 1 || isVcu),
            },
          ]}
        />
        <List
          items={[
            {
              title: 'SOS紧急呼救',
              description: '系统检测到车辆遭遇严重碰撞时，自动联系紧急联系人',
              href: 'security/sos',
              show: detail?.scheme.includes('sos'),
            },
            {
              title: '丢失模式',
              description: '若车辆或电池被盗，报失后车辆功能将被限制',
              href: `security/lost?state=${vehicleSetting?.bumpDvcSet.lose}`,
              show: detail?.scheme.includes('lose') && detail?.isOwner === 1,
            },
          ]}
        />
      </div>
    </Page>
  );
}
