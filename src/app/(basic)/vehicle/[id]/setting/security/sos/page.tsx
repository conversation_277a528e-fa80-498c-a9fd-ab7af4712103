'use client';

import { useTransition } from 'react';
import { Toast } from 'antd-mobile';
import Page from '@/components/Page';
import List from '@/components/List';
import Switch from '@/components/Switch';
import { setVehicleSetting } from '@/server/actions/vehicle';
import { getVehicleSetting } from '@/server/api/vehicle';
import { useQuery, useQueryClient } from '@tanstack/react-query';

export default function SOS({ params }: { params: { id: string } }) {
  const [isPending, startTransition] = useTransition();

  const queryClient = useQueryClient();
  const { data: vehicleSetting } = useQuery({
    queryKey: ['vehicleSetting', params.id],
    queryFn: () => getVehicleSetting<Record<string, any>>(params.id),
  });

  const handleSubmit = (checked: boolean) => {
    if (checked && !vehicleSetting?.bumpDvcSet.sosPhone) {
      Toast.show('未设置紧急联系人');
      return;
    }
    startTransition(async () => {
      queryClient.setQueryData(['vehicleSetting', params.id], {
        ...vehicleSetting,
        bumpDvcSet: {
          ...vehicleSetting?.bumpDvcSet,
          sosState: checked ? 1 : 0,
        },
      });
      const result = await setVehicleSetting({
        commandType: 0,
        deviceNo: params.id,
        sosState: checked ? 1 : 0,
      });
      if (result?.error) {
        Toast.show(result.error);
        queryClient.setQueryData(['vehicleSetting', params.id], vehicleSetting);
      }
    });
  };

  return (
    <Page title="SOS紧急呼救">
      <div className="space-y-3 px-3 py-6">
        <List
          items={[
            {
              title: 'SOS紧急呼救',
              description: '系统检测到车辆遭遇严重碰撞时，自动联系紧急联系人',
              value: (
                <Switch
                  checked={vehicleSetting?.bumpDvcSet.sosState === 1}
                  onChange={handleSubmit}
                />
              ),
            },
          ]}
        />
        <List
          items={[
            {
              title: '紧急联系人',
              value: !!vehicleSetting?.bumpDvcSet.sosPhone ? '已设置' : '未设置',
              href: 'contact',
            },
          ]}
        />
      </div>
    </Page>
  );
}
