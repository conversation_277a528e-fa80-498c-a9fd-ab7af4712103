'use client';

import { useTransition } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import { Toast } from 'antd-mobile';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { sendCommand } from '@/server/actions/home';
import { getDeviceInfo } from '@/server/api/home';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { toImg } from '@/utils';
import * as AspectRatio from '@radix-ui/react-aspect-ratio';
import dayjs from 'dayjs';

export default function Lost({ params }: { params: { id: string } }) {
  const [isPending, startTransition] = useTransition();
  const searchParams = useSearchParams();
  const router = useRouter();
  const queryClient = useQueryClient();

  const { data } = useQuery({ queryKey: ['device-info'], queryFn: () => getDeviceInfo(params.id) });

  const handleLost = () => {
    startTransition(async () => {
      const lose = searchParams.get('state') === '1' ? 0 : 1;
      const previousData: any = queryClient.getQueryData(['vehicleSetting', params.id]);
      queryClient.setQueryData(['vehicleSetting', params.id], {
        ...previousData,
        bumpDvcSet: {
          ...previousData?.bumpDvcSet,
          lose,
        },
      });
      const result = await sendCommand({
        deviceNo: params.id,
        type: 5,
        status: lose,
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        Toast.show('操作成功');
        router.back();
      }
    });
  };

  return (
    <Page title="丢失模式">
      <div className="space-y-4 px-3 py-6">
        <div className="rounded-xl bg-gradient-129 from-[#585C78] from-[17%] to-[#30333F] to-[84%] px-4 py-6 shadow-[0_8px_16px_0_rgba(57,68,83,0.1)] ">
          <div className="flex items-center">
            <div className="h-[70px] w-[70px] rounded-md bg-gradient-to-b from-[rgba(246,247,251,0.5)] to-[rgba(246,247,251,0)]">
              <div className="relative w-[90px] -translate-x-[10px]">
                <AspectRatio.Root ratio={482 / 375}>
                  <Image
                    src={data?.modelImg ? toImg(data.modelImg) : '/images/<EMAIL>'}
                    alt="车型图"
                    fill
                    priority
                  />
                </AspectRatio.Root>
              </div>
            </div>
            <div className="ml-3 space-y-1">
              <div className="text-xl font-semibold text-white">{data?.nickName}</div>
              <div className="text-2xs text-white/60">车架号：{data?.dvcDetail.deviceNo}</div>
              <div className="text-2xs text-white/60">
                激活时间：{data?.gmtBind && dayjs(data.gmtBind).format('YYYY-MM-DD')}
              </div>
            </div>
          </div>
        </div>
        <Button block type="primary" loading={isPending} onClick={handleLost}>
          {searchParams.get('state') === '1' ? '解除报失' : '车辆报失'}
        </Button>
      </div>
    </Page>
  );
}
