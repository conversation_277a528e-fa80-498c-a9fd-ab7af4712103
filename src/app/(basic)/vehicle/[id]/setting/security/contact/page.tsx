'use client';

import { useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { Form, Input, Toast } from 'antd-mobile';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { setVehicleSetting } from '@/server/actions/vehicle';
import { getVehicleSetting } from '@/server/api/vehicle';
import { PHONEREGEX } from '@/utils';
import { useQuery } from '@tanstack/react-query';

type FieldType = {
  sosName: string;
  sosPhone: string;
};

export default function Contact({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [form] = Form.useForm();
  const sosName = Form.useWatch('sosName', form);
  const sosPhone = Form.useWatch('sosPhone', form);

  const { data: vehicleSetting, dataUpdatedAt } = useQuery({
    queryKey: ['vehicleSetting', params.id],
    queryFn: () => getVehicleSetting<Record<string, any>>(params.id),
  });

  // 提交紧急联系人
  const onSubmit = (values: FieldType) => {
    startTransition(async () => {
      const result = await setVehicleSetting({
        ...values,
        commandType: 0,
        deviceNo: params.id,
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        router.back();
      }
    });
  };

  return (
    <Page title="紧急联系人">
      <div className="px-1">
        <Form
          form={form}
          layout="horizontal"
          mode="card"
          key={dataUpdatedAt}
          initialValues={{
            sosName: vehicleSetting?.bumpDvcSet.sosName,
            sosPhone: vehicleSetting?.bumpDvcSet.sosPhone,
          }}
          footer={
            <Button
              block
              type="primary"
              htmlType="submit"
              disabled={!sosName || !PHONEREGEX.test(sosPhone)}
              loading={isPending}
            >
              确定
            </Button>
          }
          onFinish={onSubmit}
        >
          <Form.Item label="姓名" name="sosName">
            <Input placeholder="请输入" maxLength={10} />
          </Form.Item>
          <Form.Header />
          <Form.Item label="手机号码" name="sosPhone">
            <Input placeholder="请输入" maxLength={11} type="text" inputMode="numeric" />
          </Form.Item>
        </Form>
      </div>
    </Page>
  );
}
