// @ts-nocheck

'use client'

import List from '@/components/List'
import Page from '@/components/Page'
import Switch from '@/components/Switch'
import { useBLEClient } from '@/lib/ble'
import { setVcuVehicleSetting, setVehicleSetting } from '@/server/actions/vehicle'
import { postBluetoothData } from '@/server/api/home'
import { getVehicleDetail, getVehicleSetting } from '@/server/api/vehicle'
import { state } from '@/store'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import Wheel from '@uiw/react-color-wheel'
import { Picker, Toast } from 'antd-mobile'

// @ts-nocheck

export default function Lighting({ params }: { params: { id: string } }) {
  const queryClient = useQueryClient()
  const bleClient = useBLEClient()

  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id),
    enabled: !!params.id
  })
  const isVcu = detail?.equiInfos.some((item) => item.equiNo.startsWith('V'))

  const { data: vehicleSetting } = useQuery({
    queryKey: ['vehicleSetting', params.id],
    queryFn: () => getVehicleSetting<Record<string, any>>(params.id)
  })

  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3)

  const handleSubmit = async (key: string, value: any) => {
    if (bleClient.isConnected()) {
      bleClient.setVehicleLighting(
        {
          lightAutomatic: vehicleSetting?.bumpDvcSet.lightAutomatic,
          lightOffDelay: vehicleSetting?.bumpDvcSet.lightOffDelay,
          lightOffDelayTime: vehicleSetting?.bumpDvcSet.lightOffDelayTime,
          [key]: value
        },
        (result: number[]) => {
          queryClient.setQueryData(['vehicleSetting', params.id], {
            ...vehicleSetting,
            bumpDvcSet: {
              ...vehicleSetting?.bumpDvcSet,
              lightAutomatic: result[0],
              lightOffDelay: result[1],
              lightOffDelayTime: result[2]
            }
          })

          postBluetoothData({
            deviceNo: params.id,
            blueName: bleClient.deviceInfo().deviceName,
            blueAddress: bleClient.deviceInfo().deviceMac,
            vcuNo: bleClient.deviceInfo().deviceSN,
            type: 18,
            lightAutomatic: result[0],
            lightOffDelay: result[1],
            lightOffDelayTime: result[2]
          })
        }
      )
      return
    } else if (!deviceNetWorkModule) {
      Toast.show('请先连接蓝牙')
      return
    }

    queryClient.setQueryData(['vehicleSetting', params.id], {
      ...vehicleSetting,
      bumpDvcSet: {
        ...vehicleSetting?.bumpDvcSet,
        [key]: value
      }
    })

    const result = isVcu
      ? await setVcuVehicleSetting({
          commandType: 1,
          vehicleSetType: 9,
          deviceNo: params.id,
          lightAtmosphereColor: vehicleSetting?.bumpDvcSet.lightAtmosphereColor,
          lightAtmosphere: vehicleSetting?.bumpDvcSet.lightAtmosphere,
          lightAutomatic: vehicleSetting?.bumpDvcSet.lightAutomatic,
          lightOffDelay: vehicleSetting?.bumpDvcSet.lightOffDelay,
          lightOffDelayTime: vehicleSetting?.bumpDvcSet.lightOffDelayTime,
          lightBrake: vehicleSetting?.bumpDvcSet.lightBrake,
          tailLightStrobeState: vehicleSetting?.bumpDvcSet.tailLightStrobeState,
          castLightState: vehicleSetting?.bumpDvcSet.castLightState,
          [key]: value
        })
      : await setVehicleSetting({
          commandType: 1,
          vehicleSetType: 9,
          deviceNo: params.id,
          lightAutomatic: vehicleSetting?.bumpDvcSet.lightAutomatic,
          lightOffDelay: vehicleSetting?.bumpDvcSet.lightOffDelay,
          lightOffDelayTime: vehicleSetting?.bumpDvcSet.lightOffDelayTime,
          tailLightStrobeState: vehicleSetting?.bumpDvcSet.tailLightStrobeState,
          castLightState: vehicleSetting?.bumpDvcSet.castLightState,
          [key]: value
        })
    if (result?.error) {
      Toast.show(result.error)
      queryClient.setQueryData(['vehicleSetting', params.id], vehicleSetting)
    }
  }

  const handleSubmitStar = async (key: string, value: any) => {
    // 更新数据方法
    const toUpdate = () =>
      queryClient.setQueryData(['vehicleSetting', params.id], {
        ...vehicleSetting,
        bumpDvcSet: {
          ...vehicleSetting?.bumpDvcSet,
          [key]: value
        }
      })

    if (bleClient.isConnected()) {
      switch (key) {
        case 'tailLightStrobeState':
          bleClient.setTailLightStrobeState(value ? 1 : 0, (result: number) => {
            queryClient.setQueryData(['vehicleSetting', params.id], {
              ...vehicleSetting,
              bumpDvcSet: {
                ...vehicleSetting?.bumpDvcSet,
                [key]: result
              }
            })
            postBluetoothData({
              deviceNo: detail?.deviceNo,
              blueName: bleClient.deviceInfo().deviceName,
              blueAddress: bleClient.deviceInfo().deviceMac,
              vcuNo: bleClient.deviceInfo().deviceSN,
              type: 18,
              [key]: result
            })
          })
          break
        case 'castLightState':
          bleClient.setCastLightState(value ? 1 : 0, (result: number) => {
            queryClient.setQueryData(['vehicleSetting', params.id], {
              ...vehicleSetting,
              bumpDvcSet: {
                ...vehicleSetting?.bumpDvcSet,
                [key]: result
              }
            })
            postBluetoothData({
              deviceNo: detail?.deviceNo,
              blueName: bleClient.deviceInfo().deviceName,
              blueAddress: bleClient.deviceInfo().deviceMac,
              vcuNo: bleClient.deviceInfo().deviceSN,
              type: 18,
              [key]: result
            })
          })
          break
      }
    } else if (deviceNetWorkModule) {
      toUpdate()
      const result = isVcu
        ? await setVcuVehicleSetting({
            commandType: 1,
            vehicleSetType: key === 'tailLightStrobeState' ? 26 : 27,
            deviceNo: params.id,
            [key]: value
          })
        : await setVehicleSetting({
            commandType: 1,
            vehicleSetType: key === 'tailLightStrobeState' ? 26 : 27,
            deviceNo: params.id,
            [key]: value
          })
      if (result?.error) {
        Toast.show(result.error)
        queryClient.setQueryData(['vehicleSetting', params.id], vehicleSetting)
      }
    } else {
      Toast.show('请先连接蓝牙')
    }
  }

  return (
    <Page title="灯光设置">
      <div className="space-y-4 px-3 py-6">
        {detail?.scheme.includes('atmosphere_lamp_color') && (
          <div className="flex justify-center">
            <Wheel
              color={vehicleSetting?.bumpDvcSet.lightAtmosphereColor ?? 'ffffff'}
              onChange={(color) => {
                handleSubmit('lightAtmosphereColor', color.hex.replace('#', ''))
              }}
            />
          </div>
        )}
        <List
          items={[
            {
              title: '氛围灯模式',
              onClick: (_, valueChildRef) => {
                valueChildRef.current?.open()
              },
              value: (
                <Picker
                  columns={[
                    [
                      { label: '白色', value: 0 },
                      { label: '单色常亮', value: 1 },
                      { label: '单色呼吸灯', value: 2 },
                      { label: '七色渐变循环', value: 3 }
                    ]
                  ]}
                  cancelText="取消"
                  value={[vehicleSetting?.bumpDvcSet.lightAtmosphere]}
                  onConfirm={(value) => {
                    handleSubmit('lightAtmosphere', value[0])
                  }}
                  disabled={detail?.isOwner === 0}
                >
                  {(_) => {
                    return <span>{_[0]?.label}</span>
                  }}
                </Picker>
              ),
              arrow: true,
              show: detail?.scheme.includes('atmosphere_lamp_mode')
            },
            {
              title: '自动大灯',
              value: (
                <Switch
                  defaultChecked={false}
                  checked={vehicleSetting?.bumpDvcSet.lightAutomatic === 1}
                  onChange={(checked) => {
                    handleSubmit('lightAutomatic', checked ? 1 : 0)
                  }}
                  disabled={detail?.isOwner === 0}
                />
              ),
              show: detail?.scheme.includes('automatic_headlamp')
            }
          ]}
        />
        <List
          items={[
            {
              title: '断电熄灯延时',
              value: (
                <Switch
                  defaultChecked={false}
                  disabled={detail?.isOwner === 0}
                  checked={vehicleSetting?.bumpDvcSet.lightOffDelay === 1}
                  onChange={(checked) => {
                    handleSubmit('lightOffDelay', checked ? 1 : 0)
                  }}
                />
              ),
              show: detail?.scheme.includes('break_light_delay')
            },
            {
              title: '延时秒数',
              onClick: (_, valueChildRef) => {
                valueChildRef.current?.open()
              },
              value: (
                <Picker
                  disabled={detail?.isOwner === 0}
                  columns={[
                    isVcu
                      ? [
                          { value: 1, label: 10 },
                          { value: 2, label: 20 },
                          { value: 4, label: 30 },
                          { value: 8, label: 60 }
                        ]
                      : [
                          { value: 0, label: 5 },
                          { value: 1, label: 15 },
                          { value: 2, label: 20 },
                          { value: 3, label: 30 },
                          { value: 4, label: 60 }
                        ]
                  ]}
                  cancelText="取消"
                  value={[vehicleSetting?.bumpDvcSet.lightOffDelayTime]}
                  onConfirm={(value) => {
                    handleSubmit('lightOffDelayTime', value[0])
                  }}
                >
                  {(_) => {
                    return <span>{_[0]?.label}</span>
                  }}
                </Picker>
              ),
              arrow: true,
              show:
                detail?.scheme.includes('delay_time') &&
                (vehicleSetting?.bumpDvcSet.lightOffDelay === 1 || isVcu)
            }
          ]}
        />
        <List
          items={[
            {
              title: '刹车灯模式',
              onClick: (_, valueChildRef) => {
                valueChildRef.current?.open()
              },
              value: (
                <Picker
                  disabled={detail?.isOwner === 0}
                  columns={[
                    [
                      { label: '常亮', value: 0 },
                      { label: '闪烁', value: 1 }
                    ]
                  ]}
                  cancelText="取消"
                  value={[vehicleSetting?.bumpDvcSet.lightBrake]}
                  onConfirm={(value) => {
                    handleSubmit('lightBrake', value[0])
                  }}
                >
                  {(_) => {
                    return <span>{_[0]?.label}</span>
                  }}
                </Picker>
              ),
              arrow: true,
              show: detail?.scheme.includes('brake_light_mode')
            },
            {
              title: '尾灯爆闪',
              value: (
                <Switch
                  defaultChecked={false}
                  checked={vehicleSetting?.bumpDvcSet.tailLightStrobeState === 1}
                  onChange={(checked) => {
                    handleSubmitStar('tailLightStrobeState', checked ? 1 : 0)
                  }}
                  disabled={detail?.isOwner === 0}
                />
              ),
              show: detail?.scheme.includes('tail_light')
            },
            {
              title: 'Logo投影灯',
              value: (
                <Switch
                  defaultChecked={false}
                  checked={vehicleSetting?.bumpDvcSet.castLightState === 1}
                  onChange={(checked) => {
                    handleSubmitStar('castLightState', checked ? 1 : 0)
                  }}
                  disabled={detail?.isOwner === 0}
                />
              ),
              show: detail?.scheme.includes('logo_light')
            }
          ]}
        />
      </div>
    </Page>
  )
}
