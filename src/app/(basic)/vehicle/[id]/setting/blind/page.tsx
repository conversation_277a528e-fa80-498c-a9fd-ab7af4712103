// @ts-nocheck

'use client'

import ListGroup from '@/components/ListGroup'
import Page from '@/components/Page'
import { useBLEClient } from '@/lib/ble'
import { useNative } from '@/lib/native'
import { postblindZoneState } from '@/server/actions/vehicle'
import { postBluetoothData } from '@/server/api/home'
import { getVehicleDetail, getblindZoneState } from '@/server/api/vehicle'
import { state } from '@/store'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Toast } from 'antd-mobile'

const list = [
  [
    {
      key: 'bsdRadarState',
      title: 'BSD雷达监测',
      type: 'switch',
      defaultValue: false,
      scheme: 'bsd_radar'
    },
    {
      key: 'bsdRadarZone',
      title: '角度调节',
      type: 'slider',
      min: 1,
      max: 3,
      defaultValue: 2,
      marksData: [15, 30, 45],
      marks: ['窄角', '标准', '广角'],
      show: 'bsdRadarState',
      scheme: 'angle'
    }
  ],
  [
    {
      key: 'lacState',
      title: 'LCA变道辅助',
      type: 'switch',
      defaultValue: false,
      scheme: 'lca'
    },
    {
      key: 'lacDistanceX',
      title: '横向距离调节',
      type: 'slider',
      min: 1,
      max: 3,
      defaultValue: 2,
      marksData: [10, 15, 20],
      marks: ['较窄', '标准', '较宽'],
      show: 'lacState',
      scheme: 'horizontal_distance'
    },
    {
      key: 'lacDistanceY',
      title: '纵向距离调节',
      type: 'slider',
      min: 1,
      max: 3,
      defaultValue: 2,
      base: 0,
      marksData: [100, 200, 300],
      marks: ['较近', '标准', '较远'],
      show: 'lacState',
      scheme: 'vertical_distance'
    }
  ]
]

export default function Blind({ params }: { params: { id: string } }) {
  const { id } = params
  const bleClient = useBLEClient()
  const queryClient = useQueryClient()
  const { clientId } = useNative()

  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3)
  const queryKey = ['blind', id]

  const { data = {} } = useQuery({
    queryKey,
    queryFn: () => getblindZoneState({ deviceNo: id, clientId }),
    enabled: !!params.id
  })

  const { data: detail = {} } = useQuery({
    queryKey: ['vehicle', 'detail', id],
    queryFn: () => getVehicleDetail(id),
    enabled: !!id
  })
  const scheme = detail.scheme || []

  const handleSubmit = async (key: string, value: any) => {
    //TODO 如果蓝牙连接，则直接调用蓝牙接口
    if (bleClient.isConnected()) {
      // 乐观更新
      queryClient.setQueryData(queryKey, { ...data, [key]: value })
      if (key === 'bsdRadarState' || key === 'lacState') {
        bleClient.setBlindState(
          { bsdRadarState: data.bsdRadarState, lacState: data.lacState, [key]: value },
          (result) => {
            // 上报数据给后端
            // queryClient.setQueryData(queryKey, { ...data, ...res })
            postBluetoothData({
              deviceNo: params.id,
              blueName: bleClient.deviceInfo().deviceName,
              blueAddress: bleClient.deviceInfo().deviceMac,
              vcuNo: bleClient.deviceInfo().deviceSN,
              type: 28,
              ...data
            })
          }
        )
        bleClient.setBlindRange(
          {
            bsdRadarZone: data.bsdRadarZone,
            lacDistanceX: data.lacDistanceX,
            lacDistanceY: data.lacDistanceY
          },
          (result) => {
            // 上报数据给后端
            // queryClient.setQueryData(queryKey, { ...data, ...res })
            postBluetoothData({
              deviceNo: params.id,
              blueName: bleClient.deviceInfo().deviceName,
              blueAddress: bleClient.deviceInfo().deviceMac,
              vcuNo: bleClient.deviceInfo().deviceSN,
              type: 29,
              ...data
            })
          }
        )
      } else {
        bleClient.setBlindRange(
          {
            bsdRadarZone: data.bsdRadarZone,
            lacDistanceX: data.lacDistanceX,
            lacDistanceY: data.lacDistanceY,
            [key]: value
          },
          (result) => {
            // 上报数据给后端
            // queryClient.setQueryData(queryKey, { ...data, ...res })
            postBluetoothData({
              deviceNo: params.id,
              blueName: bleClient.deviceInfo().deviceName,
              blueAddress: bleClient.deviceInfo().deviceMac,
              vcuNo: bleClient.deviceInfo().deviceSN,
              type: 29,
              ...data
            })
          }
        )
      }
    } else if (deviceNetWorkModule) {
      // 乐观更新
      queryClient.setQueryData(queryKey, { ...data, [key]: value })
      //TODO 如果蓝牙未连接，则使用网络请求
      const result = await postblindZoneState({
        deviceNo: params.id,
        ...data,
        [key]: value
      })
      if (result?.error) {
        Toast.show(result.error)
      }
    } else {
      //TODO 如果蓝牙未连接并不能用网络，则提示用户连接蓝牙
      Toast.show('请先连接蓝牙')
    }
  }

  return (
    <Page title="盲区监测设置">
      <div className="px-3 py-6 space-y-3">
        <ListGroup
          list={list}
          data={data}
          handleSubmit={handleSubmit}
          scheme={scheme}
          disabled={detail?.isOwner === 0}
        />
      </div>
    </Page>
  )
}
