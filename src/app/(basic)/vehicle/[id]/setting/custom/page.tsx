// @ts-nocheck

'use client'

import Button from '@/components/Button'
import List from '@/components/List'
import Page from '@/components/Page'
import Switch from '@/components/Switch'
import { useBLEClient } from '@/lib/ble'
import { useNative } from '@/lib/native'
import { deleteCustomMode, updateCustomMode } from '@/server/actions/vehicle'
import { getCustomModeList, getVehicleDetail } from '@/server/api/vehicle'
import { state } from '@/store'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Dialog, Empty, SwipeAction, Toast } from 'antd-mobile'
import Link from 'next/link'

// @ts-nocheck

export default function Custom({ params }: { params: { id: string } }) {
  const { id } = params
  const bleClient = useBLEClient()
  const queryClient = useQueryClient()
  const { clientId } = useNative()
  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3)

  const { data = [] } = useQuery({
    queryKey: ['custom', id],
    queryFn: () => getCustomModeList({ deviceNo: id, clientId }),
    enabled: !!id
  })
  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', id],
    queryFn: () => getVehicleDetail(id),
    enabled: !!id
  })
  // 更新数据方法
  const toUpdate = () => queryClient.invalidateQueries({ queryKey: ['custom', id] })

  const handleSubmit = async (item, value) => {
    //TODO 如果蓝牙连接，则直接调用蓝牙接口
    if (bleClient.isConnected()) {
      bleClient.setCustomMode({ deviceNo: id, ...item, state: value }, async (result) => {
        // 上报数据给后端
        await postBluetoothData({
          deviceNo,
          blueName: bleClient.deviceInfo().deviceName,
          blueAddress: bleClient.deviceInfo().deviceMac,
          vcuNo: bleClient.deviceInfo().deviceSN,
          type: 32,
          ...data,
          state: result[0]
        })
        toUpdate()
      })
    } else if (deviceNetWorkModule) {
      //TODO 如果蓝牙未连接，则使用网络请求
      const result = await updateCustomMode({ deviceNo: id, id: item.id, state: value })
      if (result?.error) {
        Toast.show(result.error)
      }
      toUpdate()
    } else {
      //TODO 如果蓝牙未连接并不能用网络，则提示用户连接蓝牙
      Toast.show('请先连接蓝牙')
    }
  }

  const toDel = async (key) => {
    const result = await deleteCustomMode({ deviceNo: id, id: key })
    if (result?.error) {
      Toast.show(result.error)
      return
    }
    Toast.show('删除成功')
    toUpdate()
  }

  return (
    <Page title="自定义模式">
      <div className="px-3 py-6 space-y-3">
        {data.map((item) => (
          <SwipeAction
            key={item.id}
            rightActions={
              detail?.isOwner !== 0
                ? [
                    {
                      key: 'delete',
                      text: '删除',
                      color: 'danger',
                      onClick: () => {
                        if (item.state === '1') {
                          Toast.show('请关闭后再删除')
                          return
                        }
                        Dialog.confirm({
                          content: '确定要删除吗？',
                          onConfirm: () => toDel(item.id)
                        })
                      }
                    }
                  ]
                : []
            }
          >
            <List
              key={item.id}
              items={[
                {
                  key: 1,
                  title: item.name,
                  value: (
                    <Switch
                      disabled={detail?.isOwner === 0}
                      checked={item.state === '1' || item.state === 1}
                      onChange={(checked) => handleSubmit(item, checked ? 1 : 0)}
                    />
                  )
                },
                {
                  key: 2,
                  title: '设置',
                  href: 'custom/' + item.id
                }
              ]}
            />
          </SwipeAction>
        ))}
        {data.length === 0 ? <Empty description="暂无数据" /> : null}

        <p className="text-xs text-gray-400" style={{ margin: '2rem 0 1rem 0' }}>
          支持添加3种自定义模式，选择开启后仪表档位将会根据您的设置进行调整
        </p>
        {data.length < 3 && detail?.isOwner !== 0 ? (
          <Link href="custom/0">
            <Button block type="primary">
              <div className="flex h-full w-full items-center justify-center text-white">
                立即添加
              </div>
            </Button>
          </Link>
        ) : null}
      </div>
    </Page>
  )
}
