// @ts-nocheck
'use client'

import Button from '@/components/Button'
import ListGroup from '@/components/ListGroup'
import Page from '@/components/Page'
import { useBLEClient } from '@/lib/ble'
import { useNative } from '@/lib/native'
import { postCustomMode } from '@/server/actions/vehicle'
import { getCustomMode, getVehicleDetail } from '@/server/api/vehicle'
import { state } from '@/store'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Toast } from 'antd-mobile'
import { useDeepCompareEffect } from 'common-hook'
import { isNil } from 'common-screw'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'

const list = [
  [
    {
      key: 'name',
      title: '模式名称',
      type: 'input'
    }
  ],
  [
    {
      key: 'energyRecoveryState',
      title: '能量回收强度',
      type: 'switch',
      defaultValue: false,
      scheme: 'kers_switch'
    },
    {
      key: 'energyRecoveryKind',
      title: '能量回收强度调节',
      type: 'slider',
      min: 1,
      max: 3,
      defaultValue: 2,
      marksData: [1, 2, 3],
      marks: ['弱', '标准', '强'],
      show: 'energyRecoveryState',
      scheme: 'kers_intensity'
    }
  ],
  [
    {
      key: 'tcsState',
      title: '牵引力控制TCS',
      type: 'slider',
      min: 1,
      max: 2,
      defaultValue: 1,
      marksData: [0, 1],
      marks: ['适中', '强'],
      scheme: 'tcs_intensity'
    },
    {
      key: 'throttle',
      title: '油门灵敏度',
      type: 'slider',
      min: 1,
      max: 3,
      defaultValue: 2,
      marksData: [1, 2, 3],
      marks: ['低', '适中', '高'],
      scheme: 'pedal_sensitivity'
    },
    {
      key: 'ultimateSpeed',
      title: '极速调节',
      type: 'slider',
      min: 2,
      max: 10,
      defaultValue: 2,
      marksData: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
      marks: ['', '20%', '', '', '', '', '', '', '', '100%'],
      rightShow: (data) => (data < 11 ? data * 10 + '%' : data + '%'),
      scheme: 'maxspeed_adjust'
    }
  ],
  [
    {
      key: 'snowModeState',
      title: '雨雪模式',
      type: 'switch',
      scheme: 'rain_snow_mode'
    },
    {
      key: 'nitrogenBoostState',
      title: '氮气加速',
      type: 'switch',
      scheme: 'N_accelerate'
    }
  ]
]

export default function CustomDetail({ params }) {
  const router = useRouter()
  const bleClient = useBLEClient()
  const queryClient = useQueryClient()
  const { clientId } = useNative()
  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3)

  const { id, key } = params
  const type = key === '0' ? 'add' : 'edit'
  const [data, setData] = useState({})

  const { data: customMode = {} } = useQuery({
    queryKey: ['customMode', id, key],
    queryFn: () => getCustomMode({ deviceNo: id, id: key }),
    enabled: key !== '0'
  })

  const { data: { scheme = [] } = {} } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id),
    enabled: !!params.id
  })

  useEffect(() => {
    if (type === 'add') {
      setData({
        name: '',
        energyRecoveryState: '1',
        energyRecoveryKind: 2,
        tcsState: 1,
        throttle: 2,
        ultimateSpeed: 2,
        snowModeState: '0',
        nitrogenBoostState: '0'
      })
    }
  }, [type])

  useDeepCompareEffect(() => {
    if (!isNil(customMode)) {
      setData(customMode)
    }
  }, [customMode])

  const handleSubmit = async (key, value) => setData({ ...data, [key]: value })

  const toSubmit = async () => {
    let result = await postCustomMode({ deviceNo: id, ...data })

    if (result?.error) {
      Toast.show(result.error)
      return
    }
    queryClient.invalidateQueries({ queryKey: ['customMode', id, key] })
    //返回上一级
    router.back()
  }

  return (
    <Page title="自定义模式">
      <div className="px-3 py-6 space-y-3">
        {!isNil(data) && (
          <ListGroup list={list} data={data} scheme={scheme} handleSubmit={handleSubmit} />
        )}
        <Button block type="primary" onClick={() => toSubmit()} disabled={!data.name || data.state}>
          <div className="flex h-full w-full items-center justify-center text-white">保 存</div>
        </Button>
      </div>
    </Page>
  )
}
