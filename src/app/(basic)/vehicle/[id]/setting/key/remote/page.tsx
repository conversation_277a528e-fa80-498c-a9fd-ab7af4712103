'use client';

import { useMemo, useState, useEffect } from 'react';
import Image from 'next/image';
import Page from '@/components/Page';
import List from '@/components/List';
import Button from '@/components/Button';
import { useBLEClient } from '@/lib/ble';
import { getKeys, addKey, deleteKey, updateKey } from '@/server/api/home';
import { useQuery, useMutation } from '@tanstack/react-query';
import { setVehicleSetting, setVcuVehicleSetting } from '@/server/actions/vehicle';
import { getVehicleDetail } from '@/server/api/vehicle';
import { ActionSheet, Modal, Dialog, Input, Toast } from 'antd-mobile';
import type { Action } from 'antd-mobile/es/components/action-sheet';
import { useTimeout } from '@/hooks/useTimeout';

export default function Key({ params }: { params: { id: string } }) {
  const bleClient = useBLEClient();
  const [visible, setVisible] = useState(false);
  const [currentKey, setCurrentKey] = useState('');
  const [dialogShow, setDialogShow] = useState(false);
  const [keyName, setKeyName] = useState('');
  const [refetchInterval, setRefetchInterval] = useState<number | false>(false);
  // 超时处理
  const { start, clear } = useTimeout(() => {
    Modal.clear();
    Toast.show('配对失败');
  }, 8000);

  const { data = [], refetch } = useQuery({
    queryKey: ['keys'],
    queryFn: () => getKeys(params.id),
    refetchInterval,
  });

  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id),
    enabled: !!params.id,
  });
  const isVcu = detail?.equiInfos.some((item) => item.equiNo.startsWith('V'));

  const items = useMemo(() => {
    return data?.map((item) => ({
      icon: item.type === 1 ? `/images/<EMAIL>` : '/images/<EMAIL>',
      title: item.name || item.mac,
      arrow: true,
      onClick: () => {
        setCurrentKey(item.mac);
        setKeyName(item.name || item.mac);
        setVisible(true);
      },
    }));
  }, [data]);
  // 蓝牙添加钥匙上报
  const { mutate: keyAdd } = useMutation({
    mutationFn: addKey,
    onSuccess: () => {
      refetch();
    },
  });
  // 蓝牙删除钥匙上报
  const { mutate: keyDelete } = useMutation({
    mutationFn: deleteKey,
    onSuccess: () => {
      refetch();
    },
  });
  // 修改钥匙名称
  const { mutate: keyUpdate } = useMutation({
    mutationFn: updateKey,
    onSuccess: () => {
      refetch();
      setDialogShow(false);
    },
  });

  const handleAdd = async () => {
    if (bleClient.isConnected()) {
      // 开始超时计时
      start();
      Modal.show({
        content: (
          <div>
            <div className="flex flex-col items-center ">
              <Image alt="配对中" src="/images/bluetooth-key.gif" width={105} height={105} />
              <div className="mt-3 text-xl">配对中...</div>
            </div>
            <div className=" px-4">
              <p className="mt-3 text-2xs text-color-weak">1.添加钥匙时,请先解锁车辆</p>
              <p className="mt-1 text-2xs text-color-weak">2.点击遥控钥匙的启动键来添加钥匙</p>
            </div>
          </div>
        ),
      });
      bleClient.addKey((result) => {
        clear();
        Modal.clear();
        // 0失败 1成功 2已达上限
        if (result !== 1) {
          Toast.show(result === 0 ? '配对失败' : '已达上限');
        }
      });
    } else if (detail?.communicateTypes.includes(2) || detail?.communicateTypes.includes(3)) {
      // 网络添加钥匙
      const settingParams = {
        commandType: 1,
        vehicleSetType: 8,
        deviceNo: params.id,
        keyOperate: isVcu ? undefined : 1,
      };
      const result = isVcu
        ? await setVcuVehicleSetting(settingParams)
        : await setVehicleSetting(settingParams);
      if (result?.error) {
        Toast.show(result.error);
      } else {
        Modal.show({
          content: (
            <div>
              <div className="flex flex-col items-center ">
                <Image alt="配对中" src="/images/bluetooth-key.gif" width={105} height={105} />
                <div className="mt-3 text-xl">配对中...</div>
              </div>
              <div className=" px-4">
                <p className="mt-3 text-2xs text-color-weak">1.添加钥匙时,请先解锁车辆</p>
                <p className="mt-1 text-2xs text-color-weak">2.点击遥控钥匙的启动键来添加钥匙</p>
              </div>
            </div>
          ),
        });
        // 仪表自身钥匙配对超时时间为5秒
        // 配对期间轮询获取钥匙列表
        setRefetchInterval(1000);
        // 等待5100毫秒后关闭弹窗并停止轮询
        setTimeout(() => {
          Modal.clear();
          setRefetchInterval(false);
        }, 5100);
      }
    } else {
      Toast.show('请先连接蓝牙');
    }
  };

  const handleAction = (action: Action) => {
    setVisible(false);
    if (action.key === 'delete') {
      Dialog.confirm({
        content: '确认删除钥匙？',
        onConfirm: async () => {
          if (bleClient.isConnected()) {
            bleClient.deleteBLEKey(currentKey);
            const keyId = data.find((item) => item.mac === currentKey)?.id;
            keyDelete(keyId);
          } else if (detail?.communicateTypes.includes(2) || detail?.communicateTypes.includes(3)) {
            // 网络删除钥匙
            const settingParams = {
              commandType: 1,
              vehicleSetType: 8,
              deviceNo: params.id,
              keyOperate: 3,
              key: currentKey,
            };
            const result = isVcu
              ? await setVcuVehicleSetting(settingParams)
              : await setVehicleSetting(settingParams);
            if (result?.error) {
              Toast.show(result.error);
            } else {
              // 下发删除钥匙指令后，轮询更新钥匙列表，并在2秒后停止轮询
              setRefetchInterval(600);
              setTimeout(() => {
                setRefetchInterval(false);
              }, 2000);
            }
          } else {
            Toast.show('请先连接蓝牙');
          }
        },
      });
    } else if (action.key === 'edit') {
      setDialogShow(true);
    }
  };

  useEffect(() => {
    // 上报蓝牙钥匙
    const unlistener = bleClient.onBLEDataChange((type, result: string[]) => {
      if (type === '8d') {
        // 已经添加的钥匙
        const keys = data.map((item) => item.mac);
        // 过滤已经添加的钥匙
        result = result.filter((item) => !keys.includes(item));
        result.forEach((item) => {
          // 前三个字节是00的是433钥匙，否则是蓝牙钥匙
          const type = item.slice(0, 6) === '000000' ? 1 : 2;
          keyAdd({ deviceNo: params.id, name: item, type, mac: item });
        });
      }
    });
    return () => {
      unlistener();
    };
  }, [bleClient, params.id, data, keyAdd]);

  return (
    <Page title="钥匙管理">
      <div className="px-3 py-6">
        {items.length === 0 ? (
          <div className="flex flex-col items-center">
            <Image src="/images/empty-key.png" alt="缺省" width={180} height={148} priority />
            <div className="mt-3">未添加遥控钥匙</div>
            <Button type="primary" className="mt-6 w-48" onClick={handleAdd}>
              立即添加
            </Button>
          </div>
        ) : (
          <>
            <List items={items} />
            <Button block type="primary" className="mt-6" onClick={handleAdd}>
              立即添加
            </Button>
          </>
        )}
        <ActionSheet
          cancelText="取消"
          visible={visible}
          actions={
            isVcu
              ? [{ text: '修改名称', key: 'edit' }]
              : [
                  { text: '修改名称', key: 'edit' },
                  {
                    text: '删除',
                    key: 'delete',
                  },
                ]
          }
          onAction={handleAction}
          onClose={() => setVisible(false)}
        />
        <Dialog
          visible={dialogShow}
          content={
            <div className="flex flex-col items-center justify-center px-2 text-xl">
              <div className="text-center">钥匙名称</div>
              <Input
                value={keyName}
                maxLength={15}
                placeholder="请输入钥匙名称"
                className="mb-2 mt-3 h-10 rounded border border-[#e5e5e5] px-2"
                onChange={(v) => {
                  setKeyName(v);
                }}
              />
            </div>
          }
          actions={[
            [
              {
                key: 'cancel',
                text: '取消',
              },
              {
                key: 'confirm',
                text: '确认',
              },
            ],
          ]}
          onAction={(action) => {
            if (action.key === 'confirm') {
              // 提交
              const item = data.find((item) => item.mac === currentKey);
              keyUpdate({ id: item.id, name: keyName });
            } else {
              setDialogShow(false);
            }
          }}
        />
      </div>
    </Page>
  );
}
