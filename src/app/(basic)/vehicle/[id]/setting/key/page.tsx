/*
 * @Author: <PERSON><PERSON>
 * @Email: <EMAIL>
 * @Date: 2025-03-12 15:09:11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-03-17 13:28:52
 */
'use client'

import List from '@/components/List'
import Page from '@/components/Page'
import { useBLEClient } from '@/lib/ble'
import { getVehicleDetail, getVehicleSetting } from '@/server/api/vehicle'
import { state } from '@/store'
import { useQuery, useQueryClient } from '@tanstack/react-query'

export default function Key({ params }: { params: { id: string } }) {
  const bleClient = useBLEClient()
  const queryClient = useQueryClient()
  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3)

  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id),
    enabled: !!params.id
  })
  const isVcu = detail?.equiInfos.some((item) => item.equiNo.startsWith('V'))

  const { data: vehicleSetting } = useQuery({
    queryKey: ['vehicleSetting', params.id],
    queryFn: () => getVehicleSetting<Record<string, any>>(params.id)
  })

  return (
    <Page title="钥匙管理">
      <div className="space-y-3 px-3  py-6">
        <List
          items={[
            {
              key: 1,
              icon: '/images/C-7/<EMAIL>',
              title: '遥控钥匙管理',
              href: 'key/remote',
              disabled: detail?.isOwner === 0,
              show: detail?.scheme.includes('remote_control')
            },
            {
              key: 2,
              icon: '/images/C-7/<EMAIL>',
              title: '开机密码设置',
              href: 'key/boot',
              value: vehicleSetting?.bumpDvcSet.powerPwdState ? '已设置' : '未设置',
              show: detail?.scheme.includes('pwd_set'),
              disabled: detail?.isOwner === 0
            },
            {
              key: 3,
              icon: '/images/C-7/<EMAIL>',
              title: '指纹管理',
              href: 'key/print',
              show: detail?.scheme.includes('thumbprint')
            }
          ]}
        />
      </div>
    </Page>
  )
}
