// @ts-nocheck
'use client'

import Button from '@/components/Button'
import List from '@/components/List'
import Page from '@/components/Page'
import { useBLEClient } from '@/lib/ble'
import { useNative } from '@/lib/native'
import { deleteFinger, updateFingerName } from '@/server/actions/vehicle'
import { getFingerList, getVehicleDetail, postFingerNet } from '@/server/api/vehicle'
import { state } from '@/store'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Dialog, Empty, Input, SwipeAction, Toast } from 'antd-mobile'
import { getNow } from 'common-screw'
import { useState } from 'react'

// @ts-nocheck

// 指纹图标组件
const FingerprintIcon = ({ size }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 1C5.925 1 1 5.925 1 12C1 18.075 5.925 23 12 23C18.075 23 23 18.075 23 12C23 5.925 18.075 1 12 1ZM12 21C7.038 21 3 16.962 3 12C3 7.038 7.038 3 12 3C16.962 3 21 7.038 21 12C21 16.962 16.962 21 12 21Z"
      fill="#999"
    />
    <path
      d="M12 5C8.141 5 5 8.141 5 12C5 13.927 5.763 15.673 7 16.899V14.657C6.376 13.91 6 12.997 6 12C6 8.691 8.691 6 12 6C15.309 6 18 8.691 18 12C18 12.997 17.624 13.91 17 14.657V16.899C18.237 15.673 19 13.927 19 12C19 8.141 15.859 5 12 5Z"
      fill="#999"
    />
    <path
      d="M12 7C9.243 7 7 9.243 7 12C7 13.297 7.577 14.459 8.5 15.242V13.657C8.189 13.179 8 12.613 8 12C8 9.794 9.794 8 12 8C14.206 8 16 9.794 16 12C16 12.613 15.811 13.179 15.5 13.657V15.242C16.423 14.459 17 13.297 17 12C17 9.243 14.757 7 12 7Z"
      fill="#999"
    />
    <path
      d="M12 9C10.346 9 9 10.346 9 12C9 12.617 9.189 13.179 9.5 13.657V12C9.5 11.172 10.172 10.5 11 10.5H13C13.828 10.5 14.5 11.172 14.5 12V13.657C14.811 13.179 15 12.617 15 12C15 10.346 13.654 9 12 9Z"
      fill="#999"
    />
    <path
      d="M11 11.5C10.724 11.5 10.5 11.724 10.5 12V17C10.5 17.276 10.724 17.5 11 17.5H13C13.276 17.5 13.5 17.276 13.5 17V12C13.5 11.724 13.276 11.5 13 11.5H11Z"
      fill="#999"
    />
  </svg>
)

export default function Custom({ params }: { params: { id: string } }) {
  const { id } = params
  const bleClient = useBLEClient()
  const queryClient = useQueryClient()
  const { clientId } = useNative()
  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3)
  const queryKey = ['fingerprint', id]

  const [inputInfo, setInputInfo] = useState({ open: false, key: '', value: '' })
  const [printInfo, setPrintInfo] = useState({ open: false })

  const { data = [] } = useQuery({
    queryKey,
    queryFn: () => getFingerList({ deviceNo: id, clientId }),
    enabled: !!id
  })
  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', id],
    queryFn: () => getVehicleDetail(id),
    enabled: !!id
  })

  const handleSubmit = async (key, name) => {
    const result = await updateFingerName({ deviceNo: id, id: key, name })
    if (result?.error) {
      Toast.show(result.error)
      return
    }
    Toast.show('更新成功')
    clearInput()
    toUpdate()
  }
  const toUpdate = () => queryClient.invalidateQueries({ queryKey })

  const toAdd = async (key) => {
    const deviceNo = params.id
    if (bleClient.isConnected()) {
      setPrintInfo({ open: true })
      //TODO 如果蓝牙连接，则直接调用蓝牙接口
      bleClient.setFingerprintAdd(1, async (result) => {
        if (result[0] === 0 || result[0] === '0') {
          Toast.show('指纹添加失败')
          return
        } else if (result[0] === 2 || result[0] === '2') {
          Toast.show('设备指纹已达上限,添加失败')
          return
        }
        // 上报数据给后端
        await postBluetoothData({
          deviceNo,
          blueName: bleClient.deviceInfo().deviceName,
          blueAddress: bleClient.deviceInfo().deviceMac,
          vcuNo: bleClient.deviceInfo().deviceSN,
          clientId,
          type: 27,
          fingerId: result[1],
          name: '指纹' + getNow()
        })
        setPrintInfo({ open: false })
        toUpdate()
      })
    } else if (deviceNetWorkModule) {
      setPrintInfo({ open: true })
      //TODO 如果蓝牙未连接，则使用网络请求
      const result = await postFingerNet({ deviceNo, clientId })
      console.log('[ result ] >', result)
      if (result?.error) {
        Toast.show(result.error)
      }
    } else {
      //TODO 如果蓝牙未连接，则提示用户连接蓝牙
      Toast.show('请先连接蓝牙')
    }
  }
  const toDel = async (key) => {
    const deviceNo = params.id
    // 更新数据方法

    if (bleClient.isConnected()) {
      //TODO 如果蓝牙连接，则直接调用蓝牙接口
      bleClient.setFingerprintDel(key, async (result) => {
        if (result[0] === '0') {
          Toast.show('指纹删除失败')
          return
        }
        // 上报数据给后端
        await postBluetoothData({
          deviceNo,
          blueName: bleClient.deviceInfo().deviceName,
          blueAddress: bleClient.deviceInfo().deviceMac,
          vcuNo: bleClient.deviceInfo().deviceSN,
          clientId,
          type: 27,
          deviceNo: id,
          id: key
        })
        toUpdate()
      })
    } else if (deviceNetWorkModule) {
      //TODO 如果蓝牙未连接，则使用网络请求
      const result = await deleteFinger({ deviceNo, id: key, type: 2 })
      if (result?.error) {
        Toast.show(result.error)
        return
      }
      Toast.show('指纹删除成功')
      toUpdate()
    } else {
      //TODO 如果蓝牙未连接并不能用网络，则提示用户连接蓝牙
      Toast.show('请先连接蓝牙')
    }
  }

  const clearInput = () => setInputInfo({ open: false, key: '', value: '' })

  return (
    <Page title="指纹管理">
      <div className="px-3 py-6 space-y-3">
        {data.map((item) => (
          <SwipeAction
            key={item.id}
            rightActions={
              detail?.isOwner !== 0
                ? [
                    {
                      key: 'edit',
                      text: '修改名称',
                      onClick: () => setInputInfo({ open: true, key: item.id, value: item.name })
                    },
                    {
                      key: 'del',
                      text: '删除',
                      color: 'danger',
                      onClick: async () => {
                        await Dialog.confirm({
                          content: '确定要删除吗？',
                          onConfirm: () => toDel(item.id)
                        })
                      }
                    }
                  ]
                : []
            }
          >
            <List
              key={item.id}
              items={[
                {
                  key: 1,
                  title: item.name,
                  icon: <FingerprintIcon size={24} />,
                  arrow: 'horizontal'
                }
              ]}
            />
          </SwipeAction>
        ))}
        {data.length === 0 ? <Empty description="未添加指纹" /> : null}

        {detail?.isOwner !== 0 ? (
          <Button block type="primary" onClick={toAdd}>
            <div className="flex h-full w-full items-center justify-center text-white">
              立即添加
            </div>
          </Button>
        ) : null}
      </div>
      <Dialog
        visible={printInfo.open}
        onClose={() => setPrintInfo({ open: false })}
        title="指纹添加中"
        content={
          <div className="flex flex-col items-center py-4">
            <div className="w-40 h-40 bg-gray-200 rounded-md flex items-center justify-center mb-6">
              {/* 指纹图标或动画可以放在这里 */}
              <FingerprintIcon size={100} />
            </div>

            <div className="  space-y-2 text-gray-400">
              <p className="font-medium">1. 请将手指放至指纹感应区域再移开</p>
              <p className="font-medium">2. 重复此步骤以获取完整指纹</p>
            </div>
          </div>
        }
        actions={[
          [
            {
              key: 'cancel',
              text: '取消',
              onClick: () => setPrintInfo({ open: false, value: '' })
            }
            // {
            //   key: 'confirm',
            //   text: '确认',
            //   bold: true,
            //   onClick: () => setPrintInfo({ open: false, value: '' })
            // }
          ]
        ]}
      />

      <Dialog
        visible={inputInfo.open}
        onCancel={() => clearInput()}
        content={
          <div className="flex flex-col items-center justify-center px-2 text-xl">
            <div className="text-center">{inputInfo.name}</div>
            <Input
              maxLength={10}
              placeholder={'请输入' + inputInfo.name}
              className="mb-2 mt-3 h-10 rounded border border-[#e5e5e5] px-2"
              value={inputInfo.value}
              onChange={(value) => setInputInfo({ ...inputInfo, value })}
            />
          </div>
        }
        actions={[
          [
            {
              key: 'cancel',
              text: '取消',
              onClick: () => clearInput()
            },
            {
              key: 'confirm',
              text: '确认',
              onClick: () => handleSubmit(inputInfo.key, inputInfo.value)
            }
          ]
        ]}
      />
    </Page>
  )
}
