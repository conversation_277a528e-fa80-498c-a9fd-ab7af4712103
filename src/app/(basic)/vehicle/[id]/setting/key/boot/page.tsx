'use client';

import { useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { Toast } from 'antd-mobile';
import Page from '@/components/Page';
import List from '@/components/List';
import Button from '@/components/Button';
import { setVehicleSetting } from '@/server/actions/vehicle';
import { getVehicleSetting } from '@/server/api/vehicle';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { state } from '@/store';
import { useBLEClient } from '@/lib/ble';
import { postBluetoothData } from '@/server/api/home';

export default function Password({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const queryClient = useQueryClient();
  const { data: vehicleSetting } = useQuery({
    queryKey: ['vehicleSetting', params.id],
    queryFn: () => getVehicleSetting<Record<string, any>>(params.id),
  });

  const bleClient = useBLEClient();

  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3);

  const handleSubmit = () => {
    /// 蓝牙中控直接用蓝牙设置开机密码
    if (bleClient.isConnected() && !state.hasBleLimit) {
      let password = '00FFFFFF';
      bleClient.setPassword(password, (result: any) => {
        queryClient.setQueryData(['vehicleSetting', params.id], {
          ...vehicleSetting,
          bumpDvcSet: {
            ...vehicleSetting?.bumpDvcSet,
            powerPwdState: 0,
            powerPwd: '',
          },
        });
        postBluetoothData({
          deviceNo: params.id,
          blueName: bleClient.deviceInfo().deviceName,
          blueAddress: bleClient.deviceInfo().deviceMac,
          vcuNo: bleClient.deviceInfo().deviceSN,
          type: 20,
          powerPwdState: 0,
        });

        router.back();
      });
      return;
    } else if (!deviceNetWorkModule) {
      Toast.show('请先连接蓝牙');
      return;
    }

    startTransition(async () => {
      const result = await setVehicleSetting({
        commandType: 1,
        vehicleSetType: 13,
        deviceNo: params.id,
        powerPwdState: 0,
        powerPwd: '123',
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        queryClient.setQueryData(['vehicleSetting', params.id], {
          ...vehicleSetting,
          bumpDvcSet: {
            ...vehicleSetting?.bumpDvcSet,
            powerPwdState: 0,
            powerPwd: '',
          },
        });
        router.back();
      }
    });
  };

  return (
    <Page
      title="开机密码设置"
      className="bg-[url('/images/<EMAIL>')] bg-cover bg-no-repeat"
    >
      <div className="space-y-6 px-3 py-6">
        <List
          items={[
            {
              title: '开机密码',
              value: vehicleSetting?.bumpDvcSet.powerPwdState === 1 ? '已设置' : '未设置',
              href: `boot/modify?powerPwdState=${vehicleSetting?.bumpDvcSet.powerPwdState}`,
            },
          ]}
        />
        {vehicleSetting?.bumpDvcSet.powerPwdState === 1 && (
          <Button block type="primary" loading={isPending} onClick={handleSubmit}>
            关闭密码
          </Button>
        )}
      </div>
    </Page>
  );
}
