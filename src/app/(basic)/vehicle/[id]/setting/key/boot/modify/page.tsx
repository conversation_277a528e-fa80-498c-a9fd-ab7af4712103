'use client';

import { useTransition } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Form, Input, Toast } from 'antd-mobile';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { setVehicleSetting } from '@/server/actions/vehicle';
import { useQueryClient } from '@tanstack/react-query';
import { state } from '@/store';
import { useBLEClient } from '@/lib/ble';
import { postBluetoothData } from '@/server/api/home';

export default function PasswordModify({ params }: { params: { id: string } }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const queryClient = useQueryClient();
  const bleClient = useBLEClient();

  const asciiToHex = (char: string): string => {
    return char.charCodeAt(0).toString(16).padStart(2, '0');
  };

  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3);

  const handleSubmit = (values: any) => {
    /// 蓝牙中控直接用蓝牙设置开机密码

    if (bleClient.isConnected() && !state.hasBleLimit) {
      let password = '01' + values.powerPwd.split('').map(asciiToHex).join('');
      bleClient.setPassword(password, (result: any) => {
        console.log(result);

        postBluetoothData({
          deviceNo: params.id,
          blueName: bleClient.deviceInfo().deviceName,
          blueAddress: bleClient.deviceInfo().deviceMac,
          vcuNo: bleClient.deviceInfo().deviceSN,
          type: 20,
          powerPwdState: 1,
          powerPwd: values.powerPwd,
        });
        router.back();
      });
      return;
    } else if (!deviceNetWorkModule) {
      Toast.show('请先连接蓝牙');
      return;
    }

    startTransition(async () => {
      const previousData: any = queryClient.getQueryData(['vehicleSetting', params.id]);
      queryClient.setQueryData(['vehicleSetting', params.id], {
        ...previousData,
        bumpDvcSet: {
          ...previousData?.bumpDvcSet,
          powerPwdState: 1,
          powerPwd: values.powerPwd,
        },
      });
      const result = await setVehicleSetting({
        commandType: 1,
        vehicleSetType: 13,
        deviceNo: params.id,
        powerPwdState: 1,
        powerPwd: values.powerPwd,
      });
      if (result?.error) {
        Toast.show(result.error);
        queryClient.setQueryData(['vehicleSetting', params.id], previousData);
      } else {
        router.back();
      }
    });
  };

  return (
    <Page className="bg-[url('/images/<EMAIL>')] bg-cover bg-center bg-no-repeat">
      <div className="flex flex-col p-4">
        <div className="text-4xl font-medium">
          {searchParams.get('powerPwdState') === '1' ? '修改' : '设置'}开机密码
        </div>
        <div className="mt-3">密码为3位数字</div>
      </div>
      <div className="px-1">
        <Form
          layout="horizontal"
          mode="card"
          footer={
            <Form.Subscribe to={['powerPwd']}>
              {({ powerPwd }) => {
                return (
                  <Button
                    block
                    type="primary"
                    htmlType="submit"
                    loading={isPending}
                    disabled={!/^\d{3}$/.test(powerPwd)}
                  >
                    确定
                  </Button>
                );
              }}
            </Form.Subscribe>
          }
          onFinish={handleSubmit}
        >
          <Form.Item name="powerPwd">
            <Input placeholder="请输入密码" inputMode="numeric" maxLength={3} />
          </Form.Item>
        </Form>
      </div>
    </Page>
  );
}
