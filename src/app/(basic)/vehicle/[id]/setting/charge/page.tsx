// @ts-nocheck

'use client'

import List from '@/components/List'
import Page from '@/components/Page'
import { useBLEClient } from '@/lib/ble'
import { useNative } from '@/lib/native'
import { postBluetoothData } from '@/server/actions/home'
import { postChargeState } from '@/server/actions/vehicle'
import { getChargeState, getVehicleDetail } from '@/server/api/vehicle'
import { state } from '@/store'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Slider, Toast } from 'antd-mobile'

export default function Charge({ params }: { params: { id: string } }) {
  const bleClient = useBLEClient()
  const queryClient = useQueryClient()
  const { clientId } = useNative()
  const { id } = params

  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3)
  const queryKey = ['charge', id]

  const { data: chargeState } = useQuery({
    queryKey,
    queryFn: () => getChargeState({ deviceNo: id, clientId }),
    enabled: !!params.id
  })
  const { data: detail = {} } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id),
    enabled: !!params.id
  })
  const scheme = detail.scheme || []

  const handleSubmit = async (key: string, value: any) => {
    //TODO 如果蓝牙连接，则直接调用蓝牙接口
    if (bleClient.isConnected()) {
      // 乐观更新
      queryClient.setQueryData(queryKey, { ...chargeState, [key]: value })
      bleClient.setCharge(
        {
          chargePower: chargeState.chargePower,
          chargeCapacity: chargeState.chargeCapacity,
          [key]: value
        },
        (result) => {
          queryClient.setQueryData(queryKey, { ...chargeState, ...result })
          // 上报数据给后端
          postBluetoothData({
            deviceNo: params.id,
            blueName: bleClient.deviceInfo().deviceName,
            blueAddress: bleClient.deviceInfo().deviceMac,
            vcuNo: bleClient.deviceInfo().deviceSN,
            type: 30,
            ...result
          })
        }
      )
    } else if (deviceNetWorkModule) {
      // 乐观更新
      queryClient.setQueryData(queryKey, { ...chargeState, [key]: value })
      //TODO 如果蓝牙未连接，则使用网络请求
      const result = await postChargeState({
        deviceNo: params.id,
        ...chargeState,
        [key]: value
      })
      if (result?.error) {
        Toast.show(result.error)
      }
    } else {
      //TODO 如果蓝牙未连接并不能用网络，则提示用户连接蓝牙
      Toast.show('请先连接蓝牙')
    }
  }

  return (
    <Page title="充电设置">
      <div className="space-y-6 px-3 py-6">
        <List
          items={[
            {
              key: 1,
              title: '最大充电功率设置',
              value: (chargeState?.chargePower || 600) + 'W',
              show: scheme.includes('charge_power'),
              footer: chargeState ? (
                <div className="px-2">
                  <Slider
                    min={100}
                    max={3000}
                    step={10}
                    defaultValue={[chargeState?.chargePower ?? 600]}
                    onAfterChange={(value) => handleSubmit('chargePower', value)}
                    style={{ overflow: 'hidden' }}
                  />
                  <div
                    className="flex justify-between"
                    style={{ marginTop: 3, fontSize: 13, color: '#908f94' }}
                  >
                    <span>100W</span>
                    <span>3000W</span>
                  </div>
                </div>
              ) : (
                <div style={{ height: '51px' }} />
              )
            }
          ]}
        />

        <List
          items={[
            {
              key: 1,
              title: '充电SOC上限设置',
              value: (chargeState?.chargeCapacity || '90') + '%',
              show: scheme.includes('charge_soc'),
              footer: chargeState ? (
                <div className="px-2">
                  <Slider
                    min={80}
                    max={100}
                    step={1}
                    defaultValue={[chargeState?.chargeCapacity ?? 90]}
                    onAfterChange={(value) => handleSubmit('chargeCapacity', value)}
                    style={{ overflow: 'hidden' }}
                  />
                  <div
                    className="flex justify-between"
                    style={{ marginTop: 3, fontSize: 13, color: '#908f94' }}
                  >
                    <span>80%</span>
                    <span>100%</span>
                  </div>
                </div>
              ) : (
                <div style={{ height: '51px' }} />
              )
            }
          ]}
        />
      </div>
    </Page>
  )
}
