'use client';

import { useState, useEffect } from 'react';
import { Toast } from 'antd-mobile';
import Page from '@/components/Page';
import List from '@/components/List';
import Switch from '@/components/Switch';
import Slider from '@/components/Slider';
import { useBLEClient } from '@/lib/ble';
import type { DistanceEnum } from '@/lib/ble-jssdk1.0';
import { postBluetoothData } from '@/server/actions/home';
import { getHidSet } from '@/server/api/home';
import { useQuery } from '@tanstack/react-query';
import { useNative } from '@/lib/native';

export default function Hid({ params }: { params: { id: string } }) {
  const [checked, setChecked] = useState(0);
  const [sliderValue, setSliderValue] = useState<DistanceEnum>(3);
  const bleClient = useBLEClient();
  const { clientId } = useNative();

  const { data: hidStatus } = useQuery({
    queryKey: ['hid', params.id],
    queryFn: () => getHidSet({ deviceNo: params.id, clientId }),
    enabled: !bleClient.isConnected(),
  });

  useEffect(() => {
    // 如果蓝牙已连接
    if (bleClient.isConnected()) {
      // 下发9b指令，查询设备状态
      bleClient.getDeviceState((result) => {
        console.log('设备状态 :>> ', JSON.stringify(result));
        // 设备状态
        setChecked(result.hid);
        setSliderValue(result.distance);
      });
    } else if (hidStatus) {
      // 如果蓝牙未连接，通过接口获取设备状态
      setChecked(hidStatus?.unlockAutoState);
      setSliderValue(hidStatus?.unlockAutoDistance);
    }
  }, [bleClient, hidStatus]);

  const handleSubmit = async (key: string, value: any) => {
    console.log('感应key :>> ', key, typeof value);
    if (bleClient.isConnected()) {
      if (key === 'state') {
        bleClient.setHid(value ? 1 : 0, (res) => {
          setChecked(res);
        });
      } else {
        bleClient.setHidDistance(value, (res) => {
          setSliderValue(res);
        });
      }
      // 上报数据
      const result = await postBluetoothData({
        deviceNo: params.id,
        blueName: bleClient.deviceInfo().deviceName,
        blueAddress: bleClient.deviceInfo().deviceMac,
        vcuNo: bleClient.deviceInfo().deviceSN,
        type: 0,
        state: checked,
        distance: sliderValue,
        [key]: value,
        clientId,
      });
    } else {
      Toast.show('请先连接蓝牙');
    }
  };

  return (
    <Page title="感应解锁设置">
      <div className="px-3 py-6">
        <List
          items={[
            {
              key: 1,
              title: '感应解锁',
              description: '打开后，携带手机靠近车辆即可开机',
              value: (
                <Switch
                  defaultChecked={false}
                  checked={checked === 1}
                  onChange={(checked) => handleSubmit('state', checked ? 1 : 0)}
                />
              ),
            },
            {
              key: 2,
              title: '感应解锁距离',
              footer: (
                <div className="px-6">
                  <Slider
                    min={1}
                    max={5}
                    defaultValue={[sliderValue ?? 3]}
                    onValueCommit={(value: DistanceEnum[]) => handleSubmit('distance', value[0])}
                    marks={[
                      {
                        value: 1,
                        label: '近',
                      },
                      {
                        value: 3,
                        label: '标准',
                      },
                      {
                        value: 5,
                        label: '远',
                      },
                    ]}
                  />
                </div>
              ),
              show: checked === 1,
            },
          ]}
        />
      </div>
    </Page>
  );
}
