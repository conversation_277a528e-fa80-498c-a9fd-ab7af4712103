'use client';

import { useTransition } from 'react';
import { Toast } from 'antd-mobile';
import Page from '@/components/Page';
import List from '@/components/List';
import Switch from '@/components/Switch';
import Slider from '@/components/Slider';
import { setVehicleSetting } from '@/server/actions/vehicle';
import { getVehicleSetting } from '@/server/api/vehicle';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { state } from '@/store';
import { useBLEClient } from '@/lib/ble';
import { postBluetoothData } from '@/server/api/home';

export default function Energy({ params }: { params: { id: string } }) {
  const [isPending, startTransition] = useTransition();
  const queryClient = useQueryClient();
  const { data: vehicleSetting } = useQuery({
    queryKey: ['vehicleSetting', params.id],
    queryFn: () => getVehicleSetting<Record<string, any>>(params.id),
  });

  const bleClient = useBLEClient();
  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3);

  const handleSubmit = (key: string, value: any) => {
    if (bleClient.isConnected()) {
      bleClient.setEnergyRecovery(
        {
          energyRecovery: vehicleSetting?.bumpDvcSet.energyRecovery,
          energyLevel: vehicleSetting?.bumpDvcSet.energyLevel,
          [key]: value,
        },
        (result: number[]) => {
          queryClient.setQueryData(['vehicleSetting', params.id], {
            ...vehicleSetting,
            bumpDvcSet: {
              ...vehicleSetting?.bumpDvcSet,
              energyRecovery: result[0],
              energyLevel: result[1],
            },
          });
          postBluetoothData({
            deviceNo: params.id,
            blueName: bleClient.deviceInfo().deviceName,
            blueAddress: bleClient.deviceInfo().deviceMac,
            vcuNo: bleClient.deviceInfo().deviceSN,
            type: 14,
            energyRecovery: result[0],
            energyLevel: result[1],
          });
        }
      );
      return;
    } else if (!deviceNetWorkModule) {
      Toast.show('请先连接蓝牙');
      return;
    }

    startTransition(async () => {
      queryClient.setQueryData(['vehicleSetting', params.id], {
        ...vehicleSetting,
        bumpDvcSet: {
          ...vehicleSetting?.bumpDvcSet,
          [key]: value,
        },
      });
      const result = await setVehicleSetting({
        commandType: 1,
        vehicleSetType: 12,
        deviceNo: params.id,
        energyRecovery: vehicleSetting?.bumpDvcSet.energyRecovery,
        energyLevel: vehicleSetting?.bumpDvcSet.energyLevel,
        [key]: value,
      });
      if (result?.error) {
        Toast.show(result.error);
        queryClient.setQueryData(['vehicleSetting', params.id], vehicleSetting);
      }
    });
  };

  return (
    <Page title="能量回收强度">
      <div className="px-3 py-6">
        <List
          items={[
            {
              key: 1,
              title: '能量回收开关',
              value: (
                <Switch
                  checked={vehicleSetting?.bumpDvcSet.energyRecovery === 1}
                  onChange={(checked) => {
                    handleSubmit('energyRecovery', checked ? 1 : 0);
                  }}
                />
              ),
            },
            {
              key: 2,
              title: '能量回收强度',
              footer: (
                <div className="px-6">
                  <Slider
                    defaultValue={[vehicleSetting?.bumpDvcSet.energyLevel]}
                    onValueCommit={(value) => {
                      handleSubmit('energyLevel', value[0]);
                    }}
                    max={2}
                    marks={[
                      {
                        value: 0,
                        label: '弱',
                      },
                      {
                        value: 1,
                        label: '中',
                      },
                      {
                        value: 2,
                        label: '强',
                      },
                    ]}
                  />
                </div>
              ),
              show: vehicleSetting?.bumpDvcSet.energyRecovery === 1,
            },
          ]}
        />
      </div>
    </Page>
  );
}
