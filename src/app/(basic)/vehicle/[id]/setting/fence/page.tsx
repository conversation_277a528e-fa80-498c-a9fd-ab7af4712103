/*
 * @Author: <PERSON><PERSON>
 * @Email: <EMAIL>
 * @Date: 2025-03-13 14:29:48
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-03-18 10:11:24
 */
'use client'

import List from '@/components/List'
import Page from '@/components/Page'
import Slider from '@/components/Slider'
import Switch from '@/components/Switch'
import { setVehicleSetting } from '@/server/actions/vehicle'
import { getVehicleDetail, getVehicleSetting } from '@/server/api/vehicle'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Toast } from 'antd-mobile'
import { useTransition } from 'react'

const distanceArr = [300, 500, 1000]

export default function Fence({ params }: { params: { id: string } }) {
  const [isPending, startTransition] = useTransition()
  const queryClient = useQueryClient()

  const { data: vehicleSetting } = useQuery({
    queryKey: ['vehicleSetting', params.id],
    queryFn: () => getVehicleSetting<Record<string, any>>(params.id)
  })

  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id),
    enabled: !!params.id
  })

  const handleSubmit = (key: string, value: any) => {
    startTransition(async () => {
      if (key === 'eleFenceDistance') {
        value = distanceArr[value]
      }
      queryClient.setQueryData(['vehicleSetting', params.id], {
        ...vehicleSetting,
        bumpDvcSet: {
          ...vehicleSetting?.bumpDvcSet,
          [key]: value
        }
      })
      const result = await setVehicleSetting({
        commandType: 1,
        vehicleSetType: 3,
        deviceNo: params.id,
        eleFenceState: vehicleSetting?.bumpDvcSet.eleFenceState,
        eleFenceDistance: vehicleSetting?.bumpDvcSet.eleFenceDistance,
        scheme: detail?.scheme.includes('electronic_fence_platform')
          ? 'electronic_fence_platform'
          : 'electronic_fence',
        [key]: value
      })
      if (result?.error) {
        Toast.show(result.error)
        queryClient.setQueryData(['vehicleSetting', params.id], vehicleSetting)
      }
    })
  }

  return (
    <Page title="电子围栏设置">
      <div className="px-3 py-6">
        <List
          items={[
            {
              key: 1,
              title: '电子围栏',
              description: '打开后，车辆超出有效范围自动触发报警',
              value: (
                <Switch
                  checked={vehicleSetting?.bumpDvcSet.eleFenceState === 1}
                  onChange={(checked) => {
                    handleSubmit('eleFenceState', checked ? 1 : 0)
                  }}
                />
              )
            },
            {
              key: 2,
              title: '电子围栏有效范围',
              footer: (
                <div className="px-6">
                  <Slider
                    max={2}
                    marks={[
                      { value: 0, label: '300m' },
                      { value: 1, label: '500m' },
                      { value: 2, label: '1000m' }
                    ]}
                    defaultValue={[
                      distanceArr.indexOf(vehicleSetting?.bumpDvcSet.eleFenceDistance || 300)
                    ]}
                    onValueCommit={(value) => handleSubmit('eleFenceDistance', value[0])}
                  />
                </div>
              ),
              show: vehicleSetting?.bumpDvcSet.eleFenceState === 1
            }
          ]}
        />
      </div>
    </Page>
  )
}
