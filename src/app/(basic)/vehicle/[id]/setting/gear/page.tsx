'use client';

import { Picker, Toast } from 'antd-mobile';
import Page from '@/components/Page';
import List from '@/components/List';
import Switch from '@/components/Switch';
import { setVehicleSetting } from '@/server/actions/vehicle';
import { getVehicleSetting } from '@/server/api/vehicle';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { state } from '@/store';
import { useBLEClient } from '@/lib/ble';
import { postBluetoothData } from '@/server/api/home';

export default function Gear({ params }: { params: { id: string } }) {
  const queryClient = useQueryClient();
  const { data: vehicleSetting } = useQuery({
    queryKey: ['vehicleSetting', params.id],
    queryFn: () => getVehicleSetting<Record<string, any>>(params.id),
  });

  const bleClient = useBLEClient();
  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3);

  const handleSubmit = async (key: string, value: any) => {
    if (bleClient.isConnected()) {
      bleClient.setGear(
        {
          controllerShift: vehicleSetting?.bumpDvcSet.controllerShift,
          controllerGearPosition: vehicleSetting?.bumpDvcSet.controllerGearPosition,
          controllerSpeedUp: vehicleSetting?.bumpDvcSet.controllerSpeedUp,
          [key]: value,
        },
        (result: number[]) => {
          console.log('result', result);
          queryClient.setQueryData(['vehicleSetting', params.id], {
            ...vehicleSetting,
            bumpDvcSet: {
              ...vehicleSetting?.bumpDvcSet,
              controllerShift:
                result[0] === 3 ? vehicleSetting?.bumpDvcSet.controllerShift : result[0],
              controllerGearPosition:
                result[1] === 3 ? vehicleSetting?.bumpDvcSet.controllerGearPosition : result[1],
              controllerSpeedUp:
                result[2] === 3 ? vehicleSetting?.bumpDvcSet.controllerSpeedUp : result[2],
            },
          });
          postBluetoothData({
            deviceNo: params.id,
            blueName: bleClient.deviceInfo().deviceName,
            blueAddress: bleClient.deviceInfo().deviceMac,
            vcuNo: bleClient.deviceInfo().deviceSN,
            type: 13,
            gearPosition: result[1],
          });
          postBluetoothData({
            deviceNo: params.id,
            blueName: bleClient.deviceInfo().deviceName,
            blueAddress: bleClient.deviceInfo().deviceMac,
            vcuNo: bleClient.deviceInfo().deviceSN,
            type: 10,
            shift: result[0],
          });
          postBluetoothData({
            deviceNo: params.id,
            blueName: bleClient.deviceInfo().deviceName,
            blueAddress: bleClient.deviceInfo().deviceMac,
            vcuNo: bleClient.deviceInfo().deviceSN,
            type: 9,
            speedUp: result[2],
          });
        }
      );
      return;
    } else if (!deviceNetWorkModule) {
      Toast.show('请先连接蓝牙');
      return;
    }

    queryClient.setQueryData(['vehicleSetting', params.id], {
      ...vehicleSetting,
      bumpDvcSet: {
        ...vehicleSetting?.bumpDvcSet,
        [key]: value,
      },
    });

    const result = await setVehicleSetting({
      commandType: 1,
      vehicleSetType: 11,
      deviceNo: params.id,
      controllerShift: vehicleSetting?.bumpDvcSet.controllerShift,
      controllerGearPosition: vehicleSetting?.bumpDvcSet.controllerGearPosition,
      controllerSpeedUp: vehicleSetting?.bumpDvcSet.controllerSpeedUp,
      [key]: value,
    });
    if (result?.error) {
      Toast.show(result.error);
      queryClient.setQueryData(['vehicleSetting', params.id], vehicleSetting);
    }
  };

  return (
    <Page title="挡位设置">
      <div className="space-y-4 px-3 py-6">
        <List
          items={[
            {
              key: 1,
              title: '挡位模式',
              description: '开启设置手动挡，关闭为自动挡',
              value: (
                <Switch
                  checked={vehicleSetting?.bumpDvcSet.controllerShift === 1}
                  onChange={(checked) => {
                    handleSubmit('controllerShift', checked ? 1 : 0);
                  }}
                />
              ),
            },
            {
              key: 2,
              title: '默认挡位',
              onClick: (_, valueChildRef: any) => {
                valueChildRef.current?.open();
              },
              value: (
                <Picker
                  columns={[
                    [
                      { label: '一挡', value: 0 },
                      { label: '二挡', value: 1 },
                      { label: '三挡', value: 2 },
                    ],
                  ]}
                  cancelText="取消"
                  value={[vehicleSetting?.bumpDvcSet.controllerGearPosition]}
                  onConfirm={(value) => {
                    handleSubmit('controllerGearPosition', value[0]);
                  }}
                >
                  {(_) => {
                    return <span>{_[0]?.label}</span>;
                  }}
                </Picker>
              ),
              arrow: true,
              show: vehicleSetting?.bumpDvcSet.controllerShift === 1,
            },
          ]}
        />
        <List
          items={[
            {
              title: '起步速度',
              onClick: (_, valueChildRef) => {
                valueChildRef.current?.open();
              },
              value: (
                <Picker
                  columns={[
                    [
                      { label: '缓启动', value: 0 },
                      { label: '快启动', value: 1 },
                    ],
                  ]}
                  cancelText="取消"
                  value={[vehicleSetting?.bumpDvcSet.controllerSpeedUp]}
                  onConfirm={(value) => {
                    handleSubmit('controllerSpeedUp', value[0]);
                  }}
                >
                  {(_) => {
                    return <span>{_[0]?.label}</span>;
                  }}
                </Picker>
              ),
              arrow: true,
            },
          ]}
        />
      </div>
    </Page>
  );
}
