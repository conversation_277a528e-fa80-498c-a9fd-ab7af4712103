'use client'

import List from '@/components/List'
import Page from '@/components/Page'
import Slider from '@/components/Slider'
import Switch from '@/components/Switch'
import { useBLEClient } from '@/lib/ble'
import { setVehicleSetting } from '@/server/actions/vehicle'
import { postBluetoothData } from '@/server/api/home'
import { getVehicleDetail, getVehicleSetting } from '@/server/api/vehicle'
import { state } from '@/store'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Toast } from 'antd-mobile'
import { useTransition } from 'react'

export default function Autolock({ params }: { params: { id: string } }) {
  const [isPending, startTransition] = useTransition()

  const queryClient = useQueryClient()
  const { data: vehicleSetting } = useQuery({
    queryKey: ['vehicleSetting', params.id],
    queryFn: () => getVehicleSetting<Record<string, any>>(params.id)
  })
  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id),
    enabled: !!params.id
  })
  console.log('[ detail ] >', detail)

  const bleClient = useBLEClient()
  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3)

  const handleSubmit = (key: string, value: number) => {
    if (bleClient.isConnected() && !state.hasBleLimit) {
      let checked: number = key === 'lockAutoState' ? value : 1
      let time =
        key === 'lockAutoTime' ? (value as number) : vehicleSetting?.bumpDvcSet.lockAutoTime
      if (detail?.scheme.includes('auto_lock_star')) {
        const timeMap: any = { 1: 3, 2: 5, 3: 10 }
        time = timeMap[time] || time
      }
      bleClient.setLockAuto(checked, time, (result: number[]) => {
        queryClient.setQueryData(['vehicleSetting', params.id], {
          ...vehicleSetting,
          bumpDvcSet: {
            ...vehicleSetting?.bumpDvcSet,
            lockAutoState: result[0],
            lockAutoTime: value
          }
        })
        postBluetoothData({
          deviceNo: params.id,
          blueName: bleClient.deviceInfo().deviceName,
          blueAddress: bleClient.deviceInfo().deviceMac,
          vcuNo: bleClient.deviceInfo().deviceSN,
          type: 23,
          autoLockState: result[0],
          autoLockTime: value,
          scheme: detail?.scheme.includes('auto_lock_star') ? 'auto_lock_star' : 'auto_lock'
        })
      })
      return
    } else if (!deviceNetWorkModule) {
      Toast.show('请先连接蓝牙')
      return
    }

    startTransition(async () => {
      queryClient.setQueryData(['vehicleSetting', params.id], {
        ...vehicleSetting,
        bumpDvcSet: {
          ...vehicleSetting?.bumpDvcSet,
          [key]: value
        }
      })

      const result = await setVehicleSetting({
        commandType: 1,
        vehicleSetType: 2,
        deviceNo: params.id,
        lockAutoState: vehicleSetting?.bumpDvcSet.lockAutoState,
        lockAutoTime: vehicleSetting?.bumpDvcSet.lockAutoTime,
        [key]: value,
        scheme: detail?.scheme.includes('auto_lock_star') ? 'auto_lock_star' : 'auto_lock'
      })
      if (result?.error) {
        Toast.show(result.error)
        queryClient.setQueryData(['vehicleSetting', params.id], vehicleSetting)
      }
    })
  }

  return (
    <Page title="自动锁车设置">
      <div className="px-3 py-6">
        <List
          items={[
            {
              key: 1,
              title: '自动锁车',
              description: '打开后，手机离开车辆范围即可关机',
              value: (
                <Switch
                  checked={vehicleSetting?.bumpDvcSet.lockAutoState === 1}
                  onChange={(checked) => handleSubmit('lockAutoState', checked ? 1 : 0)}
                />
              )
            },
            {
              key: 2,
              title: '自动锁车时间',
              footer: (
                <div className="px-6">
                  <Slider
                    min={1}
                    max={detail?.scheme.includes('auto_lock_star') ? 3 : 5}
                    marks={
                      detail?.scheme.includes('auto_lock_star')
                        ? [
                            { value: 1, label: '3s' },
                            { value: 2, label: '5s' },
                            { value: 3, label: '10s' }
                          ]
                        : [
                            { value: 1, label: '5s' },
                            { value: 2, label: '15s' },
                            { value: 3, label: '30s' },
                            { value: 4, label: '60s' },
                            { value: 5, label: '120s' }
                          ]
                    }
                    defaultValue={[vehicleSetting?.bumpDvcSet.lockAutoTime]}
                    onValueCommit={(value) => handleSubmit('lockAutoTime', value[0])}
                  />
                </div>
              ),
              show: vehicleSetting?.bumpDvcSet.lockAutoState === 1
            }
          ]}
        />
      </div>
    </Page>
  )
}
