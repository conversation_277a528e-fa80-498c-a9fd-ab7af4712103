// @ts-nocheck
'use client'

import Button from '@/components/Button'
import Page from '@/components/Page'
import SafeArea from '@/components/SafeArea'
import { useAmap } from '@/hooks/useAmap'
import { useNative } from '@/lib/native'
import { sendCommand } from '@/server/actions/home'
import { getExpireTime } from '@/server/api/service'
import { getVehicleDetail, getVehicleStatus } from '@/server/api/vehicle'
import { toImg } from '@/utils'
import * as AspectRatio from '@radix-ui/react-aspect-ratio'
import { useQuery } from '@tanstack/react-query'
import { ActionSheet, Toast } from 'antd-mobile'
import type { Action } from 'antd-mobile/es/components/action-sheet'
import dayjs from 'dayjs'
import Image from 'next/image'
import Link from 'next/link'
import { useEffect, useMemo, useState, useTransition } from 'react'

export default function Position({ params }: { params: { id: string } }) {
  const { elRef, map } = useAmap()
  const [address, setAddress] = useState('暂无车辆位置信息')
  const [visible, setVisible] = useState(false)
  const [isPending, startTransition] = useTransition()
  const { safeArea } = useNative()

  const { data } = useQuery({
    queryKey: ['vehicle', 'current', params.id],
    queryFn: () => getVehicleStatus(params.id),
    refetchInterval: (query) => {
      // 如果车辆开启，每8秒刷新一次
      if (query.state.data?.isDeviceLock === 1) {
        return 8 * 1000
      }
      return false
    }
  })

  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id)
  })

  const { data: expired } = useQuery({
    queryKey: ['vehicle-info', params.id],
    queryFn: () => getExpireTime(params.id)
  })

  const hasLocation = useMemo(
    () => data?.longitude && data?.longitude !== '0' && data?.latitude !== '0',
    [data]
  )

  useEffect(() => {
    if (map) {
      // 删除地图上所有的覆盖物
      map.clearMap()
      window.jsBridgeHelper?.sendMessage('queryLocation').then((res: any) => {
        if (res.code === '200') {
          const userMarker = new AMap.Marker({
            position: [res.data.longitude, res.data.latitude] as unknown as AMap.LngLatLike,
            icon: new AMap.Icon({
              size: new AMap.Size(28, 28),
              imageSize: new AMap.Size(28, 28),
              image: '/images/<EMAIL>'
            }),
            offset: new AMap.Pixel(-14, -14)
          })
          map.add([userMarker])
        }
      })
      if (hasLocation) {
        const position = [data?.longitude, data?.latitude] as unknown as AMap.LngLatLike
        const vehicleMarker = new AMap.Marker({
          position,
          icon: new AMap.Icon({
            size: new AMap.Size(34, 44),
            imageSize: new AMap.Size(34, 44),
            image: '/images/<EMAIL>'
          }),
          offset: new AMap.Pixel(-17, -44)
        })
        map.add([vehicleMarker])
        // @ts-ignore
        const geocoder = new AMap.Geocoder({
          city: '全国'
        })

        geocoder.getAddress(position, function (status: string, result: any) {
          if (status === 'complete' && result.regeocode) {
            const address = result.regeocode.formattedAddress
            setAddress(address)
          }
        })
      }
      map.setFitView()
    }
  }, [map, data, hasLocation])

  const handleVehicleView = () => {
    map?.setZoomAndCenter(18, [data?.longitude, data?.latitude] as unknown as AMap.LngLatLike)
  }

  const handleUserView = () => {
    window.jsBridgeHelper?.sendMessage('queryLocation').then((res: any) => {
      if (res.code === '200') {
        map?.setZoomAndCenter(18, [
          res.data.longitude,
          res.data.latitude
        ] as unknown as AMap.LngLatLike)
      }
    })
  }

  const handleAction = (action: Action) => {
    window.jsBridgeHelper?.sendMessage(action.key, { lat: data?.latitude, lon: data?.longitude })
    setVisible(false)
  }

  return (
    <Page title="车辆定位" className="bg-white">
      <div className="h-full" ref={elRef}></div>
      <div
        className="absolute right-4 flex-col space-y-3"
        style={{ bottom: safeArea.bottom + 209 }}
      >
        {detail?.scheme.includes('lose') && detail?.isOwner === 1 && (
          <Link href={`setting/security/lost?state=${data?.lose}`}>
            <div className="w-10 rounded-xl bg-white p-2">
              <Image
                src={`/images/icon-lock${data?.lose ? '' : '-k'}@2x.png`}
                alt="lock"
                width={24}
                height={24}
              />
            </div>
          </Link>
        )}
        <div className="w-10 rounded-xl bg-white p-2" onClick={handleVehicleView}>
          <Image src="/images/<EMAIL>" alt="lock" width={24} height={24} />
        </div>
        <div className=" w-10 rounded-xl bg-white p-2" onClick={handleUserView}>
          <Image src="/images/<EMAIL>" alt="lock" width={24} height={24} />
        </div>
      </div>
      <div className="absolute bottom-0 left-0 right-0 z-[160]">
        <div className="rounded-2xl bg-white p-4">
          <div className="flex items-center">
            <div className="my-3 h-[70px] w-[70px] rounded-md bg-[#f6f7f8]">
              <div className="relative w-[90px] -translate-x-[10px]">
                <AspectRatio.Root ratio={482 / 375}>
                  <Image
                    src={data?.img ? toImg(data.img) : '/images/<EMAIL>'}
                    alt="车型图"
                    fill
                    priority
                  />
                </AspectRatio.Root>
              </div>
            </div>
            <div className="ml-4 flex-1 space-y-2 text-xs">
              <div className="line-clamp-2 font-medium leading-normal">{address}</div>
              <div className="text-color-weak">
                {expired?.isExpired
                  ? '--'
                  : data?.gmtGps && dayjs(data.gmtGps).format('YYYY-MM-DD HH:mm:ss')}
              </div>
              <div className="flex">
                {data?.isDeviceLock === 0 && (
                  <div className="flex-1 text-color-weak">
                    已驻车
                    <span className="text-base text-color-text">
                      {expired?.isExpired ? '--' : data?.parkingTime || 0}
                    </span>
                    分钟
                  </div>
                )}
                <div className="flex space-x-2">
                  <Image
                    src={`/images/gps-small0${
                      expired?.isExpired || detail?.isOnline === 0 ? 0 : data?.gpsSignal || 0
                    }@2x.png`}
                    alt="gps"
                    width={20}
                    height={20}
                  />
                  <Image
                    src={`/images/gms-small0${
                      expired?.isExpired || detail?.isOnline === 0 ? 0 : data?.gsmSignal || 0
                    }@2x.png`}
                    alt="gms"
                    width={20}
                    height={20}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-2">
            {/* <Button
              block
              type="default"
              onClick={() => setVisible(true)}
              style={{ borderColor: '#2fb8ff' }}
            >
              导航到车
            </Button> */}
            <div
              data-track="车辆定位-导航到车"
              className="flex w-full items-center justify-center rounded-lg p-3 text-xl text-[#2fb8ff]"
              style={{ border: '1px solid #2fb8ff' }}
              onClick={() => setVisible(true)}
            >
              导航到车
            </div>
            {detail?.scheme.includes('bell') && (
              <Button
                block
                type="primary"
                onClick={() => {
                  startTransition(async () => {
                    const result = await sendCommand({
                      deviceNo: params.id,
                      status: 1,
                      type: 3
                    })
                    if (result?.error) {
                      Toast.show(result.error)
                    }
                  })
                }}
              >
                寻车铃
              </Button>
            )}
          </div>
          <SafeArea position="bottom" />
          <ActionSheet
            cancelText="取消"
            visible={visible}
            actions={[
              { text: '腾讯地图', key: 'txLauncher' },
              {
                text: '百度地图',
                key: 'bdLauncher'
              },
              {
                text: '高德地图',
                key: 'amapLauncher'
              }
            ]}
            onClose={() => setVisible(false)}
            onAction={handleAction}
          />
        </div>
      </div>
    </Page>
  )
}
