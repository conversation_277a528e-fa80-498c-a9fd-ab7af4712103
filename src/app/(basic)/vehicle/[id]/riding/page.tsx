'use client';

import { useState, useMemo, Fragment, useTransition } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import Page from '@/components/Page';
import ImageWithFallback from '@/components/ImageWithFallback';
import { Tabs } from 'antd-mobile';
import dayjs from 'dayjs';
import MonthPicker from '@/components/MonthPicker';
import { useQuery } from '@tanstack/react-query';
import { getVehicleDetail } from '@/server/api/vehicle';
import { getRidingRecord } from '@/server/api/riding';
import { toImg } from '@/utils';
import Chart from './components/Chart';

const weakly = ['日', '一', '二', '三', '四', '五', '六'];

export default function Riding({ params }: { params: { id: string } }) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const [lastMonth, setLastMonth] = useState(dayjs(searchParams.get('month') || dayjs()));
  const [activeKey, setActiveKey] = useState(
    searchParams.get('active') || lastMonth.format('YYYY-MM')
  );
  const nearbyThreeMonths = useMemo(() => {
    return [
      lastMonth,
      dayjs(lastMonth).subtract(1, 'month'),
      dayjs(lastMonth).subtract(2, 'month'),
    ];
  }, [lastMonth]);
  // 查询时间访问
  const range = useMemo(() => {
    return {
      searchBgnTime: dayjs(activeKey).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      searchEndTime: dayjs(activeKey).endOf('month').format('YYYY-MM-DD HH:mm:ss'),
    };
  }, [activeKey]);

  const { data } = useQuery({
    queryKey: ['riding', { deviceNo: params.id, ...range }],
    queryFn: () =>
      getRidingRecord({
        deviceNo: params.id,
        ...range,
      }),
  });

  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id),
  });

  // 按天进行分组
  const historyByDay = useMemo(() => {
    return data?.history?.reduce((acc, cur) => {
      const day = dayjs(cur.gmtStart).format('YYYY-MM-DD');
      const dayOfweek = '星期' + weakly[dayjs(cur.gmtStart).day()];
      const index = acc.findIndex((item: any) => item.day === day);
      if (index === -1) {
        acc.push({
          day,
          dayOfweek,
          data: [cur],
        });
      } else {
        acc[index].data.push(cur);
      }
      return acc;
    }, []);
  }, [data]);

  return (
    <Page
      title="骑行记录"
      className="bg-[url('/images/<EMAIL>')] bg-cover bg-center bg-no-repeat"
    >
      <div className="flex items-center border-b border-color-border">
        <Tabs
          data-track="骑行记录-月份切换"
          stretch={false}
          className="riding flex-1"
          activeKey={activeKey}
          onChange={(key) => {
            setActiveKey(key);
            const updatedSearchParams = new URLSearchParams(searchParams.toString());
            updatedSearchParams.set('active', key);
            startTransition(() => {
              router.replace(`${pathname}?${updatedSearchParams.toString()}`);
            });
          }}
        >
          {nearbyThreeMonths.map((item) => (
            <Tabs.Tab title={item.format('MM月')} key={item.format('YYYY-MM')} />
          ))}
        </Tabs>
        <MonthPicker
          value={lastMonth}
          onChange={(value) => {
            setLastMonth(value);
            setActiveKey(value.format('YYYY-MM'));
            const updatedSearchParams = new URLSearchParams(searchParams.toString());
            updatedSearchParams.set('month', value.format('YYYY-MM'));
            updatedSearchParams.set('active', value.format('YYYY-MM'));
            startTransition(() => {
              router.replace(`${pathname}?${updatedSearchParams.toString()}`);
            });
          }}
          disabledDate={(currentDate) => currentDate.isAfter(dayjs(), 'month')}
        >
          <Image
            src="/images/<EMAIL>"
            alt="calendar"
            width={24}
            height={24}
            className="mx-3 -translate-y-[3px]"
          />
        </MonthPicker>
      </div>
      <div className="p-4">
        {detail?.scheme.includes('all_mileage') && (
          <div>
            <div className="text-7xl font-bold">
              {data?.mileage || 0}
              <span className="text-xs font-medium text-color-weak">&nbsp;km</span>
            </div>
            <div className="mt-1 text-color-weak">当月总骑行里程</div>
          </div>
        )}
        <div className="mt-3 grid grid-cols-4 gap-2">
          {detail?.scheme.includes('riding_time') && (
            <div className="p-2">
              <div className="mb-1.5 font-bold">
                {data?.time || 0}
                <span className="text-xs font-medium text-color-weak">&nbsp;min</span>
              </div>
              <div className="text-3xs text-color-weak">骑行时间</div>
            </div>
          )}
          {detail?.scheme.includes('average_speed') && (
            <div className="p-2">
              <div className="mb-1.5 font-bold">
                {data?.avgSpeed || 0}
                <span className="ml-1 text-3xs font-medium text-color-weak">km/h</span>
              </div>
              <div className="text-3xs text-color-weak">平均速度</div>
            </div>
          )}
          {detail?.scheme.includes('maximum_speed') && (
            <div className="p-2">
              <div className="mb-1.5 font-bold">
                {data?.highSpeed || 0}
                <span className="ml-1 text-3xs font-medium text-color-weak">km/h</span>
              </div>
              <div className="text-3xs text-color-weak">最高速度</div>
            </div>
          )}
          {detail?.scheme.includes('ride_consume') && (
            <div className="p-2">
              <div className="mb-1.5 font-bold">
                {data?.powerConsume}
                <span className="ml-1 text-3xs font-medium text-color-weak">wh</span>
              </div>
              <div className="text-3xs text-color-weak">骑行功耗</div>
            </div>
          )}
        </div>
        {detail?.scheme.includes('ride_static') && (
          <div className="mt-8 rounded-2xl bg-white p-4">
            <Chart data={data} activeKey={activeKey} />
          </div>
        )}
        {historyByDay?.length === 0 && (
          <div className="mt-2 flex flex-col  items-center">
            <Image src="/images/empty-content.png" alt="无骑行轨迹" width={200} height={200} />
            <span className="mt-3 text-xs text-color-weak">暂无记录</span>
          </div>
        )}
        {detail?.scheme.includes('ride_list') &&
          historyByDay?.map(
            (item: { day: string; dayOfweek: string; data: any[] }, index: number) => (
              <Fragment key={item.day}>
                <div className="mt-2 flex items-center justify-between py-2">
                  <div className="text-sm text-color-weak">
                    {item.day}&nbsp;{item.dayOfweek}
                  </div>
                  <div>{data?.dayMap?.[item.day]}km</div>
                </div>
                <div className="space-y-3 rounded-xl bg-white p-4" key={index}>
                  {item.data?.map((riding, index) => (
                    <div
                      className="flex"
                      onClick={() => {
                        // 骑行轨迹
                        if (detail?.scheme.includes('ride_track')) {
                          router.push(`riding/${riding.id}`);
                        }
                      }}
                      key={index}
                    >
                      <div className="h-20 w-20 rounded-md">
                        <ImageWithFallback
                          src={toImg(riding.thumbnail || '')}
                          fallback="/images/riding-thumbnail.png"
                          alt="缩略图"
                          width={80}
                          height={80}
                          className="rounded-md"
                        />
                      </div>
                      <div className="ml-3 flex-1">
                        <div className="text-3xs text-color-weak">
                          {dayjs(riding.gmtStart).format('MM-DD HH:mm')}
                        </div>
                        {detail?.scheme.includes('mileage') && (
                          <div className="text-5xl font-bold">
                            {riding.ridingMileageKM || 0}
                            <span className="ml-1 text-xs font-medium text-color-weak">km</span>
                          </div>
                        )}
                        <div className="flex w-full space-x-3">
                          {detail?.scheme.includes('riding_time') && (
                            <div className="flex items-center space-x-1">
                              <Image
                                src="/images/<EMAIL>"
                                alt="时间"
                                width={16}
                                height={16}
                              />
                              <div>
                                <span className="font-bold">{riding.ridingTime || 0}</span>
                                <span className="ml-1 text-3xs font-normal text-color-weak">
                                  min
                                </span>
                              </div>
                            </div>
                          )}
                          {detail?.scheme.includes('average_speed') && (
                            <div className="flex items-center space-x-1">
                              <Image
                                src="/images/<EMAIL>"
                                alt="速度"
                                width={16}
                                height={16}
                              />
                              <div>
                                <span className="font-bold">{riding.avgSpeed}</span>
                                <span className="ml-1 text-3xs font-normal text-color-weak">
                                  km/h
                                </span>
                              </div>
                            </div>
                          )}
                          {detail?.scheme.includes('ride_consume') && (
                            <div className="flex items-center space-x-1">
                              <Image
                                src="/images/<EMAIL>"
                                alt="功耗"
                                width={16}
                                height={16}
                              />
                              <div>
                                <span className="font-bold">{riding.powerConsume || 0}</span>
                                <span className="ml-1 text-3xs font-normal text-color-weak">
                                  wh
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Fragment>
            )
          )}
      </div>
    </Page>
  );
}
