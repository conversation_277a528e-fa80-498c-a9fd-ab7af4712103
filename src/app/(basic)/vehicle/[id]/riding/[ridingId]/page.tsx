// @ts-nocheck
'use client'

import Page from '@/components/Page'
import { useAmap } from '@/hooks/useAmap'
import { useNative } from '@/lib/native'
import { uploadImage } from '@/server/actions/home'
import { getUserInfo } from '@/server/api/me'
import { getRidingTrack } from '@/server/api/riding'
import { getVehicleDetail } from '@/server/api/vehicle'
import { formatTime, toImg } from '@/utils'
import * as AspectRatio from '@radix-ui/react-aspect-ratio'
import { useQuery } from '@tanstack/react-query'
import { Mask, SpinLoading, Toast } from 'antd-mobile'
import { CloseOutline } from 'antd-mobile-icons'
import dayjs from 'dayjs'
import { domToCanvas } from 'modern-screenshot'
import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'

export default function Track({ params }: { params: { id: string; ridingId: string } }) {
  const { elRef, map } = useAmap()
  const ref = useRef<HTMLDivElement>(null)
  // 起始地
  const [startAddress, setStartAddress] = useState('')
  // 目的地
  const [endAddress, setEndAddress] = useState('')
  const [modalVisible, setModalVisible] = useState(false)
  const [loading, setLoading] = useState(false)
  // 图片链接
  const [imageUrl, setImageUrl] = useState('')
  const { hasWechat } = useNative()

  const { data } = useQuery({
    queryKey: ['riding', params.ridingId],
    queryFn: () => getRidingTrack(params.ridingId)
  })
  const { data: userInfo } = useQuery({
    queryKey: ['userinfo'],
    queryFn: getUserInfo
  })

  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id)
  })

  useEffect(() => {
    if (map && data && data.retGpsList?.length > 0) {
      // 删除地图上所有的覆盖物
      map.clearMap()
      const lineArr = (data?.retGpsList || []).map((item) => [
        item.split(',')[0],
        item.split(',')[1]
      ])
      const startMarker = new AMap.Marker({
        position: lineArr[0] as unknown as AMap.LngLatLike,
        icon: new AMap.Icon({
          size: new AMap.Size(34, 44),
          imageSize: new AMap.Size(34, 44),
          image: '/images/<EMAIL>'
        }),
        offset: new AMap.Pixel(-17, -44)
      })
      const endMarker = new AMap.Marker({
        position: lineArr[lineArr.length - 1] as unknown as AMap.LngLatLike,
        icon: new AMap.Icon({
          size: new AMap.Size(34, 44),
          imageSize: new AMap.Size(34, 44),
          image: '/images/<EMAIL>'
        }),
        offset: new AMap.Pixel(-12, -40)
      })
      // 绘制轨迹
      const polyline = new AMap.Polyline({
        path: lineArr as unknown as AMap.LngLatLike[][],
        strokeColor: '#406FE5', //线颜色
        strokeWeight: 4, //线宽
        lineCap: 'round' //线端头样式
      })
      // @ts-ignore
      const geocoder = new AMap.Geocoder({
        city: '全国'
      })
      // 出发地
      geocoder.getAddress(
        lineArr[0] as unknown as AMap.LngLatLike,
        function (status: string, result: any) {
          if (status === 'complete' && result.regeocode) {
            const address = result.regeocode.formattedAddress
            setStartAddress(address)
          }
        }
      )
      // 目的地
      geocoder.getAddress(
        lineArr[lineArr.length - 1] as unknown as AMap.LngLatLike,
        function (status: string, result: any) {
          if (status === 'complete' && result.regeocode) {
            const address = result.regeocode.formattedAddress
            setEndAddress(address)
          }
        }
      )

      const passedPolyline = new AMap.Polyline({
        strokeColor: '#AF5', //线颜色
        strokeWeight: 6 //线宽
      })

      startMarker.on('moving', (e) => {
        passedPolyline.setPath(e.passedPath)
      })

      map.add([startMarker, endMarker, polyline, passedPolyline])
      map.setFitView()

      setTimeout(() => {
        startMarker.moveAlong(lineArr, 800)
      }, 1000)
    }
  }, [map, data])

  // 微信分享
  const handleWechatShare = (type: string) => {
    window.jsBridgeHelper?.sendMessage('wxShare', { img: imageUrl, platform: type })
  }

  // 保存到相册
  const handleSaveImage = () => {
    window.jsBridgeHelper?.sendMessage('saveImgInLocal', { imgUrl: imageUrl })
  }

  return (
    <Page
      title="骑行轨迹"
      right={
        <span
          className="flex justify-end"
          onClick={async () => {
            setModalVisible(true)
            // 如果已经生成分享图片，则不需要再生成
            if (imageUrl) return
            setLoading(true)
            await new Promise((resolve) => setTimeout(resolve, 100))
            domToCanvas(ref.current!!, { scale: 2 }).then((canvas) => {
              canvas.toBlob(async (blob) => {
                if (blob) {
                  const file = new File([blob], 'share.png', { type: blob.type })
                  const formData = new FormData()
                  formData.append('image', file)
                  const result = await uploadImage(formData)
                  setLoading(false)
                  if ('error' in result) {
                    Toast.show('分享失败，请重试')
                  } else {
                    setImageUrl(toImg(result.url))
                  }
                }
              })
            })
          }}
        >
          <Image src="/images/share/<EMAIL>" alt="分享" width={24} height={24} />
        </span>
      }
    >
      <div className="flex h-full flex-col" ref={ref}>
        <div ref={elRef} className="-z-0 -mb-8 flex-1" />
        <div className="z-10 rounded-t-2xl bg-white p-4 shadow-[0_8px_16px_0_rgba(57,68,83,0.1)]">
          <div className="flex">
            <div className="flex-1">
              <div className="text-xs text-color-weak">
                {dayjs(data?.gmtStart).format('MM-DD HH:mm:ss')}
              </div>
              <div className="mt-2 text-3xl font-bold">
                {((data?.mile || 0) / 1000).toFixed(1)}
                <span className="ml-1 text-3xs text-color-weak">km</span>
              </div>
            </div>
            {modalVisible && (
              <div className="-mt-9 flex flex-col items-center">
                <div className="h-11 w-11 rounded-full border-2 border-white">
                  <div
                    className="h-full w-full rounded-full bg-[url('/images/G/<EMAIL>')] bg-cover bg-center bg-no-repeat"
                    style={
                      userInfo?.logo && {
                        backgroundImage: `url(${userInfo.logo})`
                      }
                    }
                  />
                </div>
                <div className="mt-2 font-semibold">{userInfo?.nickName}</div>
              </div>
            )}
          </div>
          <div className="mt-2 grid grid-cols-4 gap-2">
            {detail?.scheme.includes('riding_time') && (
              <div className="rounded-md bg-[#EEF1F4] p-2">
                <div>
                  <span className="font-bold">{formatTime(data?.rideTime || 0)}</span>
                  <span className="ml-1 text-3xs text-color-weak">min</span>
                </div>
                <div className="mt-2 text-3xs text-color-weak">骑行时间</div>
              </div>
            )}
            {detail?.scheme.includes('average_speed') && (
              <div className="rounded-md bg-[#EEF1F4] p-2">
                <div>
                  <span className="font-bold">{data?.avgSpeed || 0}</span>
                  <span className="ml-1 text-3xs text-color-weak">km/h</span>
                </div>
                <div className="mt-2 text-3xs text-color-weak">平均速度</div>
              </div>
            )}
            {detail?.scheme.includes('maximum_speed') && (
              <div className="rounded-md bg-[#EEF1F4] p-2">
                <div>
                  <span className="font-bold">{data?.highSpeed || 0}</span>
                  <span className="ml-1 text-3xs text-color-weak">km/h</span>
                </div>
                <div className="mt-2 text-3xs text-color-weak">最高速度</div>
              </div>
            )}
            {detail?.scheme.includes('ride_consume') && (
              <div className="rounded-md bg-[#EEF1F4] p-2">
                <div>
                  <span className="font-bold">{data?.powerConsumer || 0}</span>
                  <span className="ml-1 text-3xs text-color-weak">wh</span>
                </div>
                <div className=" mt-2 text-3xs text-color-weak">骑行功耗</div>
              </div>
            )}
          </div>
          <div className="mt-3 py-3">
            <div className="flex">
              <div className="flex flex-col items-center">
                <div className="h-2 w-2 rounded-full bg-primary" />
                <div className="h-8 w-0.5 bg-gradient-129 from-[#406FE5] from-[17%] to-[#6FD0FF] to-[84%]" />
              </div>
              <div className="ml-2 text-2xs">
                <div className="-mt-[5px]">{startAddress}</div>
              </div>
            </div>
            <div className="flex">
              <div className="flex flex-col items-center">
                <div className="h-2 w-2 rounded-full bg-[#32D74B]" />
              </div>
              <div className="ml-2 text-2xs">
                <div className="-mt-[5px]">{endAddress}</div>
              </div>
            </div>
          </div>
          {modalVisible && (
            <div className="mt-3 flex items-center px-4 py-3">
              <Image src="/images/share/fenxiang-ewm.png" alt="二维码" height={44} width={44} />
              <div className="mx-3 h-11 w-[1px] bg-color-background" />
              <div className="flex h-11 flex-1 flex-col justify-center">
                <div className="text-xs">立马科技APP记录骑行轨迹</div>
                <div className="mt-1.5 text-3xs text-color-weak">长按扫码，开启新的旅程</div>
              </div>
            </div>
          )}
        </div>
      </div>
      <Mask visible={modalVisible} color="rgba(48, 51, 63, 0.75)">
        <div className="flex flex-col items-center px-9 pt-14">
          <div className="mb-2 flex w-full justify-end">
            <CloseOutline
              fontSize={24}
              color="#fff"
              onClick={() => {
                setModalVisible(false)
              }}
            />
          </div>
          <div className="relative w-full">
            <AspectRatio.Root ratio={ref.current?.scrollWidth!! / ref.current?.scrollHeight!!}>
              {loading ? (
                <div className="flex h-full items-center justify-center">
                  <SpinLoading color="white" style={{ '--size': '24px' }} />
                  <span className="ml-2 text-3xs text-white">加载中...</span>
                </div>
              ) : (
                <Image alt="分享" src={imageUrl} fill className="rounded-2xl" />
              )}
            </AspectRatio.Root>
          </div>
          <div className="mt-5 flex space-x-5">
            {hasWechat && (
              <>
                <div
                  className="flex flex-col items-center"
                  onClick={() => handleWechatShare('session')}
                >
                  <Image
                    src="/images/share/<EMAIL>"
                    alt="微信好友"
                    width={44}
                    height={44}
                  />
                  <span className="mt-1.5 text-3xs text-white">微信好友</span>
                </div>
                <div
                  className="flex flex-col items-center"
                  onClick={() => handleWechatShare('timeline')}
                >
                  <Image
                    src="/images/share/<EMAIL>"
                    alt="朋友圈"
                    width={44}
                    height={44}
                  />
                  <span className="mt-1.5 text-3xs text-white">朋友圈</span>
                </div>
              </>
            )}
            <div className="flex flex-col items-center" onClick={handleSaveImage}>
              <Image src="/images/share/<EMAIL>" alt="相册" width={44} height={44} />
              <span className="mt-1.5 text-3xs text-white">保存到相册</span>
            </div>
          </div>
        </div>
      </Mask>
    </Page>
  )
}
