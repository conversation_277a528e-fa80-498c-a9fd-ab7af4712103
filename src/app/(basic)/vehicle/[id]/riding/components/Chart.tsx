import { useEffect } from 'react';
import { useEcharts } from '@/hooks/useEcharts';
import { LineChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import { GridComponent, TooltipComponent } from 'echarts/components';
import { UniversalTransition } from 'echarts/features';
import dayjs from 'dayjs';

export default function Chart({ data, activeKey }: { data: any; activeKey: string }) {
  const { elRef: lineChartRef, setOption } = useEcharts(
    [GridComponent, TooltipComponent, LineChart, UniversalTransition, CanvasRenderer],
    {
      grid: {
        top: 16,
        bottom: 24,
        left: '16%',
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const date = dayjs(activeKey + '-' + params[0].axisValue).format('MM-DD');
          return `<div class="text-[11px] text-color-weak">${date}</div>
            <div class="text-2xs text-color-text">
              <span class="font-bold">${params[0].value}</span>km
            </div>
            `;
        },
        padding: [6, 8],
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLine: {
          lineStyle: {
            color: '#F5F6F7',
            width: 2,
          },
        },
        axisLabel: {
          color: '#ADB8CC',
          fontSize: 11,
          formatter(value, index) {
            return index === 0 ? `${dayjs(activeKey).format('MM')}-${value}` : value;
          },
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#ADB8CC',
          fontSize: 11,
          formatter: '{value} km',
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#EEE',
          },
        },
      },
      series: [
        {
          name: 'mileage',
          data: [],
          type: 'line',
          smooth: true,
        },
      ],
    }
  );

  useEffect(() => {
    const xData = data?.dayHistory?.map((item: any) => dayjs(item.gmtStart).get('date')) || [];
    const yData = data?.dayHistory?.map((item: any) => item.ridingMileageStr) || [];
    setOption({
      xAxis: {
        data: xData,
      },
      series: [
        {
          name: 'mileage',
          data: yData,
        },
      ],
    });
  }, [data, setOption]);

  return <div ref={lineChartRef} className="h-48 w-full" />;
}
