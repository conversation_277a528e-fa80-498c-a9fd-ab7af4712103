'use client';

import React, { useState, useTransition, useEffect } from 'react';
import Image from 'next/image';
import { Picker, Toast } from 'antd-mobile';
import Page from '@/components/Page';
import List from '@/components/List';
import type { PickerValue, PickerColumn } from 'antd-mobile/es/components/picker';
import { timestampToDate, toEnumArray, toImg } from '@/utils';
import { getVehicleDetail } from '@/server/api/vehicle';
import { getUpdateVehicleBrand } from '@/server/actions/vehicle';
import { getExpireTime } from '@/server/api/service';
import { useQuery } from '@tanstack/react-query';
import * as AspectRatio from '@radix-ui/react-aspect-ratio';

const toEnum = (data: any = [], id: string = 'id', value: string = 'value') => {
  let res: Record<string, string> = {},
    len = data.length;
  if (len > 0) {
    for (let i = 0; i < len; i++) {
      let key = data[i][id];
      res[key] = data[i][value];
    }
  }
  return res;
};

export default function VehicleInfo({ params }: { params: { id: string } }) {
  const [isPending, startTransition] = useTransition();
  const [brandId, setBrandId] = useState('');
  const [bandList, setBandList] = useState<Record<string, string>>({});

  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id),
  });

  const { data: expired } = useQuery({
    queryKey: ['vehicle-info', params.id],
    queryFn: () => getExpireTime(params.id),
  });

  useEffect(() => {
    if (detail) {
      setBrandId(detail?.brandId.toString());
      const bandList = toEnum(detail.allBrand, 'id', 'brandName');
      setBandList(bandList);
    }
  }, [detail]);

  const onConfirm = (value: PickerValue[]) => {
    // @ts-ignore
    const brandId: string = value[0];

    startTransition(async () => {
      const data = await getUpdateVehicleBrand({
        id: detail?.id!!,
        brandId,
      });
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      setBrandId(brandId);
      Toast.show('操作成功');
    });
  };

  return (
    <Page title="车辆信息">
      <div
        className="absolute left-0 top-0 -z-10 flex h-[350px] w-full  items-center
      justify-center bg-[url('/images/<EMAIL>')] bg-cover bg-center bg-no-repeat"
      />

      <div className="space-y-3 px-3 py-6">
        <div className="rounded-2xl bg-[url('/images/<EMAIL>')] bg-cover bg-center bg-no-repeat px-5 py-5 text-white">
          <div className="flex items-center ">
            <div className="relative w-[100px]">
              <AspectRatio.Root ratio={482 / 375}>
                <Image
                  src={detail?.img ? toImg(detail?.img) : '/images/<EMAIL>'}
                  alt="车型图"
                  fill
                  priority
                />
              </AspectRatio.Root>
            </div>
            <div className="ml-3">
              {detail?.scheme.includes('model') && (
                <div className="text-2xl">{detail?.nickName}</div>
              )}
              {detail?.scheme.includes('color') && (
                <div className="text-xs text-white/60">{detail?.color}</div>
              )}
            </div>
          </div>
          <div className="mt-3 flex items-center justify-between">
            {detail?.scheme.includes('identify') && (
              <div className="pr-2">
                <div className="mb-2 break-all">{detail?.identifier}</div>
                <div className="text-xs text-white/60">车辆识别码</div>
              </div>
            )}
            {detail?.scheme.includes('identify') && detail?.scheme.includes('device_no') && (
              <div
                style={{
                  background: 'rgba(238, 238, 238,.4)',
                  width: '1px',
                  height: '100%',
                  minHeight: '3rem',
                }}
              />
            )}
            {detail?.scheme.includes('device_no') && (
              <div className=" pl-2">
                <div className="mb-2 break-all">{detail?.deviceNo}</div>
                <div className="text-xs text-white/60">车架号</div>
              </div>
            )}
          </div>
        </div>
        <List
          items={[
            {
              title: '电机编号',
              value: detail?.motorNo,
              show: detail?.scheme.includes('motor_no'),
            },
            {
              title: '总行驶里程',
              value: expired?.isExpired ? '--' : (detail?.totalMile ?? 0) + ' KM',
              show: detail?.scheme.includes('all_mile'),
            },
            {
              title: '品牌',
              onClick: (_, valueChildRef) => valueChildRef.current?.open(),
              value: (
                <Picker
                  value={[brandId]}
                  columns={[toEnumArray(bandList) as PickerColumn]}
                  cancelText="取消"
                  onConfirm={onConfirm}
                >
                  {() => bandList[brandId] || '-'}
                </Picker>
              ),
              arrow: true,
              show: detail?.scheme.includes('brand') && detail?.installType === 2,
            },
          ]}
        />
        <List
          items={[
            {
              title: '激活时间',
              value: timestampToDate(detail?.firstBindTime),
              show: detail?.scheme.includes('bind_time'),
            },
            {
              title: 'GPS刷新时间',
              value: expired?.isExpired ? '--' : timestampToDate(detail?.gmtGps),
              show: detail?.scheme.includes('gps_flush_time'),
            },
            {
              title: 'GMS刷新时间',
              value: expired?.isExpired ? '--' : timestampToDate(detail?.gmtGsm),
              show: detail?.scheme.includes('gsm_flush_time'),
            },
          ]}
        />
      </div>
    </Page>
  );
}
