// @ts-nocheck

'use client';

import React, { useEffect, useState, useTransition } from 'react';
import Link from 'next/link';
import { Picker, Toast, Dialog } from 'antd-mobile';
import { useRouter } from 'next/navigation';
import Page from '@/components/Page';
import List from '@/components/List';
import Switch from '@/components/Switch';
import Button from '@/components/Button';
import type { PickerValue, PickerColumn } from 'antd-mobile/es/components/picker';
import { InformationCircleOutline } from 'antd-mobile-icons';
import { toEnumArray } from '@/utils';
import {
  getBatteryDetail,
  getBatteryOptWarn,
  getBatteryUnBind,
  getBatteryUpdate,
} from '@/server/actions/battery';
import { getVehicleDetail } from '@/server/api/vehicle';
import { useQuery } from '@tanstack/react-query';

// 电池设置
export default function BatterySetting({ params }: { params: { id: string; bId: string } }) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const [enumList, setEnumList] = useState({});
  const [detail, setDetail] = useState({});

  const [batteryData, setBatteryData] = useState({
    batteryType: '',
    batteryVoltage: '',
    batteryCapacity: '',
  });

  const [switchData, setSwitchData] = useState({ lowSocWarn: false, tempWarn: false });

  const { data: vehicleDetail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id),
  });

  useEffect(() => {
    toDetail(params.bId, params.id);
  }, [params]);

  const toDetail = (bId, deviceNo) => {
    startTransition(async () => {
      const data: any = await getBatteryDetail(bId, deviceNo);
      console.log('[ data ] >', data);
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      const { info } = data;
      // console.log('[ info ] >', info);
      setDetail(info);
      setEnumList(data);
      setBatteryData({
        batteryType: info.type?.toString(),
        batteryVoltage: info.ratedVoltage?.toString(),
        batteryCapacity: info.ratedCapacitance?.toString(),
      });
      setSwitchData({
        lowSocWarn: !!info.lowSocWarn,
        tempWarn: !!info.tempWarn,
      });
    });
  };

  const list = [
    {
      name: '当前温度',
      unit: '℃',
      value: detail.ambientTemperature || ' -- ',
      show: vehicleDetail?.scheme.includes('current_temp'),
    },
    {
      name: '当前电压',
      unit: 'V',
      value: detail.currentVoltage ? detail.currentVoltage / 10 : ' -- ',
      show: vehicleDetail?.scheme.includes('current_vtg'),
    },
    {
      name: '剩余电量',
      unit: '%',
      value: detail.soc || ' -- ',
      show: vehicleDetail?.scheme.includes('soc'),
    },
    {
      name: '电池健康度',
      unit: '%',
      value: detail.soh || ' -- ',
      show: vehicleDetail?.scheme.includes('soh'),
    },
  ];

  const list2 = [
    {
      name: '当前电压',
      unit: 'V',
      value: detail.currentVoltage ? detail.currentVoltage / 10 : ' -- ',
      show: vehicleDetail?.scheme.includes('current_vtg'),
    },
    {
      name: '剩余电量',
      unit: '%',
      value: detail.soc || ' -- ',
      show: vehicleDetail?.scheme.includes('soc'),
    },
  ];

  const onConfirm = (value: PickerValue[], type: string) => {
    startTransition(async () => {
      // console.log('[ detail.id ] >', detail.id);
      const result = await getBatteryUpdate({
        [type]: value[0],
        id: detail.id,
        deviceNo: params.id,
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        setBatteryData({
          ...batteryData,
          [type]: value[0],
        });
        Toast.show('操作成功');
      }
    });
  };

  const toSwitch = (value: boolean, type: string) => {
    startTransition(async () => {
      const result = await getBatteryOptWarn({
        [type]: value ? 1 : 0,
        id: detail.id,
        deviceNo: params.id,
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        setSwitchData({
          ...switchData,
          [type]: value,
        });

        Toast.show('操作成功');
      }
    });
  };
  const toUnBind = () => {
    Dialog.confirm({
      content: '确定删除此电池？',
      onAction: (action) => {
        if (action.key === 'confirm') {
          startTransition(async () => {
            const result = await getBatteryUnBind(detail.id);
            console.log('[ result ] >', result);
            if (result?.error) {
              Toast.show(result.error);
            } else {
              Toast.show('删除成功');
              if (result.haveBms) {
                router.back();
              } else {
                router.push(`/vehicle/${params.id}`);
              }
            }
          });
        }
      },
    });
  };

  return (
    <Page
      title="电池管理"
      right={
        <div className="flex w-full justify-end">
          <Link href={`/vehicle/1/battery/tip`}>
            <InformationCircleOutline fontSize={24} color="#333" />
          </Link>
        </div>
      }
    >
      <div className="absolute left-0 top-0 -z-10 flex h-[294px] w-full items-center  justify-center  bg-[url('/images/G/<EMAIL>')] bg-cover bg-center bg-no-repeat pt-4  ">
        <div className="h-full w-[60px] bg-[url('/images/G/<EMAIL>')] bg-contain bg-center bg-no-repeat" />
        {vehicleDetail?.scheme.includes('battery_no') && detail.identifier && (
          <div className="ml-8">
            <div>电池编号</div>
            <div className="mt-1 text-xl font-bold ">{detail.identifier}</div>
          </div>
        )}
      </div>

      <div className="mt-[190px] space-y-3 px-3 ">
        {detail.identifier &&
          (vehicleDetail?.scheme.includes('soc') ||
            vehicleDetail?.scheme.includes('current_vtg') ||
            vehicleDetail?.scheme.includes('current_temp') ||
            vehicleDetail?.scheme.includes('soh')) && (
            <div className="space-y-3 rounded-xl bg-white p-4">
              {list.map(
                (_, index) =>
                  _.show && (
                    <div
                      key={index}
                      className="flex w-1/2 flex-col items-center justify-center  "
                      style={{ display: 'inline-block', margin: '10px 0 ' }}
                    >
                      <div className="text-center text-2xl text-[#30333F] ">
                        {_.value} <span className=" text-xs text-[#908F94]">{_.unit}</span>
                      </div>
                      <div className="mt-1 text-center text-xs text-[#908F94]">{_.name}</div>
                    </div>
                  )
              )}
            </div>
          )}

        {!detail.identifier &&
          (vehicleDetail?.scheme.includes('soc') ||
            vehicleDetail?.scheme.includes('current_vtg')) && (
            <div className="space-y-3 rounded-xl bg-white p-4">
              {list2.map(
                (_, index) =>
                  _.show && (
                    <div
                      key={index}
                      className="flex w-1/2 flex-col items-center justify-center  "
                      style={{ display: 'inline-block', margin: '10px 0 ' }}
                    >
                      <div className="text-center text-2xl text-[#30333F] ">
                        {_.value} <span className=" text-xs text-[#908F94]">{_.unit}</span>
                      </div>
                      <div className="mt-1 text-center text-xs text-[#908F94]">{_.name}</div>
                    </div>
                  )
              )}
            </div>
          )}
      </div>

      <div className="space-y-3 px-3 py-6" style={{ paddingTop: '0.75rem' }}>
        <List
          items={[
            {
              title: '电池类型',
              onClick: (_, valueChildRef) => {
                if (!detail.identifier && vehicleDetail?.isOwner === 1) {
                  valueChildRef.current?.open();
                }
              },
              value: (
                <Picker
                  value={[batteryData.batteryType]}
                  columns={[toEnumArray(enumList.enumBatteryType) as PickerColumn]}
                  cancelText="取消"
                  onConfirm={(_) => onConfirm(_, 'batteryType')}
                >
                  {() =>
                    batteryData.batteryType
                      ? enumList.enumBatteryType[batteryData.batteryType]
                      : ' -- '
                  }
                </Picker>
              ),
              arrow: !detail.identifier && vehicleDetail?.isOwner === 1,
              show: vehicleDetail?.scheme.includes('battery_type'),
            },
            {
              title: '电池电压',
              onClick: (_, valueChildRef) => {
                if (!detail.identifier && vehicleDetail?.isOwner === 1) {
                  valueChildRef.current?.open();
                }
              },
              value: detail.identifier ? (
                detail.ratedVoltage == null ? (
                  '--'
                ) : (
                  `${detail.ratedVoltage}V`
                )
              ) : (
                <Picker
                  columns={[toEnumArray(enumList.enumBatteryVtg) as PickerColumn]}
                  value={[batteryData.batteryVoltage]}
                  cancelText="取消"
                  onConfirm={(_) => onConfirm(_, 'batteryVoltage')}
                >
                  {() =>
                    batteryData.batteryVoltage
                      ? enumList.enumBatteryVtg[batteryData.batteryVoltage]
                      : ' -- '
                  }
                </Picker>
              ),
              arrow: !detail.identifier && vehicleDetail?.isOwner === 1,
              show: vehicleDetail?.scheme.includes('battery_vtg'),
            },
            {
              title: '电池容量',
              onClick: (_, valueChildRef) => {
                if (!detail.identifier && vehicleDetail?.isOwner === 1) {
                  valueChildRef.current?.open();
                }
              },
              value: detail.identifier ? (
                detail.ratedCapacitance == null ? (
                  '--'
                ) : (
                  `${detail.ratedCapacitance}AH`
                )
              ) : (
                <Picker
                  columns={[toEnumArray(enumList.enumBatteryCapacity) as PickerColumn]}
                  value={[batteryData.batteryCapacity]}
                  cancelText="取消"
                  onConfirm={(_) => onConfirm(_, 'batteryCapacity')}
                >
                  {() =>
                    batteryData.batteryCapacity
                      ? enumList.enumBatteryCapacity[batteryData.batteryCapacity]
                      : ' -- '
                  }
                </Picker>
              ),
              arrow: !detail.identifier && vehicleDetail?.isOwner === 1,
              show: vehicleDetail?.scheme.includes('battery_capacity'),
            },
          ]}
        />

        <List
          items={[
            {
              key: 1,
              title: '电池温度异常提醒',
              description: '开启后，当电池温度高于55℃推送异常提醒',
              value: (
                <Switch checked={switchData.tempWarn} onChange={(_) => toSwitch(_, 'tempWarn')} />
              ),
              show: vehicleDetail?.scheme.includes('lower_temp_notice') && !!detail.identifier,
            },
            {
              key: 2,
              title: '低电量智能提醒',
              description: '开启后，当电量低于10%推送充电通知',
              value: (
                <Switch
                  checked={switchData.lowSocWarn}
                  onChange={(_) => toSwitch(_, 'lowSocWarn')}
                />
              ),
              show: vehicleDetail?.scheme.includes('lower_soc_notice'),
            },
          ]}
        />

        {vehicleDetail?.scheme.includes('unbind_battery') &&
          vehicleDetail?.isOwner === 1 &&
          detail.identifier && (
            <Button block type="primary" onClick={toUnBind}>
              删除
            </Button>
          )}
      </div>
    </Page>
  );
}
