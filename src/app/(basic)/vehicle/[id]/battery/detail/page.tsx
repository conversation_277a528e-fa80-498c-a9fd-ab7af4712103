// @ts-nocheck

'use client'

import List from '@/components/List'
import Page from '@/components/Page'
import { useBLEClient } from '@/lib/ble'
import { useNative } from '@/lib/native'
import {
  getBatteryCharge,
  getBattery<PERSON>hart,
  getBatteryList,
  getBatterySum
} from '@/server/actions/battery'
import { getVehicleDetail } from '@/server/api/vehicle'
import { state } from '@/store'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Dialog, Empty, Toast } from 'antd-mobile'
import { InformationCircleOutline } from 'antd-mobile-icons'
import dayjs from 'dayjs'
import Image from 'next/image'
import Link from 'next/link'
import { useEffect, useTransition } from 'react'
import BatteryDetailChart from './Chart'

// 电池详情
export default function BatteryDetail({ params }: { params: { id: string } }) {
  const [isPending, startTransition] = useTransition()
  const deviceNo = params.id
  const queryClient = useQueryClient()
  const bleClient = useBLEClient()
  const { clientId } = useNative()
  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3)

  const { data: vehicleDetail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id)
  })

  const { data: detail = {} } = useQuery({
    queryKey: ['vehicle', 'sum', params.id],
    queryFn: () => getBatterySum(params.id),
    enabled: !!params.id && !bleClient.isConnected()
  })
  console.log('[ detail ] >', detail)

  const { data: list = [] } = useQuery({
    queryKey: ['vehicle', 'list', params.id],
    queryFn: () => getBatteryList(params.id)
  })
  console.log('[ list ] >', list)

  const { data: chart = [] } = useQuery({
    queryKey: ['vehicle', 'chart', params.id],
    queryFn: () =>
      getBatteryChart({
        deviceNo: params.id,
        // 近24小时
        searchBgnTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
        searchEndTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
      }),
    enabled: list.length > 0
  })
  console.log('[ chart ] >', chart)

  useEffect(() => {
    if (bleClient.isConnected()) {
      toBluetooth()
    }
  }, [bleClient, deviceNo, clientId, deviceNetWorkModule])

  const bil = detail.avgSoc / 100
  let pen = '0%'

  if (bil === 0 || bil === 0.5 || bil === 1) {
    pen = bil * 100 + '%'
  } else if (bil < 0.15) {
    pen = 15 + bil * 80 + '%'
  } else if (bil > 0.85) {
    pen = 5 + bil * 80 + '%'
  } else {
    pen = 10 + bil * 80 + '%'
  }

  const toCharge = (state: number) => {
    const content = state ? '开始充电' : '停止充电'
    Dialog.confirm({
      content,
      onAction: (action) => {
        if (action.key === 'confirm') {
          startTransition(async () => {
            const result = await getBatteryCharge({ deviceNo, state })
            if (result?.error) {
              Toast.show(result.error)
            } else {
              // setChargeState(state ? 1 : 5);
              await getBatterySum(params.id)
              queryClient.setQueryData(['vehicle', 'sum', params.id], detail)
              Toast.show(content + '成功')
            }
          })
        }
      }
    })
  }

  const toBluetooth = () => {
    bleClient.getBatteryData({ deviceNo }, (result) => {
      queryClient.setQueryData(['vehicle', 'sum', deviceNo], {
        ...detail,
        ...result
      })
    })
  }

  return (
    <Page
      title="电池详情"
      navStyle="white"
      right={
        <div className="flex w-full justify-end">
          <Link href={`/vehicle/1/battery/tip`}>
            <InformationCircleOutline fontSize={24} color="#fff" />
          </Link>
        </div>
      }
      navDivStyle={{
        background: 'linear-gradient(129deg,#6F7186 17.54%,  #4E5771 84.87%)'
      }}
    >
      <div
        className="flex  w-full flex-col items-center pb-6 pt-5"
        style={{
          backgroundSize: 'cover !important',
          background: "#4E5771 url('/images/G/<EMAIL>')  no-repeat center center"
        }}
      >
        {vehicleDetail?.scheme.includes('remian_battery') && (
          <div className="mt-4 text-6xl text-white ">
            {detail.avgSoc ?? ' -- '}
            <span className="ml-1 text-2xl" style={{ color: '#EBEBF5', opacity: '0.6' }}>
              %
            </span>
          </div>
        )}
        {vehicleDetail?.scheme.includes('remain_charge_time') &&
        detail.remainChargeTime &&
        detail.chargeState === 4 ? (
          <div className="mt-1 text-xl">
            <span className="text-white" style={{ color: '#EBEBF5', opacity: '0.6' }}>
              充电剩余时间
            </span>
            <span className="ml-1 text-white">
              {detail.remainChargeTime ? detail.remainChargeTime + ' min' : ' -- '}
            </span>
          </div>
        ) : null}

        {vehicleDetail?.scheme.includes('remain_charge_time') && detail.chargeState === 1 ? (
          <div className="mt-1 text-xl">
            <span className="text-white" style={{ color: '#EBEBF5', opacity: '0.6' }}>
              充电已完成
            </span>
          </div>
        ) : null}

        <div className="-mt-12 h-[120px] w-full bg-[url('/images/C-4/<EMAIL>')] bg-contain bg-center bg-no-repeat">
          <div
            className={
              detail.avgSoc && parseInt(detail.avgSoc) < 9
                ? "h-full bg-[url('/images/C-4/<EMAIL>')]  bg-contain bg-center bg-no-repeat"
                : "h-full bg-[url('/images/C-4/<EMAIL>')]  bg-contain bg-center bg-no-repeat"
            }
            style={{
              width: '100%',
              padding: '0 15%',
              clipPath: `polygon(0 0, ${pen} 0, ${pen} 100%, 0 100%)`
            }}
          >
            {vehicleDetail?.scheme.includes('charge_state') && detail.chargeState === 4 && (
              <div
                className="h-full bg-[url('/images/C-4/<EMAIL>')]  bg-contain  bg-no-repeat"
                style={{
                  width: pen,
                  backgroundSize: 'auto 26px',
                  backgroundPosition: 'center 70px'
                }}
              />
            )}
          </div>
        </div>

        {vehicleDetail?.scheme.includes('charge_control') && detail.chargeState >= 2 && (
          <div
            onClick={() => toCharge(detail.chargeState === 5 ? 1 : 0)}
            className="mt-2 flex h-11 w-2/5 items-center justify-center rounded-3xl bg-[rgba(255,255,255,0.2)]"
          >
            <span className="ml-2  text-white">
              {detail.chargeState === 5 ? '开始充电' : '停止充电'}
            </span>
          </div>
        )}
      </div>

      <div className="space-y-3 px-3 py-6 pb-5">
        {vehicleDetail?.scheme.includes('curve_soc') && (
          <div className="min-h-48 rounded-2xl bg-white p-4">
            <div className="mb-2 text-xl ">电量变化曲线</div>
            {!Array.isArray(chart) || chart.length === 0 ? (
              <Empty description="暂无数据" className="h-48 w-full" />
            ) : (
              <div className="h-48 w-full">
                <BatteryDetailChart chart={chart} />
              </div>
            )}
          </div>
        )}

        {Array.isArray(list) &&
          list.map((_: any) => {
            let tip = ''
            let tipShow = false
            if (_.soc && _.soc < 11) {
              tip = '电池电量低'
              tipShow = vehicleDetail?.scheme.includes('battery_lower')
            } else if (_.ambientTemperature > 55) {
              tip = '温度过高'
              tipShow = vehicleDetail?.scheme.includes('battery_temp_error')
            }
            // id为-1表示铅酸电池
            const isOnline = _.id === -1 ? true : _.isOnline === 1
            return (
              <div
                className={`space-y-3 rounded-xl bg-white p-4 ${isOnline ? '' : 'opacity-60'}`}
                key={_.id}
              >
                <Link href={`/vehicle/${deviceNo}/battery/${_.id}/setting`}>
                  <div className="mb-2 flex items-center justify-between">
                    {vehicleDetail?.scheme.includes('battery_no') && (
                      <div>
                        <div className="text-lg  text-[#30333F]">电池编号</div>
                        <div className=" text-xs text-[#908F94]">{_.identifier}</div>
                      </div>
                    )}
                    {tipShow && (
                      <div
                        className="flex items-center justify-center rounded-2xl border	border-[#FF6430]"
                        style={{ minWidth: 96 }}
                      >
                        <Image
                          src="/images/C-4/<EMAIL>"
                          alt="tip"
                          width={16}
                          height={16}
                        />
                        <span className="ml-1 text-3xs text-[#FF6430]">{tip}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex w-full space-x-3">
                    {vehicleDetail?.scheme.includes('remian_battery') && (
                      <div
                        className="flex items-center rounded bg-[#EEF1F4] p-[10px]"
                        style={{ width: '44%' }}
                      >
                        <div
                          className="h-[34px] w-2/5 bg-[url('/images/C-4/dcbk.svg')] bg-contain bg-center bg-no-repeat"
                          style={{ padding: '9px 7px 9px 3px' }}
                        >
                          <div
                            className={
                              isOnline
                                ? _.soc && _.soc > 11
                                  ? 'h-full rounded bg-gradient-to-b from-[#2FB8FF] to-[#9EECD9]'
                                  : 'h-full rounded bg-gradient-to-b from-[#FF9F0A] to-[#FF453A]'
                                : 'h-full rounded bg-gradient-to-b from-[#AAA9AD] to-[#C9CDD3]'
                            }
                            style={{ width: _.soc ? _.soc + '%' : '0%' }}
                          >
                            {_.chargeState === 4 && (
                              <div className="h-full bg-[url('/images/C-4/<EMAIL>')]  bg-contain bg-center bg-no-repeat" />
                            )}
                          </div>
                        </div>
                        <div className="ml-2">
                          <div className="mb-0.5 flex items-center text-xl font-bold text-[#30333F]">
                            {_.soc ?? ' -- '}
                            <span className="ml-1 text-xs font-normal text-color-weak">%</span>
                          </div>
                          <div className="text-xs font-normal text-color-weak">剩余电量</div>
                        </div>
                      </div>
                    )}
                    {vehicleDetail?.scheme.includes('electricity') && (
                      <div
                        className="rounded bg-[#EEF1F4] p-[10px]"
                        style={{ width: 'calc(28% - 0.75rem)' }}
                      >
                        <div className="mb-0.5 flex items-center text-xl font-bold text-[#30333F]">
                          {_.currentVoltage ? _.currentVoltage / 10 : ' -- '}
                          <span className="ml-1 text-xs font-normal text-color-weak">V</span>
                        </div>
                        <div className="text-xs font-normal text-color-weak">当前电压</div>
                      </div>
                    )}
                    {vehicleDetail?.scheme.includes('temperature') && (
                      <div
                        className="rounded bg-[#EEF1F4] p-[10px]"
                        style={{ width: 'calc(28% - 0.75rem)' }}
                      >
                        <div className=" mb-0.5 flex items-center text-xl font-bold text-[#30333F]">
                          {_.ambientTemperature || ' -- '}
                          <span className="ml-1 text-xs font-normal text-color-weak">℃</span>
                        </div>
                        <div className="text-xs font-normal text-color-weak">当前温度</div>
                      </div>
                    )}
                  </div>
                </Link>
              </div>
            )
          })}
        <List
          items={[
            {
              title: '充电设置',
              href: `/vehicle/${deviceNo}/setting/charge`,
              show: vehicleDetail?.scheme.includes('charge_setting')
            }
          ]}
        />
        <List
          items={[
            {
              title: '中控电量',
              value: <span className="font-bold">{detail.vcuBatterySoc ?? ' -- ' + ' '}%</span>,
              show: vehicleDetail?.scheme.includes('vcu_battery')
            }
          ]}
        />
      </div>
    </Page>
  )
}
