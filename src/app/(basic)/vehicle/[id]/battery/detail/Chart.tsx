// @ts-nocheck

'use client';

import React from 'react';
import { useEcharts } from '@/hooks/useEcharts';
import * as echarts from 'echarts/core';
import { LineChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import { GridComponent } from 'echarts/components';
import { formatTime, timestampToDate } from '@/utils';

// 电池详情
export default function BatteryDetailChart({ chart }) {
  console.log('[ chart ] >', chart);
  const xAxis: any[] = [];
  const series: any[] = [];
  chart.forEach((_: any) => {
    xAxis.push(timestampToDate(_.time));
    series.push(_.soc);
  });

  const { elRef: lineChartRef } = useEcharts([GridComponent, LineChart, CanvasRenderer], {
    grid: {
      top: 30,
      bottom: 40,
      right: 40,
      left: 40,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisTick: { show: false },
      splitLine: { show: false },
      axisLine: { show: false },
      axisLabel: {
        color: '#B0B7C3',
        fontSize: 12,
        lineHeight: 16,
        formatter: (value, idx) => value.replace(' ', '\n'),
      },
      data: xAxis,
    },
    yAxis: {
      type: 'value',
      name: series.length ? '% ' : '',
      axisTick: { show: false },
      splitLine: { show: false },
      axisLine: { show: false },
      axisLabel: {
        color: '#B0B7C3',
        fontSize: 12,
      },
    },

    series: [
      {
        data: series,
        type: 'line',
        smooth: true,
        label: { show: false },
        showSymbol: false,
        lineStyle: {
          width: 3,
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#406FE5' },
            { offset: 1, color: '#6FD0FF' },
          ]),
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            {
              offset: 0,
              color: 'rgba(47, 184, 255, 0.5)',
            },
            {
              offset: 1,
              color: 'rgba(158, 236, 217, 0.5)',
            },
          ]),
        },
      },
    ],
  });

  return <div ref={lineChartRef} className="h-full w-full" />;
}
