// @ts-nocheck

'use client';

import React, { useEffect, useState, useTransition } from 'react';
import Link from 'next/link';
import { Toast } from 'antd-mobile';
import Page from '@/components/Page';
import { InformationCircleOutline } from 'antd-mobile-icons';
import { timestampToDate } from '@/utils';
import { getBatteryList } from '@/server/actions/battery';
import { getExpireTime } from '@/server/api/service';
import { useQuery } from '@tanstack/react-query';

// 电池列表切换

export default function BatteryList({ params }: { params: { id: string } }) {
  const [isPending, startTransition] = useTransition();
  const [list, setList] = useState([]);
  const deviceNo = params.id;

  useEffect(() => {
    toList(deviceNo);
  }, [deviceNo]);

  const toList = (deviceNo) => {
    startTransition(async () => {
      const data: any = await getBatteryList(deviceNo);
      console.log('[ data ] >', data);
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      setList(data);
    });
  };

  const { data: expired } = useQuery({
    queryKey: ['vehicle-info', deviceNo],
    queryFn: () => getExpireTime(deviceNo),
  });

  return (
    <Page
      title="电池管理"
      style={{ background: '#fff' }}
      right={
        <div className="flex w-full justify-end">
          <Link href={`/vehicle/1/battery/tip`}>
            <InformationCircleOutline fontSize={24} color="#333" />
          </Link>
        </div>
      }
    >
      {/* <div className="absolute left-0 top-0 -z-10 flex h-[294px] w-full  items-center justify-center bg-[url('/images/G/<EMAIL>')]  bg-cover bg-center bg-no-repeat pb-5 pt-[54px]">
        <div className="h-full  w-full bg-[url('/images/G/<EMAIL>')]  bg-contain  bg-center  bg-no-repeat " />
      </div> */}
      <div className=" mt-10 h-[200px]  w-full bg-[url('/images/G/<EMAIL>')]  bg-contain  bg-center  bg-no-repeat " />
      <div className=" space-y-3 px-3 py-6 ">
        {list.map((_) => {
          const isOnline = expired?.isExpired ? false : _.isOnline;
          return (
            <Link href={`/vehicle/${deviceNo}/battery/${_.id}/setting`} key={_.id}>
              <div
                className={
                  isOnline
                    ? "mb-3 flex w-full  items-center  space-x-3 rounded-2xl bg-[url('/images/G/<EMAIL>')] bg-cover  bg-center bg-no-repeat px-6 py-3"
                    : 'mb-3 flex w-full items-center space-x-3 rounded-2xl bg-[#EEF1F4] px-6 py-3 text-[#908F94]'
                }
              >
                <div
                  className={
                    isOnline
                      ? "h-[50px] w-[50px] bg-[url('/images/G/<EMAIL>')] bg-contain bg-center bg-no-repeat"
                      : "h-[50px] w-[50px] bg-[url('/images/G/<EMAIL>')] bg-contain bg-center bg-no-repeat"
                  }
                />
                <div className="ml-4">
                  <div className="mb-2 text-xl" style={{ color: isOnline && '#fff' }}>
                    电池编号 {_.identifier}
                  </div>
                  <div
                    className="mb-0.5 text-2xs"
                    style={{ color: isOnline && 'rgba(255, 255, 255, 0.60)' }}
                  >
                    绑定时间 {_.gmtBind ? timestampToDate(_.gmtBind) : ' - '}
                  </div>
                  <div
                    className="text-2xs"
                    style={{ color: isOnline && 'rgba(255, 255, 255, 0.60)' }}
                  >
                    更新时间 {_.gmtUpdate ? timestampToDate(_.gmtUpdate) : ' - '}
                  </div>
                </div>
              </div>
            </Link>
          );
        })}
      </div>
    </Page>
  );
}
