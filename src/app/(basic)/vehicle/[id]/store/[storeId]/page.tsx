'use client';

import React, { useEffect, useState, useTransition } from 'react';
import Image from 'next/image';
import {ActionSheet, Tag, Toast} from 'antd-mobile';
import { StarOutline, StarFill } from 'antd-mobile-icons';
import Page from '@/components/Page';
import { useAmap } from '@/hooks/useAmap';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getStoreDetail } from '@/server/api/store';
import { storeCollect, storeCancelCollect } from '@/server/actions/store';
import {isWithinTime} from "@/utils";
import {Action} from "antd-mobile/es/components/action-sheet";

export default function StoreDetail({ params }: { params: { storeId: string } }) {
  const { elRef, map } = useAmap();
  const [_, startTransition] = useTransition();
  const queryClient = useQueryClient();
  const [distance, setDistance] = useState('0');
  const [visible, setVisible] = useState(false);
  const [currentItem, setCurrentItem] = useState<any>();

  const { data } = useQuery({
    queryKey: ['store', params.storeId],
    queryFn: () => getStoreDetail(params.storeId),
  });

  useEffect(() => {
    if (map && data) {
      // 删除地图上所有的覆盖物
      map.clearMap();
      const marker = new AMap.Marker({
        position: [data?.store.storeGpsLng, data?.store.storeGpsLat],
        icon: new AMap.Icon({
          image: '/images/<EMAIL>',
          size: new AMap.Size(34, 40),
          imageSize: new AMap.Size(34, 40),
        }),
        offset: new AMap.Pixel(-17, -44),
      });
      map.add(marker);

      window.jsBridgeHelper?.sendMessage('queryLocation').then((res: any) => {
        if (res.code === '200') {
          const userMarker = new AMap.Marker({
            position: [res.data.longitude, res.data.latitude] as unknown as AMap.LngLatLike,
            icon: new AMap.Icon({
              size: new AMap.Size(28, 28),
              imageSize: new AMap.Size(28, 28),
              image: '/images/<EMAIL>',
            }),
            offset: new AMap.Pixel(-14, -14),
          });
          map.add([userMarker]);
          // 计算距离
          const p1 = new AMap.LngLat(data?.store.storeGpsLng, data?.store.storeGpsLat);
          const p2 = new AMap.LngLat(res.data.longitude, res.data.latitude);
          const distance = (p1.distance(p2) / 1000).toFixed(1);
          setDistance(distance);
        }
      });

      map.setFitView();
    }
  }, [data, map]);

  // 收藏或取消收藏
  const handleCollect = (state: number) => {
    startTransition(async () => {
      const result = state
        ? await storeCancelCollect(params.storeId)
        : await storeCollect(params.storeId);
      if (result?.error) {
        Toast.show(result.error);
      } else {
        Toast.show(state ? '取消收藏成功' : '收藏成功');
        queryClient.invalidateQueries({ queryKey: ['store', params.storeId] });
      }
    });
  };
  const getServiceTypeIcon = (_:any) => {
    switch (_) {
      case '销售服务': return '/images/store/store-service-sale.png'
      case '保养服务': return '/images/store/store-service-care.png'
      case '维修服务': return '/images/store/store-service-fix.png'
      default: return ''
    }
  };

  // 拨号
  const handlePhoneCall = (e: any, phone: string) => {
    e.stopPropagation();
    window.jsBridgeHelper?.sendMessage('phoneCall', phone);
  };
  // 导航
  const handleAction = (action: Action) => {
    setVisible(false);
    window.jsBridgeHelper?.sendMessage(action.key, {
      lat: currentItem.storeGpsLat,
      lon: currentItem.storeGpsLng,
    });
  };

  return (
    <Page title="网点详情" navBgColor="#fff"
          right={<span onClick={() => handleCollect(data?.store.collectState)}>
                    {data?.store.collectState ? (
                        <StarFill fontSize={22} />
                    ) : (
                        <StarOutline fontSize={22} />
                    )}
                  </span>}>
      <div className="h-full bg-white space-y-3 px-5 pt-4 ">
        <div className="text-xl font-bold">{data?.store.storeName} </div>
        <p className="text-xs">
          {
            isWithinTime(data?.store?.businessTime)
                ? <span className="font-bold text-orange-500">营业中</span>
                : <span className="font-bold text-gray-500">暂未营业</span>
          }
          <span className="text-gray-500">&emsp;{data?.store?.businessTime  || '-'}</span>
          {
            data?.store?.storeNature && (<Tag className="ml-2" color='rgb(67,126,218)' fill='outline' round >{data?.store?.storeNature}</Tag>)
          }

        </p>

        {/*24小时热线*/}
        <div className="flex flex-col rounded-lg space-y-3 px-4 py-3" style={{background: 'rgba(243,244,248, 0.5)'}}
             onClick={(e) => handlePhoneCall(e, data?.store?.storeTelephone)}>
          <div className="flex items-center justify-between">
            <span className="text-xs">
              {data?.store.storeTelephone}
            </span>
            <Image
                src="/images/store/store-map-call.png"
                alt="arrow-right"
                width={20}
                height={20}
            />
          </div>
        </div>

        {/*导航*/}
        <div className="flex flex-col rounded-lg space-y-3 px-4 py-3" style={{background: 'rgba(243,244,248, 0.5)'}}
             onClick={(e) => {
               e.stopPropagation();
               setVisible(true);
               setCurrentItem(data?.store);
             }}>
          <div className="flex items-center justify-between">
                <span className="text-xs pr-8 text-pretty">
                  {data?.store.address}
                </span>
            <Image
                src="/images/store/store-map-navigation.png"
                alt="arrow-right"
                width={20}
                height={20}
            />
          </div>
          <div className="h-40 rounded-lg" ref={elRef}></div>
        </div>

        {/*服务类型*/}
        <div className="flex  flex-col bg-white rounded-lg space-y-3 px-4 py-3">
          <div className="flex items-center justify-between">
            <span>服务类型</span>
          </div>
          <div className="flex px-4 justify-items-start text-center text-3xs">
            {(data?.store?.serverItemName?.split('、') || []).map((_:any, index:number) => (
                    <div key={index} className="flex flex-col items-center mr-10">
                      <Image
                          width={30}
                          height={30}
                          priority
                          alt={_}
                          src={getServiceTypeIcon(_)}
                      />
                      <p className="mt-2">{_}</p>
                    </div>
            ))}
          </div>
        </div>
      </div>

      <ActionSheet
        cancelText="取消"
        visible={visible}
        actions={[
          { text: '腾讯地图', key: 'txLauncher' },
          {
            text: '百度地图',
            key: 'bdLauncher',
          },
          {
            text: '高德地图',
            key: 'amapLauncher',
          },
        ]}
        onClose={() => setVisible(false)}
        onAction={handleAction}
      />
    </Page>
  );
}
