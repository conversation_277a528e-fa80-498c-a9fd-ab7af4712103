'use client';

import React, { useEffect, useRef, useState, useTransition} from "react";
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import {ActionSheet, FloatingPanel, FloatingPanelRef, SearchBar, Space, Tag, Toast} from "antd-mobile";
import type { Action } from 'antd-mobile/es/components/action-sheet';
import Page from '@/components/Page';
import SafeArea from '@/components/SafeArea';
import { useAmap } from '@/hooks/useAmap';
import { useQuery } from '@tanstack/react-query';
import { getStore } from '@/server/api/store';
import ScrollIntoViewIfNeeded from "@/components/ScrollIntoViewIfNeeded";

export default function Store() {
  const router = useRouter();
  const { elRef, map } = useAmap();
  const [visible, setVisible] = useState(false);
  const [currentItem, setCurrentItem] = useState<any>();
  const [searchStoreName, setSearchStoreName] = useState<any>();
  const [userPosition, setUserPosition] = useState<number[]>([]);
  const [selectedKey, setSelectedKey] = useState<string | null>(null);
  const floatingPanelRef = useRef<FloatingPanelRef>(null)

  const anchors = [355, window.innerHeight * 0.8]

  const { data: list, refetch } = useQuery({
    queryKey: ['store-list'],
    queryFn: () => getStore(userPosition[0], userPosition[1], searchStoreName),
    enabled: userPosition.length > 0,
    // queryFn: () => getStore(121.536302,28.594273, searchStoreName),
    // enabled: true,
  });

  useEffect(() => {
    if (map) {
      // 删除地图上所有的覆盖物
      map.clearMap();
      window.jsBridgeHelper?.sendMessage('queryLocation').then((res: any) => {
        if (res.code === '200') {
          setUserPosition([res.data.longitude, res.data.latitude]);
          const userMarker = new AMap.Marker({
            position: [res.data.longitude, res.data.latitude] as unknown as AMap.LngLatLike,
            icon: new AMap.Icon({
              size: new AMap.Size(34, 40),
              imageSize: new AMap.Size(34, 40),
              image: '/images/store/store-my-position.png',
            }),
            offset: new AMap.Pixel(-14, -44),
          });
          map.add(userMarker);
        }
      });

      const markers: AMap.Marker[] = [];
      (list || []).forEach((item: any, index: number) => {
        const { storeGpsLng, storeGpsLat } = item;
        const marker = new AMap.Marker({
          position: [storeGpsLng, storeGpsLat],
          icon: new AMap.Icon({
            image: index === 0 ? '/images/<EMAIL>' : '/images/<EMAIL>',
            size: new AMap.Size(34, 40),
            imageSize: new AMap.Size(34, 40),
          }),
          offset: new AMap.Pixel(-17, -44),
          extData: item.id,
        });

        if (index === 0) {
          map.setZoomAndCenter(12, marker?.getPosition() as unknown as AMap.LngLatLike);
        }

        markers.push(marker);

        //信息窗口
        const info = [
          `
          <div class='relative'>
            <div class='bg-white rounded-lg p-2 text-3xs'>
              <div>${item.storeName}</div>
              <div class='text-center'>距您${item.distance}km</div>
            </div>
            <div class='overflow-hidden w-0 h-0
              border-8 border-t-white border-l-transparent border-r-transparent border-b-transparent
              left-1/2 -translate-x-1/2 absolute -bottom-4'
            />
          </div>`,
        ];

        const infoWindow = new AMap.InfoWindow({
          isCustom: true,
          content: info.join(''),
          offset: new AMap.Pixel(0, -46),
        });
        marker.on('click', (e) => {
          infoWindow.open(map!, e.target.getPosition());
          map.setZoomAndCenter(12, e.target.getPosition());
          // 当点击地图上的marker时，在门店列表中高亮对应的门店，并滚动到可视区域
          setSelectedKey(e.target.getExtData());
        });
        marker.on('touchstart', (e) => {
          infoWindow.open(map!, e.target.getPosition());
          map.setZoomAndCenter(12, e.target.getPosition());
          // 当点击地图上的marker时，在门店列表中高亮对应的门店，并滚动到可视区域
          setSelectedKey(e.target.getExtData());
        });
      });
      map.add(markers);
    }
  }, [list, map]);

  // 导航
  const handleAction = (action: Action) => {
    setVisible(false);
    window.jsBridgeHelper?.sendMessage(action.key, {
      lat: currentItem.storeGpsLat,
      lon: currentItem.storeGpsLng,
    });
  };

  // 拨号
  const handlePhoneCall = (phone: string) => {
    window.jsBridgeHelper?.sendMessage('phoneCall', phone);
  };

  // 定位
  const handleUserView = () => {
    window.jsBridgeHelper?.sendMessage('queryLocation').then((res: any) => {
      if (res.code === '200') {
        map?.setZoomAndCenter(12, [
          res.data.longitude,
          res.data.latitude,
        ] as unknown as AMap.LngLatLike);
      }
    });
  };

  // 门店搜索
  const storeSearchHandle = (val:string) => {
    setSearchStoreName(val)
    setTimeout(()=> {
      refetch()
    }, 200)
  }

  return (
    <Page title="附近网点" navBgColor="white">
      <div className="relative flex h-full flex-col overflow-hidden">
        <Space block direction='vertical' className="z-10 w-full pl-4 pr-4 absolute top-2">
          <SearchBar
            style={{ '--background': 'rgba(255,255,255,0.8)' }}
            onChange={ storeSearchHandle}
            placeholder='搜索附近网点'
          />
        </Space>
        <div className="z-0 h-[calc(100%-355px)]" ref={elRef}></div>
        <div className="z-10 -mt-[84px] flex justify-end">
          <div className="mb-3 mr-4 w-10 rounded-xl bg-white p-2" onClick={handleUserView}>
            <Image src="/images/store/store-curr-position.png" alt="lock" width={24} height={24} />
          </div>
        </div>
      </div>

      <FloatingPanel anchors={anchors} ref={floatingPanelRef}>
        <div>
          {!list?.length ? (
              <div className="flex h-full flex-col items-center justify-center">
                <Image src="/images/empty-content.png" alt="empty" width={200} height={200} />
                <div className="mt-3 text-3xs text-color-weak">暂无网点</div>
              </div>
          ) : (
              <div className="space-y-2 pb-2" style={{backgroundColor: 'rgb(243,244,288)'}}>
                {(list || []).map((item: any, index: number) => (
                    <ScrollIntoViewIfNeeded key={item.id} active={item.id === selectedKey}>
                      <div key={index}
                           className="flex bg-white p-3"
                           onClick={() => {
                             router.push(`store/${item.id}`);
                           }}
                      >
                        <div className="flex-1">
                          <div className="h-20 space-y-2 px-3 ">
                            <div className="text-sm font-medium">
                              {item.storeName}
                              {index === 0 && (<Tag className="ml-2" color='primary' fill='outline' round >当前最近</Tag>)}
                            </div>
                            <div className="text-3xs text-color-weak">
                              营业时间：{item.businessTime}
                            </div>
                            <div className="flex w-full items-center">
                              <span className="line-clamp-1 flex-1 text-3xs text-color-weak">
                                {item.address}
                              </span>
                              <span className="text-2xs font-medium text-color-weak">{item.distance}KM</span>
                            </div>
                          </div>
                          <div className="mt-2 grid h-6 grid-cols-2 gap-3">
                            <div
                                className="flex items-center justify-center rounded"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setVisible(true);
                                  setCurrentItem(item);
                                }}
                            >
                              <Image
                                  alt="导航"
                                  src="/images/<EMAIL>"
                                  width={20}
                                  height={20}
                              />
                              <span data-track="附近网点-导航" className="ml-1.5 text-sm">
                                导航
                              </span>
                            </div>
                            <div
                                className="flex items-center justify-center rounded"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handlePhoneCall(item.storeTelephone);
                                }}
                            >
                              <Image
                                  alt="电话"
                                  src="/images/<EMAIL>"
                                  width={20}
                                  height={20}
                              />
                              <span data-track="附近网点-电话" className="ml-1.5 text-sm">
                                电话
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </ScrollIntoViewIfNeeded>
                ))}
              </div>
          )}
          <SafeArea position="bottom" />
        </div>
      </FloatingPanel>

      <ActionSheet
          cancelText="取消"
          visible={visible}
          actions={[
            { text: '腾讯地图', key: 'txLauncher' },
            {
              text: '百度地图',
              key: 'bdLauncher',
            },
            {
              text: '高德地图',
              key: 'amapLauncher',
            },
          ]}
          onClose={() => setVisible(false)}
          onAction={handleAction}
      />
    </Page>
  );
}
