// @ts-nocheck

'use client'

import Button from '@/components/Button'
import List from '@/components/List'
import Page from '@/components/Page'
import { getBatteryList } from '@/server/actions/battery'
import { getBindCancel, updateVehicleName } from '@/server/actions/vehicle'
import { getVehicleDetail, getVehicleStatus } from '@/server/api/home'
import { toImg } from '@/utils'
import * as AspectRatio from '@radix-ui/react-aspect-ratio'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Dialog, Input, Toast } from 'antd-mobile'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useEffect, useState, useTransition } from 'react'

export default function Vehicle({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [isPending, startTransition] = useTransition()
  const [batteryList, setBatteryList] = useState([])
  const [visible, setVisible] = useState(false)
  // 车辆名称
  const [vehicleName, setVehicleName] = useState('')
  const queryClient = useQueryClient()

  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id),
    enabled: !!params.id
  })

  const { data: status, refetch } = useQuery({
    queryKey: ['vehicle', 'status', params.id],
    queryFn: () => getVehicleStatus(params.id),
    enabled: !!params.id
  })

  useEffect(() => {
    startTransition(async () => {
      const data: any = await getBatteryList(params.id)
      console.log('[ getBatteryList ] >', data)
      if (data?.error) {
        Toast.show(data.error)
        return
      }
      setBatteryList(data || [])
    })
  }, [params.id])

  const toUnBind = () => {
    Dialog.confirm({
      content: '确定解绑车辆？',
      onAction: (action) => {
        if (action.key === 'confirm') {
          startTransition(async () => {
            const result: any = await getBindCancel({ deviceNo: params.id })
            if (result?.error) {
              Toast.show(result.error)
            } else {
              queryClient.setQueryData(['info'], {
                ...queryClient.getQueryData(['info']),
                deviceNo: result.newChoose
              })
              Toast.show('解绑成功')
              router.back()
            }
          })
        }
      }
    })
  }

  return (
    <Page title="车辆管理" navStyle="white">
      <div className="absolute left-0 top-0 -z-10 h-[294px] w-full bg-[url('/images/G/<EMAIL>')] bg-cover bg-center bg-no-repeat" />
      <div className="my-6 flex items-start pr-7">
        <div className="flex flex-1 items-center">
          <div className="relative w-[136px]">
            <AspectRatio.Root ratio={482 / 375}>
              <Image
                src={status?.img ? toImg(status.img) : '/images/<EMAIL>'}
                alt="车型图"
                fill
                priority
              />
            </AspectRatio.Root>
          </div>
          <div className="flex-1">
            <div className="text-xl font-semibold text-white">{status?.nickName}</div>
            <div className="mt-1 text-2xs text-white/60">已陪伴{status?.haveTime || 1}天</div>
          </div>
        </div>
        <Image
          data-track="车辆管理-车辆名称修改"
          src="/images/G/<EMAIL>"
          alt="编辑"
          width={24}
          height={24}
          onClick={() => {
            setVisible(true)
            setVehicleName(status?.nickName)
          }}
        />
        <Dialog
          visible={visible}
          content={
            <div className="flex flex-col items-center justify-center px-2 text-xl">
              <div className="text-center">车辆名称</div>
              <Input
                value={vehicleName}
                maxLength={15}
                placeholder="请输入车辆名称"
                className="mb-2 mt-3 h-10 rounded border border-[#e5e5e5] px-2"
                onChange={(v) => {
                  setVehicleName(v)
                }}
              />
            </div>
          }
          actions={[
            [
              {
                key: 'cancel',
                text: '取消'
              },
              {
                key: 'confirm',
                text: '确认'
              }
            ]
          ]}
          onAction={async (action) => {
            if (action.key === 'confirm') {
              // 提交
              const result = await updateVehicleName({
                deviceNo: params.id,
                nickName: vehicleName
              })
              if (result?.error) {
                Toast.show(result.error)
              } else {
                refetch()
                Toast.show('修改成功')
                setVisible(false)
              }
            } else {
              setVisible(false)
            }
          }}
          afterClose={() => setVehicleName('')}
        />
      </div>
      <div className="space-y-3 px-3">
        <List
          items={[
            {
              key: 1,
              icon: '/images/G/<EMAIL>',
              title: '车辆信息',
              href: `${params.id}/info`,
              show: detail?.scheme.includes('vehicle_info')
            },
            {
              key: 7,
              icon: '/images/G/icon-clsz-ccms-1.png',
              title: '胎压监测',
              href: `${params.id}/tire`,
              show: detail?.scheme.includes('tire_pressure')
            },
            {
              key: 2,
              icon: '/images/G/<EMAIL>',
              title: '车辆设置',
              href: `${params.id}/setting`,
              show: detail?.scheme.includes('vehicle_set')
            },
            {
              key: 3,
              icon: '/images/G/<EMAIL>',
              title: '电池管理',
              href: status?.newBattery
                ? `${params.id}/battery/list`
                : `${params.id}/battery/${batteryList[0]?.id}/setting`,
              show: detail?.scheme.includes('battery_manage')
            },
            {
              key: 4,
              icon: '/images/G/<EMAIL>',
              title: '设备管理',
              href: `${params.id}/device`,
              show: detail?.scheme.includes('equ_manage')
            },
            {
              key: 5,
              icon: '/images/G/<EMAIL>',
              title: '分享账号管理',
              href: `${params.id}/share`,
              show: detail?.isOwner === 1
            },
            {
              key: 6,
              icon: '/images/G/<EMAIL>',
              title: '附近网点',
              href: `${params.id}/store`
            }
          ]}
        />
        <Button data-track="解绑车辆" block onClick={toUnBind}>
          解绑车辆
        </Button>
      </div>
    </Page>
  )
}
