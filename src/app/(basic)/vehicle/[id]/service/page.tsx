// @ts-nocheck

'use client';

import React, { useEffect, useState, useTransition } from 'react';
import { Toast } from 'antd-mobile';
import dayjs from 'dayjs';
import { useRouter } from 'next/navigation';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { timestampToDate } from '@/utils';
import { getExpired, getMedicalReport, vehicleMedical } from '@/server/actions/service';
import { getVehicleDetail } from '@/server/api/vehicle';

export default function Service({ params }: { params: { id: string } }) {
  const router = useRouter();

  const [isPending, startTransition] = useTransition();
  const [list, setList] = useState([]);
  const [menuList, setMenuList] = useState([]);
  const [lastTime, setLastTime] = useState('');
  const [state, setState] = useState(0);
  const [isAll, setIsAll] = useState(true);
  const [service, setService] = useState({
    expired: '',
  });

  const stateList = { 0: '立即体检', 1: '体检进行中' };
  const { id } = params;
  let interval;

  useEffect(() => {
    toList(id);
    toExpired(id);
  }, [id]);

  useEffect(() => {
    console.log('[ state ] >', state);
    if (state === 1) {
      clearInterval(interval);
      // eslint-disable-next-line react-hooks/exhaustive-deps
      interval = setInterval(() => {
        startTransition(async () => {
          const data: any = await getMedicalReport(id);
          console.log('[ data ] >', data);
          if (data?.error) {
            Toast.show(data.error);
            return;
          }
          console.log('[ data ] >', data);
          console.log('[ lastTime ] >', lastTime);
          if (data.bumpDvcMedical && data.bumpDvcMedical?.gmtMedical !== lastTime) {
            Toast.show('体检完成');
            setState(0);
            clearInterval(interval);
            setLastTime(data.bumpDvcMedical?.gmtMedical || '');
            setList(toCheckList(isAll, data.bumpDvcMedical));
          }
        });
      }, 5000);
      setTimeout(() => {
        console.log('[  ] >', '体检失败');
        setState(0);
        clearInterval(interval);
        interval && Toast.show('体检失败');
      }, 5000 * 4);
    } else {
      clearInterval(interval);
    }
    return () => {
      clearInterval(interval);
    };
  }, [state, lastTime, id, isAll]);

  const toList = (id) => {
    startTransition(async () => {
      const data: any = await getMedicalReport(id);
      const data2: any = await getVehicleDetail(id);
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      if (data2?.error) {
        Toast.show(data2.error);
        return;
      }
      const { scheme } = data2;
      // console.log('[ scheme ] >', scheme);
      console.log('[ 是否车主 ] >', data.isOwner);
      console.log('[ 全功能 ] >', scheme.includes('power_system'));
      console.log('[ 简版 ] >', scheme.includes('intelligent_service'));
      if (!data.isOwner) {
        setList([]);
      } else if (scheme.includes('power_system')) {
        setIsAll(true);
        setList(toCheckList(true));
      } else if (scheme.includes('intelligent_service')) {
        setIsAll(false);
        setList(toCheckList(false));
      } else {
        setList([]);
      }

      setLastTime(data.bumpDvcMedical.gmtMedical || '');
    });
  };

  const toCheckList = (isAll, data = {}) => {
    // 0: '正常', 1: '异常', 11: '无此功能'
    if (isAll) {
      // 全功能
      return [
        { name: '动力系统', icon: '/images/check/1', state: data.power },
        { name: '电源系统', icon: '/images/check/2', state: data.electricSource },
        { name: '照明系统', icon: '/images/check/3', state: data.light },
        { name: '安防系统', icon: '/images/check/4', state: data.security },
        { name: '通信系统', icon: '/images/check/5', state: data.communication },
      ];
    } else {
      return [
        { name: '智能服务', icon: '/images/check/fuwu-bx-znfw2', state: data.smartServices },
        { name: 'GPRS连接', icon: '/images/check/fuwu-bx-gprs2', state: data.gprs },
        { name: 'GPS信号', icon: '/images/check/fuwu-bx-gpsxh2', state: data.gps },
        { name: '整车电源', icon: '/images/check/fuwu-bx-zcdy2', state: data.mainBattery },
        { name: 'VCU电池', icon: '/images/check/fuwu-bx-vcudc2', state: data.spareBattery },
      ];
    }
  };
  const toExpired = (id) => {
    startTransition(async () => {
      const data: any = await getExpired(id);
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      console.log('[ data ] >', data);
      setService({
        expired: data?.isExpired
          ? '已过期'
          : `${dayjs(data.expired).diff(dayjs(data.time), 'day') + 1}天`,
      });
      setMenuList([
        {
          name: '建议与反馈',
          icon: '/images/F/<EMAIL>',
          to: '/me/feedback',
          disable: false,
        },
        {
          name: '自检手册',
          icon: '/images/F/<EMAIL>',
          to: `${process.env.NEXT_PUBLIC_PHP_URL}/home/<USER>/index`,
          disable: false,
        },
        {
          name: '智能服务',
          icon: '/images/F/<EMAIL>',
          to: 'service/purchase',
          disable: false,
          hide: !data.haveIntelligentService,
        },
        {
          name: '新手教程',
          icon: '/images/F/<EMAIL>',
          to: `${process.env.NEXT_PUBLIC_PHP_URL}/home/<USER>/index`,
          disable: false,
        },
        {
          name: '产品说明书',
          icon: '/images/F/<EMAIL>',
          to: `/vehicle/${id}/service/book`,
          disable: false,
        },
        { name: '商城', icon: '/images/F/<EMAIL>', disable: true },
        { name: '保险', icon: '/images/F/<EMAIL>', disable: true },
      ]);
    });
  };

  const toMedical = () => {
    startTransition(async () => {
      const data: any = await vehicleMedical(id);

      console.log('[ data ] >', data);
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      setList(toCheckList(isAll));
      setState(1);
    });
  };

  return (
    <Page title="服务">
      <div className="absolute left-0 top-0 -z-10   flex h-[500px] w-full flex-col items-center bg-[url('/images/<EMAIL>')] bg-cover bg-center bg-no-repeat pt-[44px]" />

      {list.length > 0 ? (
        <div className="flex flex-wrap  px-2 py-6">
          {list.map((_, index) => (
            <div key={_.icon} className="mb-3 w-1/3 px-1" style={{ width: index > 2 && '50%' }}>
              <div
                className="color-[#30333F] flex w-full flex-col items-center justify-center rounded-3xl px-3 py-3 text-xl"
                style={{
                  background: 'linear-gradient(180deg, #FFF 0%, #E2E8F0 100%)',
                  boxShadow: '0px 8px 16px 0px rgba(57, 68, 83, 0.10)',
                }}
              >
                {/* animate-spin */}
                <div className="relative mb-1 h-[80px]  w-full   p-5">
                  <div
                    className={
                      state
                        ? "absolute left-0 top-0  h-full w-full animate-spin bg-[url('/images/check/<EMAIL>')] bg-contain bg-center bg-no-repeat"
                        : "absolute left-0  top-0 h-full w-full bg-[url('/images/check/<EMAIL>')] bg-contain bg-center bg-no-repeat"
                    }
                    style={{
                      backgroundImage:
                        "url('" +
                        '/images/check/ellipse' +
                        (_.state ? '-<EMAIL>' : '@2x.png') +
                        "')",
                    }}
                  />
                  <div
                    className="h-full w-full bg-contain bg-center bg-no-repeat"
                    style={{
                      backgroundImage:
                        "url('" + _.icon + (_.state ? '-<EMAIL>' : '@2x.png') + "')",
                    }}
                  />
                </div>
                {_.name}
              </div>
            </div>
          ))}
          <div data-track="立即体验" className="mt-3 w-full px-8 text-center text-[#908F94]">
            <Button block type="primary" onClick={toMedical} loading={state === 1}>
              {stateList[state]}
            </Button>
            <div className="mt-3 ">上次体检时间</div>
            <div>{lastTime ? timestampToDate(lastTime) : '暂无体检记录'}</div>
          </div>
        </div>
      ) : (
        <div className="my-8 h-[150px] w-full   bg-[url('/images/F/fuwu.png')] bg-contain bg-center bg-no-repeat " />
      )}

      <div className="flex flex-wrap  px-2 py-2">
        {menuList
          .filter((item) => !item.hide)
          .map((_, index) => (
            <div key={_.icon} className="mb-2 w-1/3 px-1">
              <div
                className="relative flex w-full flex-col items-center justify-center overflow-hidden rounded-2xl bg-white px-3  py-4 "
                style={{ color: _.disable ? '#C9CDD3' : '#30333F' }}
                onClick={() => {
                  // 是否为https外链
                  if (_.to?.startsWith('https')) {
                    window.jsBridgeHelper?.sendMessage('webBrowse', {
                      navBar: true,
                      url: _.to,
                    });
                    return;
                  }
                  if (_.to) router.push(_.to);
                }}
              >
                <div
                  data-track={_.name}
                  className="mb-2 mt-3 h-[40px] w-full bg-contain bg-center bg-no-repeat"
                  style={{
                    backgroundImage: "url('" + _.icon + "')",
                  }}
                />
                {_.name}
                {_.disable && (
                  <div
                    className=" absolute left-0 top-0 px-2 py-1 text-2xs text-[#908F94]"
                    style={{
                      borderRadius: '16px 4px',
                      background:
                        'linear-gradient(99deg, #C9CDD3 11.87%, rgba(238, 241, 244, 0.84) 113.81%)',
                    }}
                  >
                    敬请期待
                  </div>
                )}

                {_.to === 'service/purchase' && service.expired && (
                  <div
                    className=" absolute left-0 top-0 px-3 py-1 text-2xs text-white"
                    style={{
                      borderRadius: '16px 4px',
                      background: 'linear-gradient(129deg, #406FE5 17.54%, #6FD0FF 84.87%)',
                    }}
                  >
                    {service.expired}
                  </div>
                )}
              </div>
            </div>
          ))}
      </div>
    </Page>
  );
}
