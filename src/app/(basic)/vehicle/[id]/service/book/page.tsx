// @ts-nocheck

'use client';

import React, { useEffect, useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Page from '@/components/Page';
import { bookList } from '@/server/actions/service';
import { Toast } from 'antd-mobile';
import { timestampToDate, toImg } from '@/utils';

export default function ServiceBook() {
  const router = useRouter();

  const [isPending, startTransition] = useTransition();
  const [list, setList] = useState([]);

  useEffect(() => {
    startTransition(async () => {
      const data = await bookList({ page: 1, pageSize: 100 });
      console.log('[ data ] >', data);
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      setList(data.page.records);
    });
  }, []);

  return (
    <Page title="产品说明书">
      <div className="space-y-3 px-3 py-6">
        {list.map((_: any) => (
          <div
            key={_.id}
            className="w-full"
            style={{ marginBottom: '1.5rem' }}
            onClick={() => {
              // 1外部链接 2文件
              if (_.url && _.manualType === 1) {
                // router.push(_.url)
                window.jsBridgeHelper?.sendMessage('webBrowse', {
                  url: _.url,
                  navBar: false,
                  title: '产品说明书',
                });
              }
              if (_.url && _.manualType === 2) {
                // router.push(toImg(_.url));
                window.jsBridgeHelper?.sendMessage('webBrowse', {
                  url: toImg(_.url),
                  navBar: false,
                  title: '产品说明书',
                  offlinePre: true,
                });
              }
            }}
          >
            <div className="w-full  rounded-2xl bg-white   p-4">
              <div className="line-clamp-1 text-2xl">{_.name}</div>
              <div className="mt-2 line-clamp-2 text-xs text-color-weak">
                {/* {timestampToDate(_.updateTime)} */}
                产品使用说明
              </div>
            </div>
          </div>
        ))}
      </div>
    </Page>
  );
}
