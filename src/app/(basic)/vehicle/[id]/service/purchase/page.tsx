'use client';

import { useState, useMemo, useEffect, useTransition } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Checkbox, Toast } from 'antd-mobile';
import { CheckOutline, CheckCircleFill } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import Page from '@/components/Page';
import SafeArea from '@/components/SafeArea';
import List from '@/components/List';
import Button from '@/components/Button';
import { useQuery } from '@tanstack/react-query';
import { getExpireTime, getServiceList } from '@/server/api/service';
import { aliPay, wechatPay } from '@/server/actions/service';
import { toImg } from '@/utils';
import * as AspectRatio from '@radix-ui/react-aspect-ratio';
import { useNative } from '@/lib/native';

export default function Purchase({ params }: { params: { id: string } }) {
  const [checked, setChecked] = useState(false);
  // 支付方式
  const [payType, setPayType] = useState<number | undefined>(); // 1 支付宝 3 微信
  // 当前选择的服务
  const [selectedService, setSelectedService] = useState<Record<string, any>>({});
  const [isPending, startTransition] = useTransition();
  const { hasWechat } = useNative();
  const hasAlipay = true;

  const { data: vehicleInfo } = useQuery({
    queryKey: ['vehicle-info', params.id],
    queryFn: () => getExpireTime(params.id),
  });
  const { data: service } = useQuery({
    queryKey: ['service-list'],
    queryFn: getServiceList,
  });
  const serviceList = useMemo(() => {
    return (
      (service?.data.records || [])
        .map((item: any) => {
          // 计算折扣，保留一位小数
          const discount = (((item.discountPrice ?? item.price) / item.price) * 10).toFixed(1);
          // 计算优惠额度
          const discountAmount = item.price - (item.discountPrice ?? item.price);
          // 是否有折扣
          const hasDiscount =
            (item.discountPrice !== null && !item.expirationTime) ||
            (item.discountPrice !== null &&
              item.expirationTime &&
              dayjs(service.time).isBefore(dayjs(item.expirationTime)));
          return {
            ...item,
            discount,
            discountAmount,
            hasDiscount,
          };
        })
        // 根据优惠额度由多到少排序
        .sort((a: any, b: any) => {
          if (a.hasDiscount && !b.hasDiscount) {
            return -1;
          } else if (!a.hasDiscount && b.hasDiscount) {
            return 1;
          } else {
            return b.discountAmount - a.discountAmount;
          }
        })
    );
  }, [service]);

  useEffect(() => {
    if (serviceList.length > 0) {
      setSelectedService(serviceList[0]);
    }
  }, [serviceList]);

  const handlePay = () => {
    if (!checked) {
      Toast.show('请阅读并同意服务协议');
      return;
    }
    startTransition(async () => {
      // 支付宝
      if (payType === 1) {
        const aliPayResult = await aliPay({
          deviceNo: params.id,
          payType: 1,
          payChannel: 'ALIPAYAPP',
          intelligentServingId: selectedService.id,
        });
        if ('error' in aliPayResult) {
          Toast.show(aliPayResult.error);
        } else {
          window.jsBridgeHelper
            ?.sendMessage('aliPay', aliPayResult.body)
            .then((res: { code: number; result: string }) => {
              if (res.code === 9000) {
                Toast.show('支付成功');
              } else if (res.code === 8000) {
                Toast.show('正在处理中');
              } else if (res.code === 4000) {
                Toast.show('订单支付失败');
              } else if (res.code === 6001) {
                Toast.show('支付已取消');
              } else if (res.code === 6002) {
                Toast.show('网络连接出错');
              }
            });
        }
      } else if (payType === 3) {
        const wechatPayResult = await wechatPay({
          deviceNo: params.id,
          payType: 3,
          payChannel: 'APP',
          intelligentServingId: selectedService.id,
        });
        if ('error' in wechatPayResult) {
          Toast.show(wechatPayResult.error);
        } else {
          window.jsBridgeHelper
            ?.sendMessage('wxPay', wechatPayResult)
            .then((res: number | null) => {
              if (res === 0) {
                Toast.show('支付成功');
              } else if (res === null) {
                Toast.show('支付失败');
              } else if (res === -2) {
                Toast.show('支付已取消');
              }
            });
        }
      }
    });
  };

  return (
    <Page
      title="智能服务"
      className="bg-[url('/images/<EMAIL>')] bg-contain bg-no-repeat"
    >
      <div className="px-3 pb-[calc(116px+32px)] pt-4">
        <div className="relative h-32 w-full">
          <Image src="/images/<EMAIL>" alt="服务" fill />
          <div className="absolute left-0 top-0 flex h-32 w-full items-center px-4">
            <div className="h-[70px] w-[70px] rounded-md bg-gradient-to-b from-[rgba(246,247,251,0.5)] to-[rgba(246,247,251,0)]">
              <div className="relative w-[90px] -translate-x-[10px]">
                <AspectRatio.Root ratio={482 / 375}>
                  <Image
                    src={
                      vehicleInfo?.modelImg ? toImg(vehicleInfo.modelImg) : '/images/<EMAIL>'
                    }
                    alt="车型图"
                    fill
                    priority
                  />
                </AspectRatio.Root>
              </div>
            </div>
            <div className="ml-5 flex flex-col text-xs text-white">
              <div>智能服务剩余天数</div>
              <div>
                {vehicleInfo?.expired ? (
                  vehicleInfo.isExpired ? (
                    <span className="text-color-weak">已过期</span>
                  ) : (
                    <>
                      <span className="text-6xl font-bold">
                        {dayjs(vehicleInfo.expired).diff(vehicleInfo.time, 'day') + 1}
                      </span>
                      <span className="ml-1 text-color-weak">天</span>
                    </>
                  )
                ) : (
                  '--'
                )}
              </div>
              <div className="text-3xs text-color-weak">
                服务有效期：
                {vehicleInfo?.expired ? dayjs(vehicleInfo.expired).format('YYYY-MM-DD') : '--'}
              </div>
            </div>
          </div>
        </div>
        {serviceList.length > 0 ? (
          <div className="w-full">
            <div className="z-0 mb-3 mt-4 text-xs text-color-secondary">智能服务续费</div>
            <div className="relative flex w-full snap-x snap-mandatory flex-nowrap gap-2 overflow-x-auto scrollbar-none">
              {serviceList.map((item: any) => (
                <div className="w-[calc(40%-8px)] flex-none snap-start" key={item.id}>
                  <div
                    className={`relative mt-3 flex h-[120px] flex-col items-center rounded-xl py-5 ${
                      selectedService.id === item.id
                        ? 'bg-gradient-135 from-[#585C78] from-[-6%] to-[#30333F] to-[106%]'
                        : 'bg-white'
                    }`}
                    onClick={() => setSelectedService(item)}
                  >
                    <div
                      className={`text-xs font-medium leading-none ${
                        selectedService.id === item.id ? 'text-white' : 'text-color-secondary'
                      } `}
                    >
                      {item.flowTime}天
                    </div>
                    <div className="mt-1.5 space-x-1">
                      <span
                        className={`text-3xs leading-none ${
                          selectedService.id === item.id ? 'text-white' : 'text-color-weak'
                        } `}
                      >
                        ¥
                      </span>
                      <span
                        className={`text-6xl font-bold leading-none ${
                          selectedService.id === item.id ? ' text-white/90' : ' text-color-text'
                        }`}
                      >
                        {item.hasDiscount ? item.discountPrice / 100 : item.price / 100}
                      </span>
                    </div>
                    {item.hasDiscount && (
                      <>
                        <div
                          className={`mt-1.5 text-3xs leading-none line-through ${
                            selectedService.id === item.id ? 'text-white' : 'text-color-weak'
                          }`}
                        >
                          ¥{item.price / 100}
                        </div>
                        <div
                          className={`absolute left-0 top-0 z-10 -translate-y-1/2 rounded-br-xl rounded-tl-xl  px-2 py-[2px] text-3xs  ${
                            selectedService.id === item.id
                              ? 'bg-gradient-129 from-[#406FE5] from-[17%] to-[#6FD0FF] to-[84%] text-white'
                              : 'bg-[#FFF9EB] text-[#C19030]'
                          }`}
                        >
                          限时{item.discount}折
                        </div>
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-3 rounded-2xl bg-gradient-to-b from-[#C9CDD3] from-[-17%] to-[#EEF1F4] to-[73%] px-4 py-3">
              <div className="text-sm">
                <span>购买{selectedService.flowTime || '--'}天智能服务</span>
                {selectedService.hasDiscount && (
                  <span>
                    ，立省
                    {selectedService.discountAmount / 100}元
                  </span>
                )}
              </div>
              <div className="mt-1.5 text-xs text-color-weak">
                服务延迟至：
                {vehicleInfo?.expired && service?.time
                  ? dayjs(vehicleInfo.expired > service.time ? vehicleInfo.expired : service.time)
                      .add(selectedService?.flowTime, 'day')
                      .format('YYYY-MM-DD')
                  : '--'}
              </div>
              {selectedService.expirationTime && selectedService.hasDiscount && (
                <div className="mt-1.5 text-xs text-color-weak">
                  活动截止到：{dayjs(selectedService.expirationTime).format('YYYY-MM-DD')}
                </div>
              )}
            </div>
            {(hasWechat || hasAlipay) && (
              <div className="my-3 text-xs text-color-secondary">支付方式</div>
            )}
            <List
              size="large"
              items={[
                {
                  title: '微信',
                  icon: '/images/F/<EMAIL>',
                  value: (
                    <CheckOutline fontSize={20} color={payType === 3 ? '#30333F' : '#C9CDD3'} />
                  ),
                  onClick: () => setPayType(3),
                  show: hasWechat,
                },
                {
                  title: '支付宝',
                  icon: '/images/F/<EMAIL>',
                  value: (
                    <CheckOutline fontSize={20} color={payType === 1 ? '#30333F' : '#C9CDD3'} />
                  ),
                  onClick: () => setPayType(1),
                  show: hasAlipay,
                },
              ]}
            />
            <SafeArea position="bottom" />
          </div>
        ) : (
          <div className="w-full pt-20 text-center">
            <div className="h-[300px] w-full  bg-[url('/images/empty-content.png')] bg-contain bg-center bg-no-repeat " />
            <span className="text-xl text-color-weak">暂无智能服务</span>
          </div>
        )}
      </div>
      {serviceList.length > 0 && (hasWechat || hasAlipay) && (
        <div className="fixed bottom-0 left-0 right-0 bg-white">
          <div className="py-3">
            <div className="flex items-center justify-center text-xs text-color-weak">
              <Checkbox
                icon={(checked) => {
                  return checked ? (
                    <CheckCircleFill color="#585C78" fontSize={22} />
                  ) : (
                    <div className="h-[22px] w-[22px]  rounded-full border border-color-light" />
                  );
                }}
                checked={checked}
                onChange={(value) => setChecked(value)}
              />
              &nbsp;<span>我已阅读同意</span>
              <Link href="purchase/agreement" className="text-color-text">
                《<span className="underline decoration-1">立马智能电动车服务协议</span>》
              </Link>
            </div>
            <div className="mt-5 flex items-center justify-between pl-4 pr-7">
              <div>
                实付：¥
                {selectedService.hasDiscount
                  ? selectedService.discountPrice / 100
                  : selectedService.price / 100}
              </div>
              <Button
                type="primary"
                shape="round"
                className="w-36"
                onClick={handlePay}
                loading={isPending}
                disabled={payType === undefined}
              >
                立即续费
              </Button>
            </div>
          </div>
          <SafeArea position="bottom" />
        </div>
      )}
    </Page>
  );
}
