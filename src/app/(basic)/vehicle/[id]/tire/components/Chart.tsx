/*
 * @Author: <PERSON><PERSON>
 * @Email: <EMAIL>
 * @Date: 2025-03-07 10:18:03
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-03-07 16:04:31
 */
// @ts-nocheck

'use client'

import React from 'react'
import { useEcharts } from '@/hooks/useEcharts'
import * as echarts from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import { GridComponent } from 'echarts/components'
import { formatTime, timestampToDate } from '@/utils'

export default function TirePressureChart({ data }) {
  console.log('[ TirePressureChart ] >', data)

  const { elRef: lineChartRef } = useEcharts([GridComponent, LineChart, CanvasRenderer], {
    grid: {
      top: 30,
      bottom: 40,
      right: 20,
      left: 40
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      axisTick: { show: false },
      splitLine: { show: false },
      axisLine: { show: false },
      axisLabel: {
        color: '#B0B7C3',
        fontSize: 12,
        lineHeight: 16,
        formatter: (value, idx) => value.replace(' ', '\n')
      },
      data: data.time || []
    },
    yAxis: {
      type: 'value',
      name: '',
      axisTick: { show: false },
      splitLine: { show: false },
      axisLine: { show: false },
      axisLabel: {
        color: '#B0B7C3',
        fontSize: 12
      }
    },

    series: [
      {
        data: data.data || [],
        type: 'line',
        smooth: true,
        label: { show: false },
        showSymbol: false,
        lineStyle: {
          width: 3,
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#406FE5' },
            { offset: 1, color: '#6FD0FF' }
          ])
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            {
              offset: 0,
              color: 'rgba(47, 184, 255, 0.5)'
            },
            {
              offset: 1,
              color: 'rgba(158, 236, 217, 0.5)'
            }
          ])
        }
      }
    ]
  })

  return <div ref={lineChartRef} className="h-full w-full" />
}
