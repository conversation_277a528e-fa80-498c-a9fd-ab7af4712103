/*
 * @Author: <PERSON><PERSON>
 * @Email: <EMAIL>
 * @Date: 2025-03-06 14:33:50
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-03-16 15:47:53
 */
// @ts-nocheck
'use client'

import Page from '@/components/Page'
import { useBLEClient } from '@/lib/ble'
import { useNative } from '@/lib/native'
import { getTirePressureData, getTirePressureHistoryData } from '@/server/api/vehicle'
import { state } from '@/store'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Empty } from 'antd-mobile'
import { UndoOutline } from 'antd-mobile-icons'
import { useEffect } from 'react'
import TirePressureChart from './components/Chart'

// 页面主组件
export default function TirePressure({ params }) {
  const { id } = params
  const bleClient = useBLEClient()
  const queryClient = useQueryClient()
  const { clientId } = useNative()
  const deviceNetWorkModule =
    state.communicateTypes.includes(2) || state.communicateTypes.includes(3)
  const queryKey = ['tire-pressure', id]

  useEffect(() => {
    if (bleClient.isConnected()) {
      toBluetooth()
    }
  }, [bleClient, id, clientId, deviceNetWorkModule])

  // 获取轮胎压力实时数据
  const {
    data: tireData = {},
    isLoading: isLoadingTireData,
    refetch: refetchTireData
  } = useQuery({
    queryKey,
    queryFn: () => getTirePressureData({ deviceNo: id }),
    enabled: !!id && !bleClient.isConnected()
  })
  console.log('[ tireData ] >', tireData)

  // 获取轮胎压力历史数据
  const {
    data: tireHistoryData = {},
    isLoading: isLoadingHistoryData,
    refetch: refetchHistoryData
  } = useQuery({
    queryKey: ['tire-pressure-history', id],
    queryFn: () => getTirePressureHistoryData({ deviceNo: id }),
    enabled: !!id
  })
  console.log('[ tireHistoryData ] >', tireHistoryData)

  // 刷新数据 - 同时请求两个接口
  const handleRefresh = () => {
    refetchTireData()
    refetchHistoryData()
  }

  // 判断是否正在加载
  const isLoading = isLoadingTireData || isLoadingHistoryData

  // "2025-03-10T02:09:37.790+00:00"这个格式转成2025-03-10 02:09:37
  const formatTime = (time) => {
    return time.replace('T', ' ').replace('+00:00', '').slice(0, -4)
  }

  const toBluetooth = () => {
    bleClient.getTirePressureData({ deviceNo: id }, (result) => {
      queryClient.setQueryData(queryKey, {
        frontTire: {
          frontTireBar: result[6],
          frontTireBarAlarm: toState(result[6], result[0], result[1]),
          frontTireTemp: result[8],
          frontTireTempAlarm: toState(result[8], result[4])
        },
        backTire: {
          backTireBar: result[7],
          backTireBarAlarm: toState(result[7], result[2], result[3]),
          backTireTemp: result[9],
          backTireTempAlarm: toState(result[9], result[5])
        }
      })
    })
  }

  const toState = (value, top = null, low = null) => {
    //返回值是 0 低压  1 高压 2 正常
    if (low && value < low) {
      return '0'
    } else if (top && value > top) {
      return '1'
    }
    return '2'
  }

  return (
    <Page title="胎压监测">
      <div className="p-4 space-y-4 bg-gray-50 min-h-screen">
        <TirePressureSection
          title="前轮"
          pressure={tireData.frontTire?.frontTireBar}
          pressureState={tireData.frontTire?.frontTireBarAlarm}
          temperature={tireData.frontTire?.frontTireTemp}
          temperatureState={tireData.frontTire?.frontTireTempAlarm}
          chartData={{ time: tireHistoryData.timestamp, data: tireHistoryData.frontTireBar }}
        />
        <TirePressureSection
          title="后轮"
          pressure={tireData.backTire?.backTireBar}
          pressureState={tireData.backTire?.backTireBarAlarm} // 0 低压  1 高压 2 正常
          temperature={tireData.backTire?.backTireTemp}
          temperatureState={tireData.backTire?.backTireTempAlarm} //  1 高压 2 正常
          chartData={{
            time: tireHistoryData.timestamp,
            data: tireHistoryData.backTireBar
          }}
        />

        {tireHistoryData.time && (
          <div className="flex justify-center items-center text-xs text-color-weak mt-4">
            <div className="flex items-center bg-white px-4 py-2 rounded-full  ">
              <span>数据刷新于 {formatTime(tireHistoryData.time)}</span>
              <button
                className="ml-2 p-1 bg-gray-50 rounded-full hover:bg-gray-100 transition-colors"
                onClick={handleRefresh}
              >
                <UndoOutline className={isLoading ? 'animate-spin' : ''} />
              </button>
            </div>
          </div>
        )}
      </div>
    </Page>
  )
}

// 轮胎数据卡片组件
const TireDataCard = ({ title, value, unit, isLow, isTop, showArrow = false }) => {
  return (
    <div className="flex flex-col items-center">
      <div
        className={`text-xl font-medium flex items-center ${
          isLow || isTop ? 'text-[#FF3141]' : 'text-color-text'
        }`}
      >
        {showArrow && isLow ? (
          <span className="text-[#FF3141] mr-1 animate-bounce">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M12 20L4 12L6 10L10 14V4H14V14L18 10L20 12L12 20Z" fill="currentColor" />
            </svg>
          </span>
        ) : null}
        {showArrow && isTop ? (
          <span className="text-[#FF3141] mr-1 animate-bounce">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M12 4L20 12L18 14L14 10V20H10V10L6 14L4 12L12 4Z" fill="currentColor" />
            </svg>
          </span>
        ) : null}
        <span className="font-bold">{value?.toFixed(1) || ' - '}</span>
        <span className="text-sm ml-1 font-normal opacity-80">{unit}</span>
      </div>
      <div className="text-xs text-color-weak mt-2 bg-gray-50 px-3 py-1 rounded-full">{title}</div>
    </div>
  )
}

// 轮胎压力数据部分组件
const TirePressureSection = (data) => {
  const { title, chartData, pressure, pressureState, temperature, temperatureState } = data

  return (
    <div className="bg-white rounded-lg p-4 ">
      <div className="text-lg font-medium mb-4 flex items-center">
        <div className="w-1 h-5 bg-primary rounded-full mr-2"></div>
        {title}
      </div>
      <div className="flex justify-around mb-4">
        <div className="flex-1 flex justify-center">
          <TireDataCard
            title="胎压"
            value={pressure}
            unit="bar"
            isLow={pressureState === '0'}
            isTop={pressureState === '1'}
            showArrow={pressureState !== '2'}
          />
        </div>
        <div className="w-px h-12 bg-gray-100 self-center"></div>
        <div className="flex-1 flex justify-center">
          <TireDataCard
            title="胎温"
            value={temperature}
            unit="°C"
            isTop={temperatureState === '1'}
          />
        </div>
      </div>
      <div className="h-40 w-full">
        {chartData.time?.length > 0 ? (
          <TirePressureChart data={chartData} />
        ) : (
          <Empty description="暂无数据" className="h-40 w-full" />
        )}
      </div>
    </div>
  )
}
