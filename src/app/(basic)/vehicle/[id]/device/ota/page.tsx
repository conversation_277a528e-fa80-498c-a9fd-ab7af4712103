'use client';

import { useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { Toast, Dialog } from 'antd-mobile';
import Page from '@/components/Page';
import Button from '@/components/Button';
import List from '@/components/List';
import { useQuery, useMutation } from '@tanstack/react-query';
import { checkOta, startOta } from '@/server/api/home';
import { useBLEClient } from '@/lib/ble';

export default function DeviceOTA({ params }: { params: { id: string } }) {
  const searchParams = useSearchParams();
  const equiNo = searchParams.get('equiNo') as string;
  const bleClient = useBLEClient();
  const percentageRef = useRef<HTMLSpanElement>(null);

  // 获取固件更新
  const { data: ota } = useQuery({
    queryKey: ['check-ota'],
    queryFn: () => checkOta(params.id),
  });

  // 网络设备升级
  const { mutate: startOtaMutate } = useMutation({
    mutationFn: startOta,
    onSuccess: () => {
      Dialog.alert({
        title: '温馨提示',
        content: '系统已经推送最新固件版本，请您在5~10分钟后确认是否升级成功',
      });
    },
  });

  const handleOta = async () => {
    Dialog.confirm({
      content: '确定进行固件升级？',
      onConfirm: async () => {
        if (searchParams.has('communicationId') && searchParams.get('communicationId') === '1') {
          if (!bleClient.isConnected()) {
            Toast.show('请先连接蓝牙');
            return;
          }
          Toast.show({
            icon: 'loading',
            maskClickable: false,
            duration: 0,
            content: <span ref={percentageRef}>正在下载固件0%</span>,
          });
          const response = await fetch(ota?.[equiNo].up.url);
          const reader = response.body!.getReader();
          const contentLength = response.headers.get('Content-Length');
          const total = contentLength ? parseInt(contentLength, 10) : 0;
          let receivedLength = 0;
          let chunks = [];
          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              break;
            }
            chunks.push(value);
            receivedLength += value.length;
            const percentage = Math.floor((receivedLength / total) * 100);
            percentageRef.current!.innerText = `正在下载固件${percentage}%`;
          }
          percentageRef.current!.innerText = `正在发送固件0%`;
          const arrayBuffer = new Uint8Array(receivedLength);
          let offset = 0;

          for (const chunk of chunks) {
            arrayBuffer.set(chunk, offset);
            offset += chunk.length;
          }
          const byteArray = new Uint8Array(arrayBuffer);
          // 转成16进制
          const hex = Array.from(new Uint8Array(byteArray), (bit) => {
            return ('00' + bit.toString(16)).slice(-2);
          }).join('');
          // 发送数据
          bleClient.ota({
            firmware: hex,
            success: () => {
              Toast.show({
                icon: 'success',
                content: '升级成功',
              });
            },
            fail: () => {
              Toast.show({
                icon: 'fail',
                content: '升级失败',
              });
            },
            onProgress: (process) => {
              percentageRef.current!.innerText = `正在发送固件${process}%`;
            },
          });
        } else {
          // 网络类型设备
          startOtaMutate({
            dvcNo: params.id,
            otaNo: searchParams.get('equiNo') as string,
            version: ota?.[equiNo].up.version as string,
            otaId: ota?.[equiNo].up.id,
          });
        }
      },
    });
  };

  return (
    <Page title="固件升级">
      <div className="space-y-3 px-3 py-6">
        <List
          size="small"
          items={[
            {
              key: 1,
              title: '设备编号',
              value: searchParams.get('equiNo'),
            },
            {
              key: 2,
              title: '当前固件版本',
              value: ota?.[equiNo]?.version,
            },
          ]}
        />
        <List
          size="small"
          items={[
            {
              key: 1,
              title: '最新固件版本',
              value: ota?.[equiNo].up.version,
            },
            {
              key: 2,
              title: '更新日记',
              footer: <p className="px-2 text-xs text-color-weak">{ota?.[equiNo].up.remark}</p>,
            },
          ]}
        />
        <Button block type="primary" onClick={handleOta}>
          立即升级
        </Button>
      </div>
    </Page>
  );
}
