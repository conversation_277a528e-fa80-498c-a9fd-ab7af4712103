'use client'

import List, { type ListProps } from '@/components/List'
import Page from '@/components/Page'
import Switch from '@/components/Switch'
import { useAfterSwitchVehicle } from '@/hooks/useAfterSwitchVehicle'
import { setVehicleSetting } from '@/server/actions/vehicle'
import {
  checkOta,
  deleteDevice,
  getDeviceInfo,
  getVehicleDetail,
  updateDevice
} from '@/server/api/home'
import { toImg } from '@/utils'
import * as AspectRatio from '@radix-ui/react-aspect-ratio'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Dialog, SwipeAction, Toast } from 'antd-mobile'
import { AddCircleOutline } from 'antd-mobile-icons'
import dayjs from 'dayjs'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Fragment } from 'react'

export default function Device({ params }: { params: { id: string } }) {
  const router = useRouter()
  const queryClient = useQueryClient()
  const afterSwitchVehicle = useAfterSwitchVehicle()
  const { data } = useQuery({ queryKey: ['device-info'], queryFn: () => getDeviceInfo(params.id) })
  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', params.id],
    queryFn: () => getVehicleDetail(params.id)
  })
  // 获取固件更新
  const { data: ota } = useQuery({
    queryKey: ['check-ota'],
    queryFn: () => checkOta(params.id)
  })

  // 更换设备
  const { mutate } = useMutation({
    mutationFn: updateDevice,
    onSuccess: () => {
      Toast.show('更换成功')
      queryClient.invalidateQueries({ queryKey: ['device-info'] })
      afterSwitchVehicle()
    }
  })

  // 添加设备
  const handleAdd = () => {
    router.push(`/bind/device?dvcId=${data?.dvcId}`)
  }

  // 删除设备
  const { mutate: deleteFn } = useMutation({
    mutationFn: deleteDevice,
    onSuccess: () => {
      Toast.show('删除成功')
      queryClient.invalidateQueries({ queryKey: ['device-info'] })
    }
  })

  // 更换设备，蓝牙设备和其它设备一样直接更换
  const handleUpdate = (item: any) => {
    window.jsBridgeHelper
      ?.sendMessage('qrCode', {
        title: '更换设备',
        description: '将扫描框对准设备二维码，等待自动扫描'
      })
      .then((res: { code: string; msg: string; data: string }) => {
        if (res.code === '200') {
          // 设备二维码，格式如下
          // Y113580300B23B270033,4C:4D:38:17:00:01
          // S/N:Y113580300B23B270033,4C:4D:38:17:00:01
          // Y113580300B23B270033
          // S/N:Y113580300B23B270033
          const [newEquNo, bluetoothMac = ''] = res.data.split(',')
          mutate({
            oldEquId: item.id,
            newEquNo: newEquNo.replace('S/N:', '').trim(),
            bluetoothMac: bluetoothMac.replace(/:/g, '').trim(),
            dvcId: data?.dvcId
          })
        }
      })
  }

  // 固件智能升级开关
  const handleAutoOta = async (checked: boolean) => {
    queryClient.setQueryData(['device-info'], {
      ...data,
      autoOta: checked ? 1 : 0
    })
    const result = await setVehicleSetting({
      commandType: 0,
      deviceNo: params.id,
      autoOta: checked ? 1 : 0
    })
    if (result?.error) {
      Toast.show(result.error)
      queryClient.setQueryData(['device-info'], data)
    }
  }

  return (
    <Page
      title="设备管理"
      className="bg-[url('/images/<EMAIL>')] bg-cover bg-center bg-no-repeat"
    >
      <div className="px-3 py-6">
        <div className="rounded-xl bg-gradient-129 from-[#585C78] from-[17%] to-[#30333F] to-[84%] px-4 py-6 shadow-[0_8px_16px_0_rgba(57,68,83,0.1)] ">
          <div className="flex items-center">
            <div className="h-[70px] w-[70px] rounded-md bg-gradient-to-b from-[rgba(246,247,251,0.5)] to-[rgba(246,247,251,0)]">
              <div className="relative w-[90px] -translate-x-[10px]">
                <AspectRatio.Root ratio={482 / 375}>
                  <Image
                    src={detail?.img ? toImg(detail.img) : '/images/<EMAIL>'}
                    alt="车型图"
                    fill
                    priority
                  />
                </AspectRatio.Root>
              </div>
            </div>
            <div className="ml-3 space-y-1">
              <div className="text-xl font-semibold text-white">{detail?.nickName}</div>
              <div className="text-2xs text-white/60">车架号：{detail?.deviceNo}</div>
              <div className="text-2xs text-white/60">
                激活时间：
                {detail?.firstBindTime && dayjs(detail.firstBindTime).format('YYYY-MM-DD')}
              </div>
            </div>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex justify-between py-2 text-sm text-color-weak">
            <span>设备信息</span>
            {detail?.scheme.includes('equ_add') && detail?.isOwner === 1 && (
              <AddCircleOutline fontSize={18} color="#2FB8FF" onClick={handleAdd} />
            )}
          </div>
          <div className="space-y-3">
            {data?.equiInfos.map((item: any) => {
              const items: ListProps['items'] = [
                {
                  title:
                    item.scheme?.typeId &&
                    ['', '中控编号', '仪表编号', '云盒编号'][item.scheme?.typeId],
                  value: (
                    <span className="flex">
                      {detail?.scheme.includes('equ_no') && <span>{item.equiNo}</span>}
                      {detail?.scheme.includes('equ_edit') && detail?.isOwner === 1 && (
                        <Image
                          data-track="设备管理-中控编号编辑"
                          src="/images/<EMAIL>"
                          alt="编辑"
                          width={16}
                          height={16}
                          className="ml-3 h-4 w-4"
                          onClick={() => handleUpdate(item)}
                        />
                      )}
                    </span>
                  )
                },
                {
                  title: 'MAC地址',
                  value: item.bluetoothMac,
                  show: detail?.scheme.includes('mac') && item.scheme?.communicationId !== 2
                },
                {
                  title: '蓝牙名称',
                  value: item.bluetoothName,
                  show: detail?.scheme.includes('blue_name') && item.scheme?.communicationId === 1
                },
                {
                  title: 'ICCID',
                  value: item.iccid,
                  show:
                    detail?.scheme.includes('iccid') &&
                    (item.scheme?.communicationId === 2 || item.scheme?.communicationId === 3)
                },
                {
                  title: '固件版本',
                  value:
                    detail?.isOwner === 1 && !!ota?.[item.equiNo]?.up ? (
                      <Link
                        className="text-primary"
                        href={`device/ota?equiNo=${item.equiNo}&communicationId=${item.scheme?.communicationId}`}
                      >
                        可升级
                      </Link>
                    ) : (
                      item.firmwareVersion
                    ),
                  show: detail?.scheme.includes('firmware_version')
                }
              ]
              return (
                <Fragment key={item.id}>
                  {detail?.scheme.includes('equ_delete') && detail?.isOwner === 1 ? (
                    <SwipeAction
                      rightActions={[
                        {
                          key: 'delete',
                          text: '删除',
                          color: 'danger',
                          onClick: async () => {
                            await Dialog.confirm({
                              content: '确定删除设备？',
                              onConfirm: () => {
                                deleteFn(item.id)
                              }
                            })
                          }
                        }
                      ]}
                    >
                      <List size="small" items={items} />
                    </SwipeAction>
                  ) : (
                    <List size="small" items={items} />
                  )}
                </Fragment>
              )
            })}
          </div>
        </div>
        {
          // 无配件信息时不显示
          data?.dvcDetail.motorControllerNo ||
          data?.dvcDetail.lightControllerNo ||
          data?.dvcDetail.chargerNo ||
          data?.dvcDetail.meterNo ||
          data?.dvcDetail.alarmNo ? (
            <div className="py-2 text-sm text-color-weak">配件信息</div>
          ) : null
        }
        <div className="space-y-3">
          {data?.dvcDetail.motorControllerNo && (
            <List
              size="small"
              items={[
                {
                  title: '电机控制器',
                  value: data.dvcDetail.motorControllerNo
                },
                {
                  title: '固件版本',
                  value:
                    detail?.isOwner === 1 && !!ota?.[data.dvcDetail.motorControllerNo]?.up ? (
                      <Link
                        className="text-primary"
                        href={`device/ota?equiNo=${data.dvcDetail.motorControllerNo}`}
                      >
                        可升级
                      </Link>
                    ) : (
                      data?.dvcDetail.motorControllerVersion
                    )
                }
              ]}
            />
          )}
          {data?.dvcDetail.lightControllerNo && (
            <List
              size="small"
              items={[
                {
                  title: '灯光控制器',
                  value: data.dvcDetail.lightControllerNo
                },
                {
                  title: '固件版本',
                  value:
                    detail?.isOwner === 1 && !!ota?.[data.dvcDetail.lightControllerNo]?.up ? (
                      <Link
                        className="text-primary"
                        href={`device/ota?equiNo=${data.dvcDetail.lightControllerNo}`}
                      >
                        可升级
                      </Link>
                    ) : (
                      data?.dvcDetail.lightControllerVersion
                    )
                }
              ]}
            />
          )}
          {data?.dvcDetail.chargerNo && (
            <List
              size="small"
              items={[
                {
                  title: '充电器',
                  value: data.dvcDetail.chargerNo
                },
                {
                  title: '固件版本',
                  value:
                    detail?.isOwner === 1 && !!ota?.[data.dvcDetail.chargerNo]?.up ? (
                      <Link
                        className="text-primary"
                        href={`device/ota?equiNo=${data.dvcDetail.chargerNo}`}
                      >
                        可升级
                      </Link>
                    ) : (
                      data?.dvcDetail.chargerVersion
                    )
                }
              ]}
            />
          )}
          {data?.dvcDetail.meterNo && (
            <List
              size="small"
              items={[
                {
                  title: '仪表',
                  value: data.dvcDetail.meterNo
                },
                {
                  title: '固件版本',
                  value:
                    detail?.isOwner === 1 && !!ota?.[data.dvcDetail.meterNo]?.up ? (
                      <Link
                        className="text-primary"
                        href={`device/ota?equiNo=${data.dvcDetail.meterNo}`}
                      >
                        可升级
                      </Link>
                    ) : (
                      data?.dvcDetail.meterVersion
                    )
                }
              ]}
            />
          )}
          {data?.dvcDetail.alarmNo && (
            <List
              size="small"
              items={[
                {
                  title: '报警器',
                  value: data.dvcDetail.alarmNo
                },
                {
                  title: '固件版本',
                  value:
                    detail?.isOwner === 1 && !!ota?.[data.dvcDetail.alarmNo]?.up ? (
                      <Link
                        className="text-primary"
                        href={`device/ota?equiNo=${data.dvcDetail.alarmNo}`}
                      >
                        可升级
                      </Link>
                    ) : (
                      data?.dvcDetail.alarmVersion
                    )
                }
              ]}
            />
          )}
        </div>
        {detail?.scheme.includes('firmware_ota') && detail?.isOwner === 1 && (
          <div className="mt-3">
            <List
              data-track="设备管理-固件智能升级"
              size="small"
              items={[
                {
                  title: '固件智能升级',
                  description: '打开后，车辆会在最新固件时自动升级',
                  value: <Switch checked={data?.autoOta === 1} onChange={handleAutoOta} />
                }
              ]}
            />
          </div>
        )}
      </div>
    </Page>
  )
}
