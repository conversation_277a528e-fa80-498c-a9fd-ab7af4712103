/*
 * @Date: 2025-03-12 15:09:11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-03-12 15:28:29
 */
// @ts-nocheck
'use client'

import Page from '@/components/Page'
import SafeArea from '@/components/SafeArea'
import { useAmap } from '@/hooks/useAmap'
import { useNative } from '@/lib/native'
import { toImg } from '@/utils'
import * as AspectRatio from '@radix-ui/react-aspect-ratio'
import dayjs from 'dayjs'
import Image from 'next/image'
import { useEffect, useState } from 'react'

export default function Location({ hasLocation, data, detail }) {
  const { elRef, map } = useAmap()
  const [address, setAddress] = useState('暂无车辆位置信息')
  const { safeArea } = useNative()

  useEffect(() => {
    if (map) {
      // 删除地图上所有的覆盖物
      map.clearMap()
      if (hasLocation) {
        const position = [data?.longitude, data?.latitude] as unknown as AMap.LngLatLike
        const vehicleMarker = new AMap.Marker({
          position,
          icon: new AMap.Icon({
            size: new AMap.Size(34, 44),
            imageSize: new AMap.Size(34, 44),
            image: '/images/<EMAIL>'
          }),
          offset: new AMap.Pixel(-17, -44)
        })
        map.add([vehicleMarker])
        // @ts-ignore
        const geocoder = new AMap.Geocoder({
          city: '全国'
        })

        geocoder.getAddress(position, function (status: string, result: any) {
          if (status === 'complete' && result.regeocode) {
            const address = result.regeocode.formattedAddress
            setAddress(address)
          }
        })
      }
      map.setFitView()
    }
  }, [map, data, hasLocation])

  const handleVehicleView = () => {
    map?.setZoomAndCenter(18, [data?.longitude, data?.latitude] as unknown as AMap.LngLatLike)
  }
  return (
    <Page title="SOS车辆定位" backArrow={<></>} className="bg-white">
      <div className="h-full w-full" ref={elRef}></div>
      <div
        className="absolute right-4 flex-col space-y-3"
        style={{ bottom: safeArea.bottom + 150 }}
      >
        <div className="w-10 rounded-xl bg-white p-2" onClick={handleVehicleView}>
          <Image src="/images/<EMAIL>" alt="lock" width={24} height={24} />
        </div>
      </div>
      <div className="absolute bottom-0 left-0 right-0 z-[160]">
        <div className="rounded-2xl bg-white p-4">
          <div className="flex items-center">
            <div className="my-3 h-[70px] w-[70px] rounded-md bg-[#f6f7f8]">
              <div className="relative w-[90px] -translate-x-[10px]">
                <AspectRatio.Root ratio={482 / 375}>
                  <Image
                    src={data?.img ? toImg(data.img) : '/images/<EMAIL>'}
                    alt="车型图"
                    fill
                    priority
                  />
                </AspectRatio.Root>
              </div>
            </div>
            <div className="ml-4 flex-1 space-y-2 text-xs">
              <div className="line-clamp-2 font-medium leading-normal">{address}</div>
              <div className="text-color-weak">
                {data?.gmtGps && dayjs(data.gmtGps).format('YYYY-MM-DD HH:mm:ss')}
              </div>
              <div className="flex">
                {data?.isDeviceLock === 0 && (
                  <div className="flex-1 text-color-weak">
                    已驻车
                    <span className="text-base text-color-text">{data?.parkingTime || 0}</span>
                    分钟
                  </div>
                )}
                <div className="flex space-x-2">
                  <Image
                    src={`/images/gps-small0${
                      detail?.isOnline === 0 ? 0 : data?.gpsSignal || 0
                    }@2x.png`}
                    alt="gps"
                    width={20}
                    height={20}
                  />
                  <Image
                    src={`/images/gms-small0${
                      detail?.isOnline === 0 ? 0 : data?.gsmSignal || 0
                    }@2x.png`}
                    alt="gms"
                    width={20}
                    height={20}
                  />
                </div>
              </div>
            </div>
          </div>

          <SafeArea position="bottom" />
        </div>
      </div>
    </Page>
  )
}
