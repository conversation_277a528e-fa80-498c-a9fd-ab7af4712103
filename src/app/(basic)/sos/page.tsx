/*
 * @Author: <PERSON><PERSON>
 * @Email: <EMAIL>
 * @Date: 2025-04-01 10:35:24
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-04-02 13:37:45
 */
// @ts-nocheck

'use client'

import { getVehicleDetailSos, getVehicleStatusSos } from '@/server/api/vehicle'
import { useQuery } from '@tanstack/react-query'
import dynamic from 'next/dynamic'
import { useMemo, useTransition } from 'react'
const Location = dynamic(() => import('./components/location'), { ssr: false })

export default function SOSDetail({ searchParams }) {
  const { deviceNo = null } = searchParams
  const [isPending, startTransition] = useTransition()
  const { data } = useQuery({
    queryKey: ['vehicle', 'current', deviceNo],
    queryFn: () => getVehicleStatusSos(deviceNo),
    enabled: !!deviceNo,
    refetchInterval: (query) => {
      // 如果车辆开启，每8秒刷新一次
      if (query.state.data?.isDeviceLock === 1) {
        return 8 * 1000
      }
      return false
    }
  })

  const { data: detail } = useQuery({
    queryKey: ['vehicle', 'detail', deviceNo],
    queryFn: () => getVehicleDetailSos(deviceNo),
    enabled: !!deviceNo
  })

  const hasLocation = useMemo(
    () => data?.longitude && data?.longitude !== '0' && data?.latitude !== '0',
    [data]
  )

  return <Location hasLocation={hasLocation} data={data} detail={detail} />
}
