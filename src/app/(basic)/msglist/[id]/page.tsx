// @ts-nocheck

'use client';

import React, { useEffect, useState, useTransition } from 'react';
import Image from 'next/image';
import { Tabs, Badge, Tag, Toast } from 'antd-mobile';
import Page from '@/components/Page';
import { getNoticeInfo } from '@/server/actions/message';
import { timestampToDate, toContent } from '@/utils';

export default function Message({ params }: { params: { id: string } }) {
  const [isPending, startTransition] = useTransition();
  const [list, setList] = useState({});
  const { id } = params;

  useEffect(() => {
    toInit(id);
  }, [id]);

  const toInit = (id: string) => {
    startTransition(async () => {
      const data: any = await getNoticeInfo(id);
      console.log('[ data ] >', data);
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      setList(data);
    });
  };

  return (
    <Page title="消息详情" style={{ background: '#fff' }}>
      <div className="bg-white px-6 py-8">
        <div className="mb-4 text-center text-2xl">{list.title}</div>
        <div className="mb-8 text-right text-xs text-[#C9CDD3]">
          {timestampToDate(list.pushTime)}
        </div>
        <div
          className=" mb-10  text-xs text-color-weak"
          dangerouslySetInnerHTML={{ __html: toContent(list.content) }}
        />
      </div>
    </Page>
  );
}
