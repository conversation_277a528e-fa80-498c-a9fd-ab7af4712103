'use client';

import React, { useMemo } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Tabs, Badge, Image, SpinLoading } from 'antd-mobile';
import Page from '@/components/Page';
import {timestampToDate, toImg, toPhpImg} from '@/utils';
import { getMessage, getMessageDvc, getNewNotice } from '@/server/api/message';
import { getNews } from '@/server/api/news';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useQuery, useInfiniteQuery } from '@tanstack/react-query';
import { useNative } from '@/lib/native';

export default function MsgList({ searchParams }: { searchParams: { [key: string]: string } }) {
  const router = useRouter();
  const activeKey = searchParams.tab || '5';
  const deviceNo = searchParams.deviceNo || null;

  const {
    data: messageData,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
  } = useInfiniteQuery({
    queryKey: ['message', activeKey],
    queryFn: ({ pageParam }) => {
      if (activeKey === '3') {
        return getMessageDvc({ page: pageParam, pageSize: 20, deviceNo });
      }
      if (activeKey === '5') {
        return getNews({page: pageParam, pageSize: 20})
      }
      return getMessage({ page: pageParam, pageSize: 20, type: activeKey });
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages, lastPageParam) => {
      const totalPages = activeKey === '3' ? lastPage?.page.pages : lastPage?.pages;
      if (totalPages === lastPageParam) {
        return undefined;
      }
      return lastPageParam + 1;
    },
  });

  const flattenData = messageData?.pages
    ? messageData.pages.flatMap((page) => {
      const records = activeKey === '3' ? page.page.records : page.records;
      return [...records];
    })
    : [];

  const msgType: any = {'0': '通知', '1': '活动', '2': '其他', '3': '车辆上报', '5': '资讯'}

  return (
    <Page title={msgType[activeKey]} navBgColor="#fff" style={{backgroundColor: 'rgb(243,244,248)'}}>
      <div className="relative">
        <div className="space-y-3 px-3 py-6">
          <InfiniteScroll
            dataLength={flattenData.length}
            endMessage={
              flattenData.length > 0 ? (
                <div className="pb-8 text-center text-3xs text-color-weak">到底了~</div>
              ) : null
            }
            hasMore={!!hasNextPage}
            loader={
              isFetchingNextPage ? (
                <div className="flex items-center justify-center space-x-1">
                  <SpinLoading style={{ '--size': '24px' }} />
                  <span className="text-3xs text-color-weak">加载中...</span>
                </div>
              ) : null
            }
            next={() => {
              fetchNextPage();
            }}
          >
            {flattenData.map((_: any) => (
              <div key={_.id} className="w-full" style={{ marginBottom: '1.5rem' }}>
                <div className="mb-2 text-center text-2xs text-color-weak">
                  {timestampToDate(_.createTime || _.gmtRelease)}
                </div>
                <div
                  className="w-full  overflow-hidden rounded-2xl bg-white"
                  onClick={() => {
                    if (activeKey !== '3') {
                      // 跳转资讯详情页
                      if(activeKey === '5') {
                        router.push(`/news/${_.id}`)
                      }
                      if (_.isurl === 1) router.push(`/msglist/${_.id}`);
                      if (_.isurl === 0 && _.content)
                        window.jsBridgeHelper?.sendMessage('webBrowse', {
                          url: _.content,
                          navBar: false,
                          title: '消息',
                        });
                    }
                  }}
                >
                  {_.img && (
                    <Image src={toImg(_.img)} alt="img" style={{ width: '100%', height: 'auto' }} />
                  )}
                  {/*资讯类*/}
                  {activeKey === '5' && _.image && (
                    <Image src={toPhpImg(_.image)} alt="img" style={{ width: '100%', height: 'auto' }} />
                  )}
                  <div className="p-4">
                    <div className="line-clamp-1 text-2xl">{_.title || _.deviceName}</div>
                    <div className="mt-2 line-clamp-2 text-xs text-color-weak" />
                    {_.contentName || _.descrip || _.introduce}
                  </div>
                </div>
              </div>
            ))}
          </InfiniteScroll>
          {flattenData.length === 0 && (
            <div className="w-full pt-20 text-center">
              <div className="h-[300px] w-full  bg-[url('/images/empty-content.png')] bg-contain bg-center bg-no-repeat " />
              <span className="text-xl text-color-weak">暂无消息</span>
            </div>
          )}
          <div className="h-[calc(49px+env(safe-area-inset-bottom))]" />
        </div>
      </div>
    </Page>
  );
}
