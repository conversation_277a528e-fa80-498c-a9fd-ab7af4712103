// @ts-nocheck

'use client';

import React, { useEffect, useState, useTransition } from 'react';
import { Toast, ActionSheet } from 'antd-mobile';
import cookies from 'js-cookie';
import Page from '@/components/Page';
import { getNewsInfo, getNewsOpt } from '@/server/actions/news';
import { timestampToDate, toPhpContent } from '@/utils';
import { useNative } from '@/lib/native';

export default function Message({ params }: { params: { id: string } }) {
  const [isPending, startTransition] = useTransition();
  const [list, setList] = useState({});
  const [visible, setVisible] = useState(false);
  const [token, setToken] = useState(false);
  const { id } = params;
  const { hasWechat } = useNative();

  useEffect(() => {
    toInit(id);
    setToken(cookies.get('token'));
  }, [id]);

  const toInit = (id) => {
    startTransition(async () => {
      const data = await getNewsInfo(id);
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      console.log('[ data ] >', data);
      setList(data);
    });
  };

  const toOpt = (id, state) => {
    // 1收藏0取消收藏
    console.log('[ status ] >', state);

    startTransition(async () => {
      const data = await getNewsOpt(id, state ? 0 : 1);
      if (data?.error) {
        Toast.show(data.error);
        return;
      }
      setList({ ...list, status: list.status ? 0 : 1 });
      console.log('[ data ] >', data);
      // toInit(id);
    });
  };
  const toShare = () => {
    setVisible(true);
  };

  // 分享
  const handleAction = (action: any) => {
    console.log('[ action.key ] >', action.key);
    console.log('[ action.key ] >', list);
    console.log('[ action.key ] >', window.location.href);
    setVisible(false);
    window.jsBridgeHelper?.sendMessage('wxShare', {
      type: 1,
      url: window.location.href + '?tabbar=hide',
      platform: action.key,
      title: list.title,
      desc: list.introduce,
    });
  };

  return (
    <Page
      title="资讯详情"
      style={{ background: '#fff' }}
      navBgColor="#fff"
      backArrow={token ? null : <></>}
      right={
        token ? (
          <div className="flex w-full items-center justify-end">
            <div
              className="h-[30px] w-[30px] bg-contain bg-center bg-no-repeat"
              style={{
                backgroundImage: list.status
                  ? "url('/images/<EMAIL>')"
                  : "url('/images/<EMAIL>')",
              }}
              onClick={() => toOpt(id, list.status)}
            />
            {hasWechat && (
              <div
                className="ml-2  h-[26px] w-[26px]  bg-[url('/images/<EMAIL>')]  bg-contain bg-center bg-no-repeat"
                onClick={toShare}
              />
            )}
          </div>
        ) : null
      }
    >
      <div className="bg-white px-6 py-8">
        <div className="mb-4 text-center  text-2xl">{list.title}</div>
        <div className="mb-8 text-right text-xs text-[#C9CDD3]">
          {timestampToDate(list.gmtRelease)}
        </div>
        <div
          className=" mb-10  text-xs text-color-weak"
          dangerouslySetInnerHTML={{ __html: toPhpContent(list.content) }}
        />
      </div>
      <ActionSheet
        cancelText="取消"
        visible={visible}
        actions={[
          { text: '分享给好友', key: 'session' },
          { text: '分享到朋友圈', key: 'timeline' },
        ]}
        onClose={() => setVisible(false)}
        onAction={handleAction}
      />
    </Page>
  );
}
