'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { CloseOutline, AddCircleOutline } from 'antd-mobile-icons';
import { useQuery, useMutation } from '@tanstack/react-query';
import { getVehicles, switchVehicle } from '@/server/api/home';
import { useAfterSwitchVehicle } from '@/hooks/useAfterSwitchVehicle';

export default function SwitchPage() {
  const router = useRouter();
  const afterSwitchVehicle = useAfterSwitchVehicle();

  const { data = [] } = useQuery({ queryKey: ['vehicles'], queryFn: getVehicles });
  const { mutate } = useMutation({
    mutationFn: switchVehicle,
    onSuccess: () => {
      afterSwitchVehicle();
      router.push('/');
    },
  });

  return (
    <Page
      title="切换车辆"
      backArrow={<CloseOutline fontSize={24} color="#333" />}
      className="bg-white"
    >
      <div className="space-y-6 bg-white px-3 py-6 ">
        {data.map((item) => {
          return (
            <div
              className="relative"
              key={item.id}
              onClick={() => {
                // 当前车辆直接跳转
                if (item.isCurrent === 1) {
                  router.push('/');
                } else {
                  mutate(item.deviceNo);
                }
              }}
            >
              <div className="h-32 rounded-2xl bg-[url('/images/C-2/<EMAIL>')] bg-cover bg-center bg-no-repeat">
                <div className="flex flex-col px-4 pt-6">
                  <div className="space-y-1">{item.nickName}</div>
                  <div className="text-xs text-color-weak">{item.carModel}</div>
                  <div className="mt-1 text-xs text-color-weak">车架号</div>
                  <div className="text-xs text-color-weak"> {item.deviceNo}</div>
                </div>
              </div>
              <Image
                alt="车型图"
                src="/images/C-2/car-img1.png"
                width={150}
                height={150}
                className="absolute bottom-0 right-0"
              />
            </div>
          );
        })}
        <Button type="primary" block href="/bind">
          <span className="flex items-center justify-center">
            <AddCircleOutline fontSize={24} color="#fff" />
            <span className="ml-3">添加车辆</span>
          </span>
        </Button>
      </div>
    </Page>
  );
}
