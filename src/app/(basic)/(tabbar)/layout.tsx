'use client';

import { useMemo } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import Image from 'next/image';
import { TabBar, Badge } from 'antd-mobile';
import { useNative } from '@/lib/native';
import {getDeviceNewNotice, getSimpleNewNotice} from '@/server/api/message';
import { useQuery } from '@tanstack/react-query';

export default function Layout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const router = useRouter();
  const { safeArea } = useNative();

  const {data: simpleNewNotice} = useQuery({
      queryKey: ['simpleNewNotice'],
      queryFn: getSimpleNewNotice,
  });

  const {data: deviceNewNotice} = useQuery({
      queryKey: ['deviceNewNotice'],
      queryFn: getDeviceNewNotice,
  });

  const dot = useMemo(() => {
    if (!simpleNewNotice || !deviceNewNotice) return false;
    return (simpleNewNotice.dot.length > 0) || (deviceNewNotice.dot.length > 0);
  }, [simpleNewNotice, deviceNewNotice]);

  const tabs = useMemo(
    () => [
      {
        key: '/',
        title: (active: boolean) => (
          <span className={active ? 'font-medium text-color-text' : ' text-color-weak'}>车况</span>
        ),
        icon: (active: boolean) => (
          <Image
            src={`/images/tab-vehicle-v2-${active ? 's' : 'n'}@2x.png`}
            alt="home"
            width={24}
            height={24}
            data-track="车况"
          />
        ),
      },
      {
          key: '/service',
          title: (active: boolean) => (
              <span className={active ? 'font-medium text-color-text' : ' text-color-weak'}>服务</span>
          ),
          icon: (active: boolean) => (
              <Image
                  src={`/images/tab-service-v2-${active ? 's' : 'n'}@2x.png`}
                  alt="service"
                  width={24}
                  height={24}
                  data-track="服务"
              />
          ),
      },
      {
        key: '/message',
        title: (active: boolean) => (
          <span className={active ? 'font-medium text-color-text' : ' text-color-weak'}>消息</span>
        ),
        icon: (active: boolean) => (
          <Image
            src={`/images/tab-news-v2-${active ? 's' : 'n'}@2x.png`}
            alt="message"
            width={24}
            height={24}
            data-track="消息"
          />
        ),
        badge: Badge.dot,
      },
      // {
      //   key: '/news',
      //   title: (active: boolean) => (
      //     <span className={active ? 'font-medium text-color-text' : ' text-color-weak'}>资讯</span>
      //   ),
      //   icon: (active: boolean) => (
      //     <Image
      //       src={`/images/tab-consulting-${active ? 's' : 'n'}@2x.png`}
      //       alt="news"
      //       width={24}
      //       height={24}
      //       data-track="资讯"
      //     />
      //   ),
      // },
      {
        key: '/me',
        title: (active: boolean) => (
          <span className={active ? 'font-medium text-color-text' : ' text-color-weak'}>我的</span>
        ),
        icon: (active: boolean) => (
          <Image
            src={`/images/tab-my-v2-${active ? 's' : 'n'}@2x.png`}
            alt="me"
            width={24}
            height={24}
            data-track="我的"
          />
        ),
      },
    ],
    []
  );

  const onChange = (key: string) => {
    router.push(key);
  };

  return (
    <div className="relative h-screen">
      <div className="h-screen">{children}</div>
      <div className="fixed bottom-0 left-0 right-0 bg-white">
        <TabBar activeKey={pathname} onChange={onChange}>
          {tabs.map((item) => (
            <TabBar.Item
              key={item.key}
              title={item.title}
              icon={item.icon}
              badge={dot && item.badge}
            />
          ))}
        </TabBar>
        <div style={{ height: safeArea.bottom }} />
      </div>
    </div>
  );
}
