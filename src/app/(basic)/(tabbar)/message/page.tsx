'use client';

import Page from '@/components/Page';
import Image from "next/image";
import React, {useEffect, useMemo, useState} from "react";
import {Badge, Dialog, Space, Toast} from "antd-mobile";
import Link from "next/link";
import {useQuery} from "@tanstack/react-query";
import {getDeviceNewNotice, getSimpleNewNotice, readAllNotice} from "@/server/api/message";
import {useRouter} from "next/navigation";

export default function Message() {

  const router = useRouter();

  const {data: simpleNewNotice, refetch: simpleNoticeFetch} = useQuery({
    queryKey: ['simpleNewNotice'],
    queryFn: getSimpleNewNotice,
  });

  const {data: deviceNewNotice, refetch: deviceNoticeFetch} = useQuery({
    queryKey: ['deviceNewNotice'],
    queryFn: getDeviceNewNotice,
  });

  const tabItems = useMemo(() => {
    return [
      {
        key: '5',
        name: '资讯',
        img: '/images/<EMAIL>',
        dot: simpleNewNotice?.dot.some(ele => ele === 'news'),
        notice: simpleNewNotice?.news
      },
      {
        key: '0',
        name: '通知',
        img: '/images/<EMAIL>',
        dot: simpleNewNotice?.dot.some(it => it === 'notify'),
        notice: simpleNewNotice?.notify
      },
      {
        key: '1',
        name: '活动',
        img: '/images/<EMAIL>',
        dot: simpleNewNotice?.dot.some(it => it === 'activity'),
        notice: simpleNewNotice?.activity
      },
      {
        key: '2',
        name: '其他',
        img: '/images/<EMAIL>',
        dot: simpleNewNotice?.dot.some(it => it === 'other'),
        notice: simpleNewNotice?.other
      },
    ];
  }, [simpleNewNotice]);

  const formatTime = (dateInput: string) => {
    if (!dateInput) {
      return ''
    }
    const date = new Date(dateInput);
    // 获取年份的后两位
    const year = String(date.getFullYear()).slice(2);

    // 获取月份，不足两位时补零
    const month = String(date.getMonth() + 1).padStart(2, '0');

    // 获取日期，不足两位时补零
    const day = String(date.getDate()).padStart(2, '0');

    // 获取小时，不足两位时补零
    const hours = String(date.getHours()).padStart(2, '0');

    // 获取分钟，不足两位时补零
    const minutes = String(date.getMinutes()).padStart(2, '0');

    // 组合成需要的格式
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }

  const toMsglist = (_:any) => {
    if(_.key) {
        router.push(`/msglist?tab=${_.key}`);
    } else {
        router.push(`/msglist?tab=3&deviceNo=${_.deviceNo}`);
    }

  }

  // 已读
  const allRead = () => {
    const hasDot = (deviceNewNotice && deviceNewNotice?.dot.length > 0)
        || (simpleNewNotice?.dot && simpleNewNotice?.dot.length > 0)
    if(!hasDot) {
      Toast.show('暂无未读消息');
      return
    }

    Dialog.confirm({
      content:
        '确认已读所有信息',
      onAction: async (action) => {
        if (action.key === 'confirm') {
          await readAllNotice({
            simpleDots: simpleNewNotice?.dot || [],
            deviceDots: deviceNewNotice?.dot || []
          })
          Toast.show('全部已读');
          simpleNoticeFetch()
          deviceNoticeFetch()
        }
      },
    });

  }

  return (
    <Page showTitle={false} style={{backgroundColor: 'rgb(243,244,248)'}}
          backArrow={
            <div className="flex w-[calc(100vw-200px)] items-center text-color-text pt-2">
              <div className="place-items-center">
                <Image
                  width={30}
                  height={30}
                  priority
                  alt="消息中心"
                  src={'/images/<EMAIL>'}/>
              </div>
              <div className="ml-2 text-xl font-bold">消息中心</div>
            </div>
          } title="消息中心"
          right={
            <Space>
              <div className="pt-4 flex flex-row min-w-6 justify-end">
                <Image onClick={allRead} className="mr-2" src="/images/message/message-clean.png" alt="arrow" width={24} height={24}/>

                <Link
                  href="/me/setting/push"
                  data-track="消息管理"
                >
                  <Image src="/images/message/message-setting.png" alt="arrow" width={24} height={24}/>
                </Link>
              </div>
            </Space>
          }
    >
      <div className="h-full space-y-3 px-3 pt-4 relative">
        {tabItems.map((_, index) => (
          <div key={index} onClick={() => toMsglist(_)} className="grid grid-cols-10 bg-white rounded-lg space-y-3 px-3 py-6">
            <div className="col-span-2 h-full">
              <div className="w-full flex justify-between">
                <div className="w-12 h-12 rounded-full flex items-center justify-center relative"
                     style={{backgroundColor: 'rgb(243,244,247)'}}>
                  <Image
                    src={_.img}
                    alt="arrow-right"
                    width={25}
                    height={25}
                  />

                  {
                    _.dot && (
                      <Badge
                        color="#E33939"
                        content={Badge.dot}
                        style={{position: 'absolute', right: '0', top: 0}}
                      />
                    )
                  }

                </div>
              </div>
            </div>

            <div className="col-span-8 h-full flex flex-col" style={{marginTop: 0}}>
              <div>
                <div className="w-full flex items-start justify-between" style={{marginTop: 0}}>
                  <span>{_.name}</span>

                  <div className="flex items-center">
                    <span
                      className="text-color-weak text-2xs">{formatTime(_.notice?.createTime || _.notice?.gmtRelease || null)}</span>
                    <Image
                      src="/images/<EMAIL>"
                      alt="arrow-right"
                      width={20}
                      height={20}
                      className="ml-1.5"
                    />
                  </div>
                </div>
              </div>
              <div className="pr-4">
                <span
                  className="mt-2 text-color-weak line-clamp-1 overflow-hidden text-xs">{_.notice?.descrip || _.notice?.introduce || '暂无消息'}
                </span>
              </div>
            </div>
          </div>
        ))}

        {/*车辆信息*/}
        <div className="text-color-weak text-xs mb-2 pl-2">车辆消息</div>
        {
          !deviceNewNotice?.bindDevice.length &&
          <div className="w-full text-center">
            <div
              className="h-[200px] w-full  bg-[url('/images/empty-content.png')] bg-contain bg-center bg-no-repeat "/>
            <span className="text-xl text-color-weak text-xs">暂无车辆</span>
          </div>
        }


        {deviceNewNotice?.bindDevice?.map((_, index) => (
          <div key={index} onClick={() => toMsglist(_)} className="grid grid-cols-10 bg-white rounded-lg space-y-3 px-3 py-6">
            <div className="col-span-2 h-full">
              <div className="w-full flex justify-between">
                <div
                  className="w-12 h-12 rounded-full flex items-center justify-center relative"
                  style={{backgroundColor: 'rgb(243,244,247)'}}>
                  <Image
                    src="/images/<EMAIL>"
                    alt="arrow-right"
                    width={25}
                    height={25}
                  />

                    {
                        deviceNewNotice?.dot.some(it => it === _.deviceNo) && (
                            <Badge
                                color="#E33939"
                                content={Badge.dot}
                                style={{position: 'absolute', right: '0', top: 0}}
                            />
                        )
                    }

                </div>
              </div>
            </div>

            <div className="col-span-8 h-full flex flex-col" style={{marginTop: 0}}>
              <div>
                <div className="w-full flex items-start justify-between" style={{marginTop: 0}}>
                  <span className=" line-clamp-1 overflow-hidden w-28">{_?.nickName}</span>

                  <div className="flex items-center">
                    <span
                      className="text-color-weak text-2xs">{formatTime(deviceNewNotice[_.deviceNo]?.createTime || '')}</span>
                    <Image
                      src="/images/<EMAIL>"
                      alt="arrow-right"
                      width={20}
                      height={20}
                      className="ml-1.5"
                    />
                  </div>
                </div>
              </div>
              <div className="pr-4">
                <span
                  className="mt-2 text-color-weak line-clamp-1 overflow-hidden text-xs">{deviceNewNotice[_.deviceNo]?.contentName || '暂无消息'}
                </span>
              </div>
            </div>
          </div>
        ))}

        {/*safearea*/}
        <div className="h-20"/>
      </div>
    </Page>
  );
}
