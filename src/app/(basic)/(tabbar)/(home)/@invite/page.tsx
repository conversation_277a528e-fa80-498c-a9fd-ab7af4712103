'use client';

import Image from 'next/image';
import SafeArea from '@/components/SafeArea';
import Button from '@/components/Button';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import { replyInvite } from '@/server/api/home';
import { InviteInfo } from '@/server/api/types';
import { toImg } from '@/utils';

export default function InvitePage() {
  const queryClient = useQueryClient();
  const data = queryClient.getQueryData<InviteInfo>(['info']);
  const { mutate } = useMutation({
    mutationFn: replyInvite,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['info'] });
    },
    onError: (error) => {
      // 6135代表用户绑定的车辆超过上限，显示车况页面
      if (error.cause === '6135') {
        queryClient.invalidateQueries({ queryKey: ['info'] });
      }
    },
  });

  const handleInvite = (type: number) => {
    mutate({ type, deviceNo: data?.list[0].deviceNo as string });
  };

  return (
    <div className="relative h-full">
      <SafeArea position="top" />
      <div className="px-4 pt-14">
        <div className="text-4xl font-medium">{data?.list[0].nickName}</div>
        <div className="mt-3">
          邀请您共同使用车辆<span className="text-primary">{data?.list[0].modelName}</span>
        </div>
        <div className="mb-6 mt-8 flex items-center justify-center rounded-2xl bg-white py-3">
          <Image
            src={data?.list[0].modelImg ? toImg(data?.list[0].modelImg) : '/images/<EMAIL>'}
            alt="car"
            priority
            width={382}
            height={273}
            className="rounded-2xl"
          />
        </div>
        <Button type="primary" block onClick={() => handleInvite(1)}>
          立即接受
        </Button>
        <Button block className="mt-3" onClick={() => handleInvite(2)}>
          拒绝
        </Button>
      </div>
    </div>
  );
}
