import { dehydrate, HydrationBoundary, QueryClient } from '@tanstack/react-query'
import Layout from './components/layout'
import { getInfo } from '@/server/actions/home'

export default async function HomeLayout(props: {
  children: React.ReactNode
  bind: React.ReactNode
  invite: React.ReactNode
}) {
  const queryClient = new QueryClient()

  await queryClient.prefetchQuery({
    queryKey: ['info'],
    queryFn: getInfo
  })

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <Layout {...props} />
    </HydrationBoundary>
  )
}
