import { animated, useSpring } from '@react-spring/web'

export default function Btn4() {
  const [gStyle, api] = useSpring(() => ({
    from: { transform: 'rotate(0deg) scale(1)', transformOrigin: 'center center' }
  }))

  const startAnimation = () => {
    api.start({
      to: async (next) => {
        await next({ transform: 'rotate(45deg) scale(0.2)' })
        await next({ transform: 'rotate(0deg) scale(1)' })
      },
      config: { duration: 200 }
    })
  }

  return (
    <svg
      width="60"
      height="60"
      viewBox="0 0 120 120"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={startAnimation}
    >
      <circle cx="60" cy="60" r="60" fill="#EFEFF1" />
      <animated.g clipPath="url(#clip0_125_1457)" style={gStyle}>
        <path
          d="M70.2 58V51C70.2302 48.3254 69.2287 45.742 67.4037 43.7866C65.5786 41.8312 63.0703 40.6541 60.4 40.5C59.0404 40.4461 57.6839 40.6675 56.412 41.1509C55.14 41.6343 53.9789 42.3697 52.9982 43.313C52.0176 44.2563 51.2376 45.388 50.7052 46.6402C50.1728 47.8924 49.8989 49.2393 49.9 50.6V58C49.9016 58.8898 49.6834 59.7661 49.2647 60.5512C48.846 61.3363 48.2398 62.0057 47.5 62.5C46.2637 63.3677 45.2547 64.5205 44.5584 65.8609C43.8621 67.2012 43.4991 68.6896 43.5 70.2C43.4996 71.0847 43.8441 71.9347 44.4602 72.5695C45.0764 73.2043 45.9157 73.574 46.8 73.6H73.2C74.1018 73.6 74.9665 73.2418 75.6042 72.6042C76.2418 71.9665 76.6 71.1017 76.6 70.2C76.6009 68.6896 76.2379 67.2012 75.5416 65.8609C74.8453 64.5205 73.8363 63.3677 72.6 62.5C71.8602 62.0057 71.254 61.3363 70.8353 60.5512C70.4166 59.7661 70.1984 58.8898 70.2 58Z"
          stroke="#30333F"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M76.5 52.4C76.5828 51.9045 76.6163 51.4021 76.6 50.9C76.6122 49.0945 76.2134 47.3098 75.4337 45.6812C74.6541 44.0526 73.514 42.6228 72.1 41.5"
          stroke="#908F94"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M43.6 52.4C43.5172 51.9045 43.4837 51.4021 43.5 50.9C43.4902 49.1049 43.8799 47.3301 44.6409 45.7043C45.402 44.0785 46.5152 42.6423 47.9 41.5"
          stroke="#908F94"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M64.4 74.1C64.4 75.2669 63.9364 76.3861 63.1113 77.2113C62.2861 78.0364 61.167 78.5 60 78.5C59.4268 78.5002 58.8594 78.3857 58.331 78.1634C57.8027 77.9411 57.3242 77.6154 56.9235 77.2054C56.5229 76.7955 56.2083 76.3096 55.9982 75.7763C55.7881 75.243 55.6867 74.673 55.7 74.1"
          stroke="#30333F"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </animated.g>
      <defs>
        <clipPath id="clip0_125_1457">
          <rect width="36.1" height="41" fill="white" transform="translate(42 39)" />
        </clipPath>
      </defs>
    </svg>
  )
}
