import { animated, useSpring } from '@react-spring/web'

export default function Btn3({ toggle, loading }: { toggle: boolean; loading: boolean }) {
  const [gStyle, api] = useSpring(() => ({
    from: { transform: 'rotate(0deg) scale(1)', transformOrigin: 'center center' }
  }))

  const style = {
    fill: toggle ? '#585C78' : '#EFEFF1',
    stroke1: toggle ? '#fff' : '#30333F',
    stroke2: toggle ? '#fff' : '#908F94',
    config: { duration: 100 }
  }

  const startAnimation = () => {
    api.start({
      to: async (next) => {
        await next({ transform: 'rotate(45deg) scale(0.2)' })
        await next({ transform: 'rotate(0deg) scale(1)' })
      },
      config: { duration: 200 }
    })
  }

  return (
    <svg
      width="60"
      height="60"
      viewBox="0 0 120 120"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={startAnimation}
    >
      <animated.circle cx="60" cy="60" r="60" fill={style.fill} />
      <animated.g clipPath="url(#clip0_125_1456)" style={gStyle}>
        <path
          d="M40.5 72.5C40.5 74.0913 41.1321 75.6174 42.2574 76.7426C43.3826 77.8678 44.9087 78.5 46.5 78.5H72.5C74.0754 78.45 75.5726 77.8017 76.6872 76.6872C77.8017 75.5726 78.4499 74.0754 78.5 72.5C78.5 70.9087 77.8678 69.3826 76.7426 68.2574C75.6174 67.1322 74.0913 66.5 72.5 66.5H52.5"
          stroke={style.stroke1}
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M52.5 46.5C52.5 43.1863 49.8137 40.5 46.5 40.5C43.1863 40.5 40.5 43.1863 40.5 46.5V72.5C40.5 75.8137 43.1863 78.5 46.5 78.5C49.8137 78.5 52.5 75.8137 52.5 72.5V46.5Z"
          stroke={style.stroke1}
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M66.9709 48.3416C67.7118 47.9712 68.0122 47.0701 67.6417 46.3292C67.2712 45.5882 66.3702 45.2879 65.6292 45.6584L60.0292 48.4584C59.2882 48.8288 58.9879 49.7299 59.3584 50.4708L62.1584 56.0708C62.5289 56.8118 63.4299 57.1121 64.1709 56.7416C64.9118 56.3712 65.2122 55.4701 64.8417 54.7292L63.7798 52.6055C64.6167 52.6385 65.442 52.8194 66.2171 53.1405C67.0785 53.4973 67.8612 54.0202 68.5205 54.6795C69.1798 55.3388 69.7028 56.1215 70.0596 56.9829C70.4164 57.8444 70.6 58.7676 70.6 59.7C70.6 60.5284 71.2716 61.2 72.1 61.2C72.9285 61.2 73.6 60.5284 73.6 59.7C73.6 58.3736 73.3388 57.0603 72.8312 55.8349C72.3236 54.6095 71.5797 53.4961 70.6418 52.5582C69.704 51.6203 68.5905 50.8764 67.3652 50.3688C66.4109 49.9735 65.4033 49.7277 64.3777 49.6382L66.9709 48.3416Z"
          fill={style.stroke2}
        />
      </animated.g>
      <defs>
        <clipPath id="clip0_125_1456">
          <rect width="41" height="41" fill="white" transform="translate(39 39)" />
        </clipPath>
      </defs>
    </svg>
  )
}
