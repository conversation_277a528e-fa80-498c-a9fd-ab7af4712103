import { useState } from 'react'
import { animated, useSpring } from '@react-spring/web'

export default function Btn2({ toggle, loading }: { toggle: boolean; loading: boolean }) {
  const [length, setLength] = useState(0)

  const style = useSpring({
    fill: toggle ? '#585C78' : '#EFEFF1',
    stroke1: toggle ? 'white' : '#30333F',
    stroke2: toggle ? 'white' : '#908F94',
    config: { duration: 100 }
  })

  const { strokeDashoffset } = useSpring({
    from: { strokeDashoffset: loading ? length : 0 },
    to: { strokeDashoffset: 0 },
    config: { duration: 1500 },
    loop: true // 循环动画
  })

  return (
    <svg
      width="60"
      height="60"
      viewBox="0 0 120 120"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <animated.circle cx="60" cy="60" r="60" fill={style.fill} />
      <g clipPath="url(#clip0_125_1454)">
        <animated.path
          ref={(ref) => {
            if (ref) {
              setLength(ref.getTotalLength())
            }
          }}
          d="M76.5 48.8V55.2C76.4797 60.3788 74.8254 65.4191 71.7729 69.6028C68.7205 73.7865 64.4255 76.9001 59.5 78.5C54.5662 76.9135 50.2627 73.8033 47.208 69.6167C44.1534 65.43 42.505 60.3825 42.5 55.2V48.8C42.5 48.8 52 51.1 59.5 40.5C59.5 40.5 65.6 49.1 76.5 48.8Z"
          stroke={style.stroke1}
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeDasharray={length}
          strokeDashoffset={strokeDashoffset}
        />
        <animated.path
          d="M54.8999 61L58.6999 64.2L65.8999 55.7"
          stroke={style.stroke2}
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_125_1454">
          <rect width="37" height="41" fill="white" transform="translate(41 39)" />
        </clipPath>
      </defs>
    </svg>
  )
}
