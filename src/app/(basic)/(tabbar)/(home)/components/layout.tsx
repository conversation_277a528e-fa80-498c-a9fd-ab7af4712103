'use client'

import { useQuery } from '@tanstack/react-query'
import { getInfo } from '@/server/api/home'

export default function HomeLayout(props: {
  children: React.ReactNode
  bind: React.ReactNode
  invite: React.ReactNode
}) {
  const { data = { deviceNo: '', list: [] } } = useQuery({ queryKey: ['info'], queryFn: getInfo })

  // Display the invite page if there are invitation codes
  if (data.list.length > 0) {
    return props.invite
  }

  // Display the bind page if no vehicle is bound
  if (!data.deviceNo) {
    return props.bind
  }

  // Display the main content if all conditions are met
  return props.children
}
