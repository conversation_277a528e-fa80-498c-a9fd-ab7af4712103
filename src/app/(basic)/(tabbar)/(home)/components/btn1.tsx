import { useState } from 'react'
import { animated, useSpring, config } from '@react-spring/web'

export default function Btn1({ toggle, loading }: { toggle: boolean; loading: boolean }) {
  const [length, setLength] = useState(0)

  const style = useSpring({
    fill: toggle ? '#585C78' : '#EFEFF1',
    stroke1: toggle ? 'white' : '#30333F',
    stroke2: toggle ? 'white' : '#908F94',
    config: { duration: 100 }
  })

  const { strokeDashoffset } = useSpring({
    from: { strokeDashoffset: loading ? -length : 0 }, // 周长
    to: { strokeDashoffset: 0 },
    config: { duration: 1200 },
    loop: true // 循环动画
  })

  return (
    <svg
      width="60"
      height="60"
      viewBox="0 0 120 120"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <animated.circle cx="60" cy="60" r="60" fill={style.fill} />
      <g clipPath="url(#clip0_125_1322)">
        <animated.path
          ref={(ref) => {
            if (ref) {
              setLength(ref.getTotalLength())
            }
          }}
          d="M46.6001 46.3C43.7481 48.8934 41.7476 52.2891 40.8615 56.0408C39.9755 59.7924 40.2453 63.7243 41.6356 67.3197C43.0259 70.9151 45.4715 74.0057 48.6511 76.1852C51.8306 78.3647 55.5953 79.5311 59.4501 79.5311C63.3049 79.5311 67.0696 78.3647 70.2491 76.1852C73.4287 74.0057 75.8743 70.9151 77.2646 67.3197C78.6549 63.7243 78.9247 59.7924 78.0387 56.0408C77.1526 52.2891 75.1521 48.8934 72.3001 46.3"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
          stroke={style.stroke1}
          strokeDasharray={length}
          strokeDashoffset={strokeDashoffset}
        />
        <animated.path
          d="M59.5 51.2V41.5"
          strokeOpacity="0.5"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
          stroke={style.stroke2}
        />
      </g>
      <defs>
        <clipPath id="clip0_125_1322">
          <rect width="41" height="40.9" fill="white" transform="translate(39 40)" />
        </clipPath>
      </defs>
    </svg>
  )
}
