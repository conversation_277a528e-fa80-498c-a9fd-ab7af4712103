'use client';

import { useRouter } from 'next/navigation';
import Page from '@/components/Page';
import Image from "next/image";
import React, {useEffect, useState} from "react";
import dayjs from "dayjs";
import { useQuery } from '@tanstack/react-query';
import {getExpireTime} from "@/server/api/service";
import * as AspectRatio from "@radix-ui/react-aspect-ratio";
import {isNil, isWithinTime, toImg} from "@/utils";
import {getInfo} from "@/server/api/home";
import {Action} from "antd-mobile/es/components/action-sheet";
import {ActionSheet} from "antd-mobile";
import {getStore, getStoreDetail} from "@/server/api/store";

export default function Service() {
  const router = useRouter();

  const [visible, setVisible] = useState(false);
  const [currentItem, setCurrentItem] = useState<any>();
  const [userPosition, setUserPosition] = useState<number[]>([]);

  useEffect(() => {
    window.jsBridgeHelper?.sendMessage('queryLocation').then((res: any) => {
      if (res.code === '200') {
        setUserPosition([res.data.longitude, res.data.latitude]);
      }
    });
  })

  // 门店
  const { data: storeList } = useQuery({
    queryKey: ['store-list'],
    queryFn: () => getStore(userPosition[0], userPosition[1]),
    enabled: userPosition.length > 0,
  });
  // 门店详情
  const store = storeList && storeList.length ? storeList[0] : null;
  const { data:storeDetail } =  useQuery({
    queryKey: ['store'],
    queryFn: () => getStoreDetail(store.id),
    enabled: !isNil(store),
  });


  // 获取智能服务时间
  let { data = { deviceNo: '', list: [] }, isFetching } = useQuery({
    queryKey: ['info'],
    queryFn: getInfo
  })
  const { data: vehicleInfo } = useQuery({
    queryKey: ['vehicle-info', data.deviceNo],
    queryFn: () => getExpireTime(data.deviceNo),
    enabled: !isNil(data.deviceNo)
  });

  const helpList = ([
    {
      name: '新手入门',
      icon: '/images/service/service-study.png',
      to: `${process.env.NEXT_PUBLIC_PHP_URL}/home/<USER>/index`,
    },
    {
      name: '自检手册',
      icon: '/images/service/service-self-insp.png',
      to: `${process.env.NEXT_PUBLIC_PHP_URL}/home/<USER>/index`,
    },
    {
      name: '关于立马',
      icon: '/images/service/service-about.png',
      to: 'me/about/lima',
    },
    {
      name: '分割线',
      icon: '',
      to: '',
      divide: true
    },
    {
      name: '建议与反馈',
      icon: '/images/service/service-suggest.png',
      to: '/me/feedback',
    },
  ]);

  const commonProblemList = [
    {key: 256, name: '电量显示不准'},
    {key: 258, name: '无法预估里程'},
    {key: 246, name: '车辆解锁/开锁失败'},
  ]

  // 打开链接
  const openUri = (to:string) => {
    // 是否为https外链
    if (to?.startsWith('https')) {
      window.jsBridgeHelper?.sendMessage('webBrowse', {
        navBar: true,
        url: to,
      });
      return;
    }
    if (to) router.push(to);
  }

  // 拨号
  const handlePhoneCall = (e: any, phone: string) => {
    e.stopPropagation();
    window.jsBridgeHelper?.sendMessage('phoneCall', phone);
  };
  // 导航
  const handleAction = (action: Action) => {
    setVisible(false);
    window.jsBridgeHelper?.sendMessage(action.key, {
      lat: currentItem.storeGpsLat,
      lon: currentItem.storeGpsLng,
    });
  };

  return (
    <Page showTitle={false}
          backArrow={
            <div className="flex w-[calc(100vw-200px)] items-center text-color-text pt-2">
              <div className="place-items-center">
                <Image
                    width={30}
                    height={30}
                    priority
                    alt="服务与帮助"
                    src={'/images/<EMAIL>'} />
              </div>
              <div className="ml-2 text-xl font-bold">服务与帮助</div>
            </div>
          } title="服务">
      <div className="space-y-3 px-3 pt-4 pb-20 relative">
        {/*门店*/}
        <div className="flex flex-col bg-white rounded-lg space-y-3 px-4 py-3">
          <div className="flex items-center justify-between">
            <span>附近门店</span>
            <span className="text-xs text-gray-500" onClick={() => openUri(`/vehicle/store/store`)}>{`更多 >`}</span>
          </div>

          <div className="space-y-3 px-3">
            <div className="flex items-center justify-between">
              <span className="text-xs font-bold">{store?.storeName || '-'}</span>
              <span className="text-xs">{`${store?.distance || '?'} km`}</span>
            </div>
            <div className="flex flex-col">
              <span className="text-3xs text-gray-500 leading-relaxed">暂无评分&emsp;0单</span>

              <div className="px-3 py-3 rounded-lg pb-4 grid grid-cols-5" style={{
                  background: `url('/images/background-map.png') no-repeat`,
                  backgroundSize: 'cover'}}
                  onClick={() => { if (store) {router.push(`/vehicle/store/store/${store?.id}`)}}}>
                <div className="col-span-4">
                  <p className="text-xs">
                      {
                        isWithinTime(store?.businessTime)
                              ? <span className="font-bold text-orange-500">营业中</span>
                              : <span className="font-bold text-gray-500">暂未营业</span>
                      }
                    &emsp;{store?.businessTime}
                  </p>
                  <p className="text-3xs text-gray-500">
                      {store?.address}
                  </p>
                </div>

                <div className="col-span-1 flex items-center">
                    {store?.storeGpsLat && <Image
                        alt="导航"
                        src="/images/<EMAIL>"
                        width={20}
                        height={20}
                        onClick={(e) => {
                            e.stopPropagation();
                            setVisible(true);
                            setCurrentItem(store);
                        }}
                    />}

                    {store?.storeTelephone && <Image
                        className="ml-4"
                        alt="电话"
                        src="/images/<EMAIL>"
                        width={20}
                        height={20}
                        onClick={(e) => handlePhoneCall(e, store?.storeTelephone)}
                    />}

                </div>
              </div>

              <span className="text-3xs text-gray-500 leading-relaxed mt-2">门店性质：{storeDetail?.store?.storeNature || '-'}</span>
              <span className="text-3xs text-gray-500 leading-relaxed mt-2">服务类型：{storeDetail?.store?.serverItemName || '-'}</span>
            </div>
          </div>
        </div>

        {/*新手入门*/}
        <div className="flex px-4 py-3 justify-between text-center text-3xs">
          {helpList.map((_, index) => (
              _.divide ? <div key={index} className="border-l-2 border-gray-300"/> :
              <div key={index} className="flex flex-col items-center" onClick={() => openUri(_.to)}>
                <Image
                  width={25}
                  height={25}
                  priority
                  alt={_.name}
                  src={_.icon}
                />
                <p className="mt-1">{_.name}</p>
              </div>
          ))}
        </div>

        {/*常见问题*/}
        <div className="flex flex-col bg-white rounded-lg space-y-3 px-4 py-3">
          <div className="flex items-center justify-between">
            <span>常见问题</span>
            <span className="text-xs text-gray-500"
                  onClick={() => openUri(`${process.env.NEXT_PUBLIC_PHP_URL}/home/<USER>/index`)}>
              {`更多 >`}
            </span>
          </div>

          <div className="space-y-3 px-3 py-3 rounded-lg bg-gray-100 text-xs">
            {commonProblemList.map((_, index) => (
              <div key={index} className="grid grid-cols-10 mb-4">
                <p className="col-span-9">{_.name}</p>
                <Image
                  src="/images/<EMAIL>"
                  alt="arrow-right"
                  width={20}
                  height={20}
                  className="ml-1.5 col-span-1"
                  onClick={() => openUri(`${process.env.NEXT_PUBLIC_PHP_URL}/home/<USER>/index/${_.key}`)}
                />
              </div>
            ))}
          </div>
        </div>

        {/*智能服务*/}
        <div className="relative h-32 w-full" onClick={() => {
          if (data.deviceNo) {
            openUri(`/vehicle/${data.deviceNo}/service/purchase`)
          }
        }}>
          <Image src="/images/<EMAIL>" alt="服务" fill />
          <div className="absolute left-0 top-0 flex h-32 w-full items-center px-4">
            <div className="h-[70px] w-[70px] rounded-md bg-gradient-to-b from-[rgba(246,247,251,0.5)] to-[rgba(246,247,251,0)]">
              <div className="relative w-[90px] -translate-x-[10px]">
                <AspectRatio.Root ratio={482 / 375}>
                  <Image
                    src={
                      vehicleInfo?.modelImg ? toImg(vehicleInfo.modelImg) : '/images/<EMAIL>'
                    }
                    alt="车型图"
                    fill
                    priority
                  />
                </AspectRatio.Root>
              </div>
            </div>
            <div className="ml-5 flex flex-col text-xs text-white">
              <div>智能服务剩余天数</div>
              <div>
                {vehicleInfo?.expired ? (
                  vehicleInfo.isExpired ? (
                    <span className="text-color-weak">已过期</span>
                  ) : (
                    <>
                    <span className="text-6xl font-bold">
                      {dayjs(vehicleInfo.expired).diff(vehicleInfo.time, 'day') + 1}
                    </span>
                      <span className="ml-1 text-color-weak">天</span>
                    </>
                  )
                ) : (
                  '--'
                )}
              </div>
              <div className="text-3xs text-color-weak">
                服务有效期：
                {vehicleInfo?.expired ? dayjs(vehicleInfo.expired).format('YYYY-MM-DD') : '--'}
              </div>
            </div>
          </div>
        </div>

        {/*24小时热线*/}
        <div className="flex flex-col bg-white rounded-lg space-y-3 px-4 py-3" onClick={(e) => handlePhoneCall(e, '4008818777')}>
          <div className="flex items-center justify-between">
            <div className="flex">
              <Image
                  src="/images/24hour.png"
                  alt="arrow-right"
                  width={25}
                  height={25}
              />
              <span className="ml-2">24小时热线</span>
            </div>
            <span className="text-xs text-gray-500">
              ************
            </span>
          </div>
        </div>
      </div>

      <ActionSheet
        cancelText="取消"
        visible={visible}
        actions={[
          { text: '腾讯地图', key: 'txLauncher' },
          {
            text: '百度地图',
            key: 'bdLauncher',
          },
          {
            text: '高德地图',
            key: 'amapLauncher',
          },
        ]}
        onClose={() => setVisible(false)}
        onAction={handleAction}
      />
    </Page>
  );
}
