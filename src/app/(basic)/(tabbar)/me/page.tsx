'use client';

import { RightOutline, AddOutline } from 'antd-mobile-icons';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import Page from '@/components/Page';
import List from '@/components/List';
import Button from '@/components/Button';
import { toImg } from '@/utils';
import { getVehicles } from '@/server/api/home';
import { getUserInfo } from '@/server/api/me';
import { useQuery } from '@tanstack/react-query';
import * as AspectRatio from '@radix-ui/react-aspect-ratio';

export default function Me() {
  const router = useRouter();

  const { data: list } = useQuery({ queryKey: ['vehicles'], queryFn: getVehicles });
  const { data: info } = useQuery({ queryKey: ['userInfo'], queryFn: getUserInfo });

  return (
    <Page nav={false} title="我的">
      <div
        className="absolute left-0 top-0 -z-10 flex h-[350px] w-full  items-center
      justify-center bg-[url('/images/G/<EMAIL>')] bg-cover bg-center bg-no-repeat"
      />
      <div className="space-y-3 px-3 py-6">
        <div
          className="flex items-center  px-5 py-8 text-white"
          onClick={() => router.push(`me/info`)}
        >
          <div className="relative h-[70px] w-[70px] overflow-hidden rounded-full">
            <Image
              priority
              fill
              quality={25}
              sizes="70px"
              alt="头像"
              style={{ objectFit: 'cover' }}
              src={info?.logo || '/images/G/<EMAIL>'}
            />
          </div>
          <div className="ml-5">
            <div className="text-2xl">{info?.nickName || '-'}</div>
            <div className="text-xs" style={{ color: 'rgba(255, 255, 255, .6)' }}>
              {info?.loginName}
            </div>
          </div>

          <div className="ml-[auto] p-5">
            <RightOutline />
          </div>
        </div>

        {(list || []).length > 0 ? (
          <div className="rounded-2xl bg-white p-4">
            <div className="mb-2 text-xl">我的车辆</div>
            {list?.map((_: any) => (
              <Link href={`/vehicle/${_.deviceNo}/info`} key={_.id}>
                <div className="flex items-center py-2">
                  <div className="h-[70px] w-[70px] rounded-md bg-[#f6f7f8]">
                    <div className="relative w-[90px] -translate-x-[10px]">
                      <AspectRatio.Root ratio={482 / 375}>
                        <Image
                          src={_.img ? toImg(_.img) : '/images/<EMAIL>'}
                          alt="车型图"
                          fill
                          priority
                          sizes="90px"
                        />
                      </AspectRatio.Root>
                    </div>
                  </div>
                  <div className="ml-5">
                    <div className="text-2xl text-[#30333F]">{_.nickName}</div>
                    <div className="text-xs text-[#908F94]">{_.carModel}</div>
                  </div>
                  {_.isOwner ? (
                    <div
                      className="ml-[auto]  px-2 py-1 text-3xs text-[#FF6430]"
                      style={{ background: 'rgba(255, 208, 192, 0.60)', borderRadius: '10px' }}
                    >
                      车主
                    </div>
                  ) : null}
                </div>
              </Link>
            ))}
          </div>
        ) : null}
        <Button block type="primary" href="/bind">
          <div className="flex h-full w-full items-center justify-center text-white">
            <AddOutline className=" mr-3 text-3xl " />
            添加车辆
          </div>
        </Button>
        <List
          items={[
            {
              key: 1,
              icon: '/images/G/<EMAIL>',
              title: '我的收藏',
              href: 'me/collect',
            },
            {
              key: 3,
              icon: '/images/G/<EMAIL>',
              title: '设置',
              href: 'me/setting',
            },
            {
              key: 4,
              icon: '/images/G/<EMAIL>',
              title: '关于',
              href: 'me/about',
            },
          ]}
        />
        <div className="h-[calc(49px+env(safe-area-inset-bottom))]" />
      </div>
    </Page>
  );
}
