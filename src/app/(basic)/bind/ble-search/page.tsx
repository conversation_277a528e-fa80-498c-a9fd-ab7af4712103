'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Toast } from 'antd-mobile';
import Page from '@/components/Page';
import { useBLEClient } from '@/lib/ble';
import Button from '@/components/Button';
import List from '@/components/List';
import { useMutation } from '@tanstack/react-query';
import { updateDevice } from '@/server/api/home';
import { bindVehicle } from '@/server/api/home';
import Tutorial from './components/tutorial';
import { state } from '@/store';
import { useAfterSwitchVehicle } from '@/hooks/useAfterSwitchVehicle';

export default function BLESearch({ searchParams }: { searchParams: { [key: string]: string } }) {
  const router = useRouter();
  const [count, setCount] = useState<number>(15);
  const [timing, setTiming] = useState(true);
  const bleClient = useBLEClient();
  const [devices, setDevices] = useState<{ deviceMac: string; deviceName: string }[]>([]);
  const [process, setProcess] = useState(true);
  const afterSwitchVehicle = useAfterSwitchVehicle();
  const { dvcId } = searchParams;

  const { mutate } = useMutation({
    mutationFn: updateDevice,
    onSuccess: () => {
      Toast.show('添加成功');
      router.back();
    },
  });

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (timing) {
      interval = setInterval(() => {
        setCount((preCount) => {
          if (preCount <= 1) {
            setTiming(false);
            setProcess(false);
            clearInterval(interval);
            return 15;
          }
          return preCount - 1;
        });
      }, 1000);
    }
    return () => {
      clearInterval(interval);
    };
  }, [timing, devices]);

  const handleSearch = () => {
    setProcess(true);
    setTiming(true);
    bleClient.startDiscovery(
      state.bind.bluetoothMac,
      (res: { deviceMac: string; deviceName: string }) => {
        setDevices([res]);
      }
    );
  };

  const handleConnect = async () => {
    // 停止搜索
    bleClient.stopDiscovery();
    setProcess(false);
    if (searchParams.hasOwnProperty('dvcId')) {
      // 添加设备
      mutate({
        newEquNo: state.bind.vcuNo,
        bluetoothMac: state.bind.bluetoothMac,
        dvcId,
      });
    } else {
      const result = await bindVehicle({
        ...state.bind,
        province: state.location.province,
        city: state.location.city,
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        afterSwitchVehicle();
        Toast.show('绑定成功');
        router.replace('/');
      }
    }
  };

  // 进入页面发起蓝牙搜索
  useEffect(() => {
    bleClient.startDiscovery(
      state.bind.bluetoothMac,
      (res: { deviceMac: string; deviceName: string }) => {
        setDevices([res]);
      }
    );
  }, [bleClient]);

  return (
    <Page>
      {!process && devices.length === 0 ? (
        <div className="flex h-full flex-col px-3">
          <Image
            src="/images/empty-device.png"
            alt="无设备"
            width={200}
            height={200}
            className="mx-auto"
          />
          <div className="flex-1">
            <div className="mb-3 text-xl">未搜索到附近设备</div>
            <div className="rounded-2xl bg-white p-4">
              <div className="mb-3">请排除以下几种可能，再重新搜索</div>
              <div className="text-xs text-color-weak">
                <div>一、您的手机蓝牙未开启；</div>
                <div>二、设备距离手机位置较远；</div>
                <div>三、设备已连接其他手机，需在其他手机APP内解绑后才能绑定；</div>
                <div>
                  四、设备未激活绑定模式；
                  <Tutorial />
                </div>
              </div>
            </div>
          </div>
          <Button block type="primary" className="mb-12" onClick={handleSearch}>
            重新搜索
          </Button>
        </div>
      ) : (
        <div className="flex h-full flex-col items-center">
          {process ? (
            <Image
              src="/images/bluetooth-search.gif"
              alt="搜索中"
              width={240}
              height={220}
              className="mt-16"
            />
          ) : (
            <Image src="/images/<EMAIL>" alt="搜索完成" width={80} height={80} />
          )}
          {devices.length === 0 ? (
            <>
              <div className="mt-16 text-xl">
                正在搜索附近的设备（<span className="text-primary">{count}s</span>）...
              </div>
              <div className="mt-2 text-3xs text-color-weak">
                搜索过程中请靠近设备，并确保车辆已激活绑定模式。
              </div>
              <div className="flex-1" />
              <div className="mb-12">
                <Tutorial />
              </div>
            </>
          ) : (
            <div className="flex h-full w-full flex-col px-3">
              <div className="mb-3 mt-6 text-xl">
                已搜索到<span className="text-primary">{devices.length}</span>个设备
              </div>
              <List
                items={[
                  {
                    icon: (
                      <div className="h-[70px] w-[70px] rounded-xl bg-[#F6F7FB]">
                        <Image
                          src="/images/C-2/car-img1.png"
                          alt="车型图"
                          width={70}
                          height={70}
                          priority
                        />
                      </div>
                    ),
                    title: state.bind.vcuNo,
                    arrow: true,
                    onClick: handleConnect,
                  },
                ]}
              />
              <div className="flex-1" />
              {!process && (
                <Button block type="primary" className="mb-12" onClick={handleSearch}>
                  重新搜索
                </Button>
              )}
            </div>
          )}
        </div>
      )}
    </Page>
  );
}
