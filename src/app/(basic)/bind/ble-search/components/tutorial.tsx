'use client';

import { useState } from 'react';
import Image from 'next/image';
import { CenterPopup } from 'antd-mobile';

export default function Tutorial() {
  const [visible, setVisible] = useState(false);

  return (
    <>
      <span
        className="text-xs text-error"
        onClick={() => {
          setVisible(true);
        }}
      >
        如何激活？
      </span>
      <CenterPopup
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
        style={{ '--border-radius': '1rem' }}
      >
        <div className="overflow-hidden rounded-2xl">
          <div className="flex flex-col">
            <Image alt="激活图示" src="/images/guide.gif" width={280} height={160} />
            <div className="p-3">
              <div className="text-center text-xl font-medium">激活说明</div>
              <div className="mt-2 text-sm text-color-secondary">
                打开电门长按“M”键3秒可激活车辆进入绑定模式，如果没有搜索到车辆或激活时间超过15秒，请重新激活车辆！
              </div>
            </div>
          </div>
          <div
            className="mt-2 border-t border-[#eee] p-3 text-center text-xl text-primary"
            onClick={() => {
              setVisible(false);
            }}
          >
            我知道了
          </div>
        </div>
      </CenterPopup>
    </>
  );
}
