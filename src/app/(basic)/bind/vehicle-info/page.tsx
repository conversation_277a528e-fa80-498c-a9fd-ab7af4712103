'use client';

import { useTransition, useEffect, useMemo, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { Form, Input, Toast, Picker } from 'antd-mobile';
import { ScanningOutline } from 'antd-mobile-icons';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { checkVehicleInfo } from '@/server/actions/home';
import { getBrands } from '@/server/api/home';
import { getAllEnum } from '@/server/api/enum';
import { useQuery } from '@tanstack/react-query';
import { state, setBind } from '@/store';

type PickerColumnItem = {
  label: ReactNode;
  value: string;
  key?: string | number;
};

export default function VehicleInfo() {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [form] = Form.useForm();

  // 获取品牌列表
  const { data: brands } = useQuery({
    queryKey: ['brands'],
    queryFn: getBrands,
  });
  const brandList = useMemo(
    () => (brands || []).map((item) => ({ label: item.brandName, value: item.id })),
    [brands]
  );

  const { data: enums } = useQuery({
    queryKey: ['enums'],
    queryFn: getAllEnum,
  });
  const { batteryType, batteryVoltage, batteryCapacity } = useMemo(() => {
    let batteryType: PickerColumnItem[] = [];
    let batteryVoltage: PickerColumnItem[] = [];
    let batteryCapacity: PickerColumnItem[] = [];
    if (enums) {
      batteryType = Object.keys(enums.enumBatteryType).map((key) => ({
        label: enums.enumBatteryType[key],
        value: key,
      }));
      batteryVoltage = Object.keys(enums.enumBatteryVtg).map((key) => ({
        label: enums.enumBatteryVtg[key],
        value: key,
      }));
      batteryCapacity = Object.keys(enums.enumBatteryCapacity).map((key) => ({
        label: enums.enumBatteryCapacity[key],
        value: key,
      }));
    }
    return {
      batteryType,
      batteryVoltage,
      batteryCapacity,
    };
  }, [enums]);

  useEffect(() => {
    // 品牌默认选择 立马
    const defaultBrand = brandList.find((item) => item.label === '立马');
    if (defaultBrand) {
      form.setFieldValue('brandId', [state.bind.brandId || defaultBrand.value]);
    }
  }, [form, brandList]);

  const handleSubmit = async (values: any) => {
    startTransition(async () => {
      const result = await checkVehicleInfo({
        deviceNo: values.deviceNo,
        identity: values.identity,
        brandId: values.brandId[0],
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        // 校验通过
        setBind({
          ...values,
          brandId: values.brandId[0],
          batteryType: values.batteryType[0],
          batteryVoltage: values.batteryVoltage[0],
          batteryCapacity: values.batteryCapacity[0],
        });
        router.push('/bind/device');
      }
    });
  };

  // 扫一扫
  const handleScan = (key: 'identity' | 'deviceNo') => {
    window.jsBridgeHelper
      ?.sendMessage('qrCode')
      .then((res: { code: string; msg: string; data: string }) => {
        if (res.code === '200') {
          // 正常二维码格式L1ZL1ZEH8P0503159、062310230361
          // 旧版本格式 http://www.lima-info.com/wx/062310230361.aspx
          const value = res.data.replace(/^.*\/([A-Za-z0-9]+)\.aspx$/, '$1').trim();
          form.setFieldValue(key, value);
        }
      });
  };

  return (
    <Page title="填写车辆信息">
      <div className="h-full px-1">
        <Form
          form={form}
          layout="horizontal"
          mode="card"
          requiredMarkStyle="none"
          footer={
            <Form.Subscribe
              to={[
                'identity',
                'deviceNo',
                'brandId',
                'batteryType',
                'batteryVoltage',
                'batteryCapacity',
              ]}
            >
              {({ identity, deviceNo, brandId, batteryType, batteryVoltage, batteryCapacity }) => {
                return (
                  <Button
                    type="primary"
                    block
                    htmlType="submit"
                    loading={isPending}
                    disabled={
                      !(
                        identity &&
                        deviceNo &&
                        brandId &&
                        batteryType &&
                        batteryVoltage &&
                        batteryCapacity
                      )
                    }
                  >
                    确定
                  </Button>
                );
              }}
            </Form.Subscribe>
          }
          initialValues={{
            identity: state.bind.identity,
            deviceNo: state.bind.deviceNo,
            brandId: [state.bind.brandId],
            batteryType: [state.bind.batteryType],
            batteryVoltage: [state.bind.batteryVoltage],
            batteryCapacity: [state.bind.batteryCapacity],
          }}
          onFinish={handleSubmit}
        >
          <Form.Header>基本信息</Form.Header>
          <Form.Item
            label="车辆识别码"
            name="identity"
            extra={<ScanningOutline fontSize={18} onClick={() => handleScan('identity')} />}
          >
            <Input placeholder="请输入车辆识别码" />
          </Form.Item>
          <Form.Item
            label="车架号"
            name="deviceNo"
            extra={<ScanningOutline fontSize={18} onClick={() => handleScan('deviceNo')} />}
          >
            <Input placeholder="请输入车架号" />
          </Form.Item>
          <Form.Item
            label="品牌"
            name="brandId"
            trigger="onConfirm"
            onClick={(_, pickerRef) => {
              pickerRef.current?.open();
            }}
          >
            <Picker columns={[brandList]} cancelText="取消">
              {(value) => {
                return <span>{value[0]?.label}</span>;
              }}
            </Picker>
          </Form.Item>
          <Form.Header>电池信息</Form.Header>
          <Form.Item
            label="电池类型"
            name="batteryType"
            trigger="onConfirm"
            onClick={(_, pickerRef) => {
              pickerRef.current?.open();
            }}
          >
            <Picker columns={[batteryType]} cancelText="取消">
              {(value) => {
                return <span>{value[0]?.label}</span>;
              }}
            </Picker>
          </Form.Item>
          <Form.Item
            label="电池电压"
            name="batteryVoltage"
            trigger="onConfirm"
            onClick={(_, pickerRef) => {
              pickerRef.current?.open();
            }}
          >
            <Picker columns={[batteryVoltage]} cancelText="取消">
              {(value) => {
                return <span>{value[0]?.label}</span>;
              }}
            </Picker>
          </Form.Item>
          <Form.Item
            label="电池容量"
            name="batteryCapacity"
            trigger="onConfirm"
            onClick={(_, pickerRef) => {
              pickerRef.current?.open();
            }}
          >
            <Picker columns={[batteryCapacity]} cancelText="取消">
              {(value) => {
                return <span>{value[0]?.label}</span>;
              }}
            </Picker>
          </Form.Item>
          <Form.Header />
        </Form>
      </div>
    </Page>
  );
}
