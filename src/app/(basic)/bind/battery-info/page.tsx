'use client';

import { useMemo, useTransition, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { Form, Toast, Picker } from 'antd-mobile';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { bindVehicle } from '@/server/api/home';
import { getAllEnum } from '@/server/api/enum';
import { useQuery } from '@tanstack/react-query';
import { state } from '@/store';
import { useAfterSwitchVehicle } from '@/hooks/useAfterSwitchVehicle';

type PickerColumnItem = {
  label: ReactNode;
  value: string;
  key?: string | number;
};

export default function BatteryInfo() {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [form] = Form.useForm();
  const afterSwitchVehicle = useAfterSwitchVehicle();

  const { data: enums } = useQuery({
    queryKey: ['enums'],
    queryFn: getAllEnum,
  });
  const { batteryType, batteryVoltage, batteryCapacity } = useMemo(() => {
    let batteryType: PickerColumnItem[] = [];
    let batteryVoltage: PickerColumnItem[] = [];
    let batteryCapacity: PickerColumnItem[] = [];
    if (enums) {
      batteryType = Object.keys(enums.enumBatteryType).map((key) => ({
        label: enums.enumBatteryType[key],
        value: key,
      }));
      batteryVoltage = Object.keys(enums.enumBatteryVtg).map((key) => ({
        label: enums.enumBatteryVtg[key],
        value: key,
      }));
      batteryCapacity = Object.keys(enums.enumBatteryCapacity).map((key) => ({
        label: enums.enumBatteryCapacity[key],
        value: key,
      }));
    }
    return {
      batteryType,
      batteryVoltage,
      batteryCapacity,
    };
  }, [enums]);

  const handleSubmit = (values: any) => {
    startTransition(async () => {
      // 直接绑定车辆
      const result = await bindVehicle({
        ...state.bind,
        batteryType: values.batteryType[0],
        batteryVoltage: values.batteryVoltage[0],
        batteryCapacity: values.batteryCapacity[0],
        province: state.location.province,
        city: state.location.city,
      });
      if (result?.error) {
        Toast.show(result.error);
        return;
      }
      afterSwitchVehicle();
      router.push('/');
    });
  };

  return (
    <Page title="填写电池信息">
      <div className="px-1">
        <Form
          form={form}
          layout="horizontal"
          mode="card"
          requiredMarkStyle="none"
          footer={
            <Form.Subscribe to={['batteryType', 'batteryVoltage', 'batteryCapacity']}>
              {({ batteryType, batteryVoltage, batteryCapacity }) => {
                return (
                  <Button
                    type="primary"
                    block
                    htmlType="submit"
                    loading={isPending}
                    disabled={!(batteryType && batteryVoltage && batteryCapacity)}
                  >
                    确定
                  </Button>
                );
              }}
            </Form.Subscribe>
          }
          onFinish={handleSubmit}
        >
          <Form.Item
            label="电池类型"
            name="batteryType"
            trigger="onConfirm"
            onClick={(_, pickerRef) => {
              pickerRef.current?.open();
            }}
          >
            <Picker columns={[batteryType]} cancelText="取消">
              {(value) => {
                return <span>{value[0]?.label}</span>;
              }}
            </Picker>
          </Form.Item>
          <Form.Item
            label="电池电压"
            name="batteryVoltage"
            trigger="onConfirm"
            onClick={(_, pickerRef) => {
              pickerRef.current?.open();
            }}
          >
            <Picker columns={[batteryVoltage]} cancelText="取消">
              {(value) => {
                return <span>{value[0]?.label}</span>;
              }}
            </Picker>
          </Form.Item>
          <Form.Item
            label="电池容量"
            name="batteryCapacity"
            trigger="onConfirm"
            onClick={(_, pickerRef) => {
              pickerRef.current?.open();
            }}
          >
            <Picker columns={[batteryCapacity]} cancelText="取消">
              {(value) => {
                return <span>{value[0]?.label}</span>;
              }}
            </Picker>
          </Form.Item>
          <Form.Header />
        </Form>
      </div>
    </Page>
  );
}
