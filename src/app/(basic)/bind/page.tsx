'use client';

import { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { Input, Space, Toast, Popup } from 'antd-mobile';
import { ScanningOutline, QuestionCircleOutline } from 'antd-mobile-icons';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { checkBind } from '@/server/actions/home';
import { bindVehicle } from '@/server/api/home';
import { state, setBind, resetBind } from '@/store';
import { useAfterSwitchVehicle } from '@/hooks/useAfterSwitchVehicle';

export default function Bind() {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [value, setValue] = useState('');
  const [visible, setVisible] = useState(false);
  const afterSwitchVehicle = useAfterSwitchVehicle();

  const handBind = async () => {
    // 数字、字母或组合
    if (!/^[A-Za-z0-9]+$/.test(value)) {
      Toast.show('车架号/车辆识别码格式错误');
      return;
    }
    // 重置绑定信息
    resetBind();
    startTransition(async () => {
      const check = await checkBind(value);
      if ('error' in check) {
        // 6135: 用户绑定车辆数已达到上限 6137: 用户已绑定该车辆 6143: 该车辆已被绑定
        Toast.show(check.error);
        return;
      }
      // 车架号/识别码是否存在数据库中
      if (check.existSn) {
        // 如果存在，判断车辆是否绑定设备
        if (check.isBind) {
          // 如果绑定设备，判断设备是否为简版
          if (check.isLowVersion) {
            // 如果是简版，填写电池信息
            setBind({ deviceNo: check.deviceNo, identity: check.identifier });
            router.push('/bind/battery-info');
          } else {
            // 绑定车辆
            const result = await bindVehicle({
              deviceNo: check.deviceNo,
              identity: check.identifier,
              province: state.location.province,
              city: state.location.city,
            });
            if (result?.error) {
              Toast.show(result.error);
              return;
            }
            afterSwitchVehicle();
            router.push('/');
          }
        } else {
          // 如果没有绑定设备，让用户绑定设备
          setBind({ deviceNo: check.deviceNo, identity: check.identifier });
          router.push('/bind/device');
        }
      } else {
        // 如果不存在，让用户填写车辆信息
        setBind({
          deviceNo: check.deviceNo || value,
          identity: check.identifier || value,
        });
        router.push('/bind/vehicle-info');
      }
    });
  };

  // 扫一扫
  const handleScan = () => {
    window.jsBridgeHelper
      ?.sendMessage('qrCode')
      .then((res: { code: string; msg: string; data: string }) => {
        if (res.code === '200') {
          // 正常二维码格式L1ZL1ZEH8P0503159、062310230361
          // 旧版本格式 http://www.lima-info.com/wx/062310230361.aspx
          const value = res.data.replace(/^.*\/([A-Za-z0-9]+)\.aspx$/, '$1').trim();
          setValue(value);
        }
      });
  };

  // 用车指南
  const handleGuide = () => {
    window.jsBridgeHelper?.sendMessage('webBrowse', {
      url: 'http://u5201604.viewer.maka.im/pcviewer/3JUXPC9V',
      navBar: false,
      title: '用车指南',
    });
  };

  return (
    <Page
      title="车辆绑定"
      right={
        <span className="text-sm text-color-weak" onClick={handleGuide}>
          用车指南
        </span>
      }
    >
      <div className="px-4">
        <Space className="mb-3 ml-2 mt-10  flex items-center">
          扫码来绑定车辆
          <QuestionCircleOutline
            color="#ff3141"
            fontSize={20}
            onClick={() => {
              setVisible(true);
            }}
          />
        </Space>
        <div className="mb-3  flex items-center rounded-lg bg-white px-4 py-3">
          <Input
            placeholder="请输入车架号/车辆识别码"
            value={value}
            onChange={(value) => setValue(value)}
          />
          <ScanningOutline fontSize={18} onClick={handleScan} />
        </div>
        <div className="mb-6 ml-2 text-xs text-color-weak">请扫描整车二维码来绑定车辆~</div>
        <Button block type="primary" loading={isPending} disabled={!value} onClick={handBind}>
          确认绑定
        </Button>
        <Popup
          visible={visible}
          onMaskClick={() => {
            setVisible(false);
          }}
        >
          <div style={{ height: '50vh', overflowY: 'scroll', padding: '30px' }}>
            <h3 className="pb-2 text-xl font-bold">1.车主如何绑定？</h3>
            <p>
              答：当车辆没有账户绑定时，用户可通过 :
              a.输入车辆识别码;b.扫描整车二维码来绑定车辆，绑定成功后，即可成为车主。
            </p>
            <h3 className="pb-2  pt-4 text-xl font-bold">2.如何邀请家人朋友共享车辆？</h3>
            <p>
              答：车主账号可以通过添加手机号邀请家人朋友使用爱车（“车况”—“共享”—“共享账号管理”—“添加账号”），受邀请的账号登录后可看到邀请信息，接受邀请后则邀请成功。
            </p>
            <h3 className="pb-2  pt-4 text-xl font-bold">3.一个账户最多绑定几辆车？</h3>
            <p>
              答：最多绑定8辆，当用户已绑定8辆车（包括车主）为拥有车辆已达上限，请先解绑部分车辆再绑定。
            </p>
            <h3 className="pb-2 pt-4 text-xl font-bold">4.一辆车最多能被几个账户绑定？</h3>
            <p>答：一辆车最多是能被6位账户绑定（包括车主）。</p>
          </div>
        </Popup>
      </div>
    </Page>
  );
}
