'use client';

import { useState, useTransition } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Input, Toast } from 'antd-mobile';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { PASSWORDREGEX, encryptPassword } from '@/utils';
import { changePhoneForCheck } from '@/server/actions/user';

export default function Password({ searchParams }: { searchParams: { oldPhone: string } }) {
  const router = useRouter();
  const { oldPhone } = searchParams;
  const [password, setPassword] = useState('');
  const [isPending, startTransition] = useTransition();

  // 账号密码登录
  const handleLogin = () => {
    startTransition(async () => {
      const result = await changePhoneForCheck({
        phone: oldPhone,
        password: encryptPassword(password),
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        router.push(`/user/edit-phone-3?oldPhone=${oldPhone}`);
      }
    });
  };

  return (
    <Page>
      <div className="p-4">
        <div className="text-4xl font-medium">密码验证</div>
        <div className="mt-3">需要更换手机号？请输入原手机号 {oldPhone} 的登录密码验证</div>
        <div className="mb-3 mt-8 rounded-lg bg-white px-4 py-3">
          <Input
            placeholder="请输入登录密码"
            type="password"
            value={password}
            onChange={(value) => setPassword(value)}
          />
        </div>
        <div className="mb-6  mt-1 flex justify-between text-xs">
          <Link href={`/user/forgot?phone=${oldPhone}`} className="text-color-secondary">
            忘记密码？
          </Link>
          <Link href={`/user/edit-phone?phone=${oldPhone}`} replace className="text-primary">
            验证原手机号
          </Link>
        </div>
        <Button
          block
          type="primary"
          disabled={!PASSWORDREGEX.test(password)}
          loading={isPending}
          onClick={handleLogin}
        >
          下一步
        </Button>
      </div>
    </Page>
  );
}
