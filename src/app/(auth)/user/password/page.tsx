'use client';

import { useState, useTransition } from 'react';
import Link from 'next/link';
import { Input, Toast } from 'antd-mobile';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { PASSWORDREGEX, encryptPassword } from '@/utils';
import { passwordLogin } from '@/server/actions/user';
import { useNative } from '@/lib/native';
import cookies from 'js-cookie';

export default function Password({ searchParams }: { searchParams: { phone: string } }) {
  const { phone } = searchParams;
  const [password, setPassword] = useState('');
  const [isPending, startTransition] = useTransition();
  const { clientId, isiOS } = useNative();

  // 账号密码登录
  const handleLogin = () => {
    startTransition(async () => {
      const result = await passwordLogin({
        phone,
        clientId: clientId,
        password: encryptPassword(password),
        appType: isiOS ? 'ios' : 'android',
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        window.jsBridgeHelper?.sendMessage('token', cookies.get('token'));
        window.jsBridgeHelper?.sendMessage('userInfo', { phone: cookies.get('phone') });
      }
    });
  };

  return (
    <Page>
      <div className="p-4">
        <div className="text-4xl font-medium">账号密码登录</div>
        <div className="mb-3 mt-8 rounded-lg bg-white px-4 py-3">
          <Input
            placeholder="请输入登录密码"
            type="password"
            value={password}
            onChange={(value) => setPassword(value)}
          />
        </div>
        <div className="mb-6  mt-1 flex justify-between text-xs">
          <Link href={`/user/forgot?phone=${phone}`} className="text-color-secondary">
            忘记密码？
          </Link>
          <Link href={`/user/code?phone=${phone}`} replace className="text-primary">
            验证码登录
          </Link>
        </div>
        <Button
          block
          type="primary"
          disabled={!PASSWORDREGEX.test(password)}
          loading={isPending}
          onClick={handleLogin}
        >
          确定
        </Button>
      </div>
    </Page>
  );
}
