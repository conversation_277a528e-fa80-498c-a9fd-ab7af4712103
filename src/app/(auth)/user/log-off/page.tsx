'use client';

import { useState, useEffect, useTransition } from 'react';
import { Toast, Dialog } from 'antd-mobile';
import { ExclamationCircleFill } from 'antd-mobile-icons';
import Page from '@/components/Page';
import VerificationCodeInput from '@/components/VerificationCodeInput';
import Button from '@/components/Button';
import { doCancellation, getCodeByPhone } from '@/server/actions/user';
import { encryptCode } from '@/utils';
import { useLogout } from '@/hooks/useLogout';
import { useQueryClient } from '@tanstack/react-query';

export default function Code({ searchParams }: { searchParams: { phone: string } }) {
  const [_, startTransition] = useTransition();
  const [isPending, startTransitionSignup] = useTransition();
  const [count, setCount] = useState<number>(60);
  const [timing, setTiming] = useState(false);
  const { phone } = searchParams;
  const [code, setCode] = useState('');
  const queryClient = useQueryClient();
  const logout = useLogout();

  useEffect(() => {
    const timer = setTimeout(() => {
      handleReGetCode();
    }, 100);
    return () => {
      clearTimeout(timer);
    };
  }, []);

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (timing) {
      interval = setInterval(() => {
        setCount((preCount) => {
          if (preCount <= 1) {
            setTiming(false);
            clearInterval(interval);
            return 60;
          }
          return preCount - 1;
        });
      }, 1000);
    }
    return () => {
      clearInterval(interval);
    };
  }, [timing]);

  // 获取验证码
  const handleReGetCode = () => {
    startTransition(async () => {
      const result = await getCodeByPhone({ phone, sign: encryptCode(phone) });
      console.log('[ result ] >', result);
      if (result?.error) {
        Toast.show(result.error);
      } else {
        setTiming(true);
        Toast.show('验证码已经发送');
      }
    });
  };

  // 登录
  const handleLogin = () => {
    Dialog.confirm({
      header: (
        <ExclamationCircleFill
          style={{
            fontSize: 64,
            color: 'var(--adm-color-warning)',
          }}
        />
      ),
      title: '温馨提示',
      content:
        '注销账号是不可恢复的操作，请您谨慎操作，一旦注销成功，您将无法登录或使用账号内的信息。请再次确认是否注销账号！',
      confirmText: '仍要注销',
      onAction: (action) => {
        if (action.key === 'confirm') {
          startTransitionSignup(async () => {
            const result = await doCancellation({
              phone,
              code,
            });
            if (result?.error) {
              Toast.show(result.error);
            } else {
              queryClient.clear();
              logout();
            }
          });
        }
      },
    });
  };

  return (
    <Page>
      <div className="p-4">
        <div className="text-4xl font-medium">注销账号</div>
        <div className="mt-3">
          验证码已发送至<span className="text-color-weak">{phone}</span>
        </div>
        <div className="mt-8 flex justify-center">
          <VerificationCodeInput value={code} onChange={(value) => setCode(value)} />
        </div>
        <div className="mb-4 mt-3 flex justify-between text-xs">
          <div>
            收不到验证码？
            {timing ? (
              <span className="text-color-weak">重新获取（{count} s）</span>
            ) : (
              <span className="text-primary" onClick={handleReGetCode}>
                重新获取
              </span>
            )}
          </div>
        </div>
        <Button
          block
          type="primary"
          disabled={code.length !== 6}
          loading={isPending}
          onClick={handleLogin}
        >
          确定
        </Button>
      </div>
    </Page>
  );
}
