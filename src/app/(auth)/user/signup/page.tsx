'use client';

import { useState, useEffect, useTransition } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Form, Input, Checkbox, Toast } from 'antd-mobile';
import { CheckCircleFill, EyeInvisibleOutline, EyeOutline } from 'antd-mobile-icons';
import { PHONEREGEX, PASSWORDREGEX, encryptPassword, encryptCode } from '@/utils';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { getCodeByPhone, phoneRegister } from '@/server/actions/user';
import cookies from 'js-cookie';
import { setSignup, state } from '@/store';
import './page.scss';
import Image from 'next/image';

export default function Signup() {
  const router = useRouter();
  const [count, setCount] = useState<number>(60);
  const [timing, setTiming] = useState(false);
  const [form] = Form.useForm();
  const [_, startTransition] = useTransition();
  const [isPending, startTransitionSignup] = useTransition();
  const [pwdVisible, setPwdVisible] = useState(false);
  const [confirmPwdVisible, setConfirmPwdVisible] = useState(false);

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (timing) {
      interval = setInterval(() => {
        setCount((preCount) => {
          if (preCount <= 1) {
            setTiming(false);
            clearInterval(interval);
            return 60;
          }
          return preCount - 1;
        });
      }, 1000);
    }
    return () => {
      clearInterval(interval);
    };
  }, [timing]);

  // 获取验证码
  const handleGetCode = () => {
    const phone = form.getFieldValue('phone');
    if (!PHONEREGEX.test(phone)) {
      return Toast.show('手机号格式不正确');
    }
    startTransition(async () => {
      const result = await getCodeByPhone({ phone, sign: encryptCode(phone) });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        setTiming(true);
        Toast.show('验证码已发送');
      }
    });
  };

  const handleSubmit = (values: any) => {
    if (!values.checked) {
      return Toast.show('请阅读并同意用户协议和隐私政策');
    }
    if (values.password !== values.confirmPassword) {
      return Toast.show('两次密码输入不一致');
    }
    startTransitionSignup(async () => {
      const result = await phoneRegister({
        phone: values.phone,
        code: values.code,
        password: encryptPassword(values.password),
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        window.jsBridgeHelper?.sendMessage('token', cookies.get('token'));
        window.jsBridgeHelper?.sendMessage('userInfo', { phone: cookies.get('phone') });
        // 清空全局表单数据
        setSignup({});
      }
    });
  };

  // 将表单数据同步到全局，目的是查看用户协议和隐私政策后返回表单数据不丢失
  const handleValuesChange = (changedValues: any, allValues: any) => {
    setSignup(allValues);
  };

  return (
    <Page
      title="注册"
      showTitle={false}
      onBack={() => {
        // 清空全局表单数据
        setSignup({});
        router.back();
      }}
    >
      <div className="signup_container">
        {/*<video autoPlay muted loop webkit-playsinline playsInline className="backgroundVideo">*/}
        {/*  <source src="/images/limaVideo.mp4" type="video/mp4" />*/}
        {/*</video>*/}
        <Image
          alt="welcome_gif"
          layout="fill"
          src="/images/limaGif.gif"
          className="backgroundVideo"
        />

        <div className="px-1 text-white ">
          <div className="title">欢迎注册立马科技</div>
          <Form
            form={form}
            layout="horizontal"
            mode="card"
            requiredMarkStyle="none"
            footer={
              <>
                <div className="mb-5 flex items-center text-xs text-color-weak">
                  <Form.Item noStyle name="checked" valuePropName="checked">
                    <Checkbox
                      icon={(checked) => {
                        return checked ? (
                          <CheckCircleFill color="#fff" fontSize={22} />
                        ) : (
                          <div className="h-[22px] w-[22px]  rounded-full border border-color-light" />
                        );
                      }}
                    />
                  </Form.Item>
                  <div className="read_text">
                    &nbsp;&nbsp;我已阅读同意
                    <Link href="/me/about/agreement" className="text-white">
                      《<span className="underline decoration-1">用户协议</span>》
                    </Link>
                    和
                    <Link href="/me/about/privacy" className="text-white">
                      《<span className="underline decoration-1">隐私政策</span>》
                    </Link>
                  </div>
                </div>
                <Form.Subscribe to={['phone', 'code', 'password', 'confirmPassword']}>
                  {({ phone, code, password, confirmPassword }) => {
                    return (
                      <Button
                        type="primary"
                        className="bg-custom-gray mt-5"
                        block
                        htmlType="submit"
                        loading={isPending}
                        disabled={
                          !(
                            PHONEREGEX.test(phone) &&
                            code &&
                            password &&
                            PASSWORDREGEX.test(password) &&
                            confirmPassword &&
                            PASSWORDREGEX.test(confirmPassword)
                          )
                        }
                      >
                        确定
                      </Button>
                    );
                  }}
                </Form.Subscribe>
              </>
            }
            initialValues={state.signup}
            onValuesChange={handleValuesChange}
            onFinish={handleSubmit}
          >
            <div className="form">
              <Form.Item name="phone">
                <Input
                  placeholder="请输入手机号"
                  inputMode="numeric"
                  maxLength={11}
                  className="form_input bg-transparent"
                />
              </Form.Item>
              <Form.Item
                name="code"
                extra={
                  timing ? (
                    <span className="text-color-weak">{count}秒后重发</span>
                  ) : (
                    <span className="text-xs text-white" onClick={handleGetCode}>
                      获取验证码
                    </span>
                  )
                }
              >
                <Input
                  placeholder="请输入验证码"
                  inputMode="numeric"
                  maxLength={6}
                  className="form_input"
                />
              </Form.Item>
              <Form.Header />
              <Form.Item
                name="password"
                extra={
                  pwdVisible ? (
                    <EyeOutline onClick={() => setPwdVisible(false)} />
                  ) : (
                    <EyeInvisibleOutline onClick={() => setPwdVisible(true)} />
                  )
                }
              >
                <Input
                  placeholder="6-26位数字或字母"
                  type={pwdVisible ? 'text' : 'password'}
                  className="form_input form_input_password"
                />
              </Form.Item>
              <Form.Item
                name="confirmPassword"
                extra={
                  confirmPwdVisible ? (
                    <EyeOutline onClick={() => setConfirmPwdVisible(false)} />
                  ) : (
                    <EyeInvisibleOutline onClick={() => setConfirmPwdVisible(true)} />
                  )
                }
              >
                <Input
                  placeholder="确认密码"
                  type={confirmPwdVisible ? 'text' : 'password'}
                  className="form_input form_input_password"
                />
              </Form.Item>
            </div>

            <Form.Header />
          </Form>
        </div>
      </div>
    </Page>
  );
}
