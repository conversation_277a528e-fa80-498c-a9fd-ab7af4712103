'use client';

import { useTransition } from 'react';
import { useRouter } from 'next/navigation';

import { Button } from 'antd-mobile';
import Page from '@/components/Page';
import './page.scss';
import { Toast } from 'antd-mobile';
import Link from 'next/link';
import Image from 'next/image';
import { useNative } from '@/lib/native';
import { getAuthTokenDypns, dypnsLogin } from '@/server/actions/user';
import cookies from 'js-cookie';

export default function Landing() {
  const [_, startTransition] = useTransition();
  const [isPending, startTransitionSignup] = useTransition();
  const { clientId, isiOS, hasWechat } = useNative();
  const router = useRouter();
  let phoneNumberServer: any;

  const getToken = () => {
    Toast.show({
      icon: 'loading',
      content: '加载中…',
      maskClickable: false,
    });

    if (typeof window !== 'undefined') {
      // 只有在客户端环境下才引入 SDK
      const numberAuthSDK = require('aliyun_numberauthsdk_web');
      // 可以在这里初始化 SDK 等操作
      const phoneNumberServerClass = numberAuthSDK.PhoneNumberServer;
      phoneNumberServer = new phoneNumberServerClass();
      // getAuth();

      startTransition(async () => {
        const result = await getAuthTokenDypns();
        console.log('🚀 result:', result);

        if (result.accessToken) {
          phoneNumberServer.checkLoginAvailable({
            accessToken: result.accessToken,
            jwtToken: result.jwtToken,

            success: function (res: any) {
              // 身份鉴权成功,调用 getLoginToken 接口
              // getToken();
              console.log('checkLogin', res);

              phoneNumberServer.getLoginToken({
                success: function (res: any) {
                  // 成功回调，获取spToken
                  // console.log("spToken", res.spToken)
                  // getPhone(res.spToken);

                  startTransition(async () => {
                    const result = await dypnsLogin({
                      spToken: res.spToken,
                      clientId,
                      appType: isiOS ? 'ios' : 'android',
                    });
                    if (result?.error) {
                      Toast.show(result.error);
                    } else {
                      window.jsBridgeHelper?.sendMessage('token', cookies.get('token'));
                      window.jsBridgeHelper?.sendMessage('userInfo', {
                        phone: cookies.get('phone'),
                      });
                    }
                  });
                },

                // 失败回调
                error: function (res: object) {
                  // Toast.show(JSON.stringify(res));
                  Toast.show('关闭Wi-Fi或者尝试其他登录方式');
                  console.log('失败回调', JSON.stringify(res));
                },

                // 授权页状态监听函数 status, data
                // watch: function () { },
                // 配置选项
                authPageOption: {
                  navText: '一键登录认证',
                  subtitle: '', // 副标题
                  isHideLogo: false, // logo是否隐藏
                  // logoImg: "https://www.shanghailima.com/bocstatic/web/img/bocweb-logo.png?v=v19",
                  logoImg: 'https://lima-uat2.oss-cn-hangzhou.aliyuncs.com/logo.png',
                  btnText: '立即登录',
                  agreeSymbol: '、',
                  privacyOne: ['《用户协议》', '/me/about/agreement'],
                  privacyTwo: ['《隐私政策》', '/me/about/privacy?nav=false'],
                  showCustomView: true,
                  privacyBefore: '我已阅读并同意',
                  privacyEnd: '',
                  vendorPrivacyPrefix: '《',
                  vendorPrivacySuffix: '》',
                  privacyVenderIndex: 2,
                  isDialog: false, // 是否是弹窗样式
                  manualClose: false, // 是否手动关闭弹窗/授权页
                  // isPrePageType: false,
                  // isShowProtocolDesc: false,
                  // isShowPreviewPrivacy: false,
                  privacyAlertIsNeedShow: true,
                  // prePageTypeOption: {
                  //   // mount: '',
                  //   privacyOne: ['条款1', 'https://wap.cmpassport.com/resources/html/contract.html'],
                  //   privacyTwo: ['条款2', 'https://wap.cmpassport.com/resources/html/contract.html'],
                  //   // showCustomView: true,
                  //   agreeSymbol: '及',
                  //   // tips: <div style={{ position: 'absolute', top: '10px', right: '10px', borderRadius: '30%', fontSize: '12px', color: '#fff' }}>tips</div>,
                  //   btnText: '',
                  // }

                  // isDialog: true, // 是否是弹窗样式
                  // manualClose: true, // 是否手动关闭弹窗/授权页
                },
              });
            },
            // 身份鉴权失败,提示用户关闭Wi-Fi或者尝试其他登录方案
            error: function (res: any) {
              console.log('checkLogin', res);
              Toast.show('关闭Wi-Fi或者尝试其他登录方案');
            },
          });
        }
      });
    }
  };

  const phoneLogin = () => {
    router.push('/user/verify-login');
  };

  return (
    <Page nav={false}>
      <div className="login_page">
        {/*<video autoPlay muted loop webkit-playsinline playsInline className="backgroundVideo">*/}
        {/*  <source src="/images/limaVideo.mp4" type="video/mp4" />*/}
        {/*</video>*/}
        <Image
          alt="welcome_gif"
          layout="fill"
          src="/images/limaGif.gif"
          className="backgroundVideo"
        />

        <div className="content">
          <div className="logo">
            <div className="logo_image">
              <Image
                src="/images/lima_logo_white.png"
                alt="logo"
                width={50}
                height={50}
                className="object-contain"
              />
            </div>
          </div>
          <div className="title">立马科技</div>
          <div className="subtitle">小微出行全球领导者</div>

          <div className="button">
            <Button
              block
              shape="rounded"
              color="primary"
              className="button_item"
              onClick={() => getToken()}
            >
              一键登录
            </Button>
            <Button
              block
              shape="rounded"
              color="primary"
              className="button_item switch"
              onClick={() => phoneLogin()}
            >
              切换登录方式
            </Button>
          </div>
        </div>

        <Link href="/experience">
          <div data-track="虚拟体验" className="virtualExperience">
            虚拟体验
          </div>
        </Link>
      </div>
    </Page>
  );
}
