.login_page {
  padding: 0 20px;

  .backgroundVideo {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    object-fit: cover;
    z-index: -1;
    filter: brightness(50%);
  }

  .content {
    /* position: relative; */
    /* color: white;
  text-align: center;
  font-size: 2em;
  padding-top: 20vh; */
    color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
  }
  .logo {
    margin-top: 130px;
    margin-bottom: 30px;
  }
  .phone_number {
    font-size: 24px;
  }
  .title {
    font-size: 28px;
    color: #fff;
    text-align: center;
    letter-spacing: 2px;
  }
  .subtitle {
    font-size: 12px;
    color: #5b676d;
    text-align: center;
    letter-spacing: 2px;
  }
  .button {
    display: flex;
    flex-direction: column;
    margin-top: 155px;
    width: 100%;
  }
  .button_item {
    width: 100%;
    margin-bottom: 30px;
    padding: 10px 0;
    border-radius: 15px;
    /* background-color: #fff;
  border-color: #fff;
  color: #000; */

    background-color: #c70e2d;
    border-color: #c70e2d;
    color: #fff;
  }
  .button_item.switch {
    background-color: rgba(89, 98, 103, 0.3);
    color: #fff;
    border-color: rgba(89, 98, 103, 0.3);
  }

  .text-color-text {
    color: #fff;
  }
  .link_text {
    margin-left: 10px;
  }

  .other_login {
    display: flex;
    justify-content: center;
    margin: 20px 0;
  }
  .other_login_item {
    padding: 0 10px;
  }
  .auth_login_text {
    font-size: 12px;
    color: #5b676d;
    text-align: center;
  }

  .line_style::after,
  .line_style::before {
    border: 1px solid #5b676d;
    width: 300px;
  }

  .footer {
    position: fixed;
    width: 100%;
    left: 0;
    right: 0;
    bottom: 50px;
    padding: 0 20px;
  }
  .footer_container {
    width: 100%;
  }

  .virtualExperience {
    color: #94a1a7e6;
    position: fixed;
    right: 20px;
    top: 50px;
    letter-spacing: 1px;
  }
}
