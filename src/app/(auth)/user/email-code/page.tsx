'use client';

import { useState, useEffect, useTransition } from 'react';
import { Toast } from 'antd-mobile';
import Page from '@/components/Page';
import VerificationCodeInput from '@/components/VerificationCodeInput';
import Button from '@/components/Button';
import { useNative } from '@/lib/native';
import { useRouter } from 'next/navigation';
import { emailCodeLogin, getCodeByEmail } from '@/server/actions/user';
import cookies from 'js-cookie';
import './page.scss';
import Image from 'next/image';

export default function EmailCode({ searchParams }: { searchParams: { email: string } }) {
  const [_, startTransition] = useTransition();
  const [isPending, startTransitionSignup] = useTransition();
  const [count, setCount] = useState<number>(60);
  const [timing, setTiming] = useState(false);
  const { email } = searchParams;
  const [code, setCode] = useState('');
  const router = useRouter();
  const { clientId, isiOS } = useNative();

  useEffect(() => {
    const timer = setTimeout(() => {
      handleReGetCode();
    }, 100);
    return () => {
      clearTimeout(timer);
    };
  }, []);

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (timing) {
      interval = setInterval(() => {
        setCount((preCount) => {
          if (preCount <= 1) {
            setTiming(false);
            clearInterval(interval);
            return 60;
          }
          return preCount - 1;
        });
      }, 1000);
    }
    return () => {
      clearInterval(interval);
    };
  }, [timing]);

  // 获取验证码
  const handleReGetCode = () => {
    startTransition(async () => {
      // 此处报错为后端接口未实现
      console.log(email);
      const result = await getCodeByEmail({ email });

      console.log('[ result ] >', result);
      if (result?.error) {
        Toast.show(result.error);
      } else {
        setTiming(true);
        Toast.show('验证码已经发送');
      }
    });
  };

  // 登录
  const handleLogin = () => {
    startTransitionSignup(async () => {
      // 通过邮箱和验证码确认是否注册
      console.log('emailCodeLogin', email, code);
      const result = await emailCodeLogin({
        email: email,
        emailCode: code,
        clientId: clientId || 'adebff4108e552fab03138f0c528d0fd',
        appType: isiOS ? 'ios' : 'android',
      });
      if (result.error) {
        const { error } = result;
        console.log('error', error);
        if (error === '用户不存在') {
          Toast.show('邮箱未绑定手机号，请先进行绑定');
          // 跳转到注册页面
          setTimeout(() => {
            router.push(`/user/bind-phone?email=${email}`);
          }, 1000);
        } else {
          Toast.show(error || '登录失败');
        }
      }
      window.jsBridgeHelper?.sendMessage('token', cookies.get('token'));
      window.jsBridgeHelper?.sendMessage('userInfo', { phone: cookies.get('phone'), email: cookies.get('email') });
    });
  };

  return (
    <Page title="邮箱验证码登录" showTitle={false}>
      <div className="code_login_container">

        <Image
          alt="welcome_gif"
          layout="fill"
          src="/images/limaGif.gif"
          className="backgroundVideo"
        />

        <div className="p-4 text-white">
          <div className="title">邮箱验证码登录</div>
          <div className="mt-3">
            已发送6位验证码至 &nbsp;
            <span className="text-gray-300 underline underline-offset-4">{email}</span>
          </div>
          <div className="mt-8 flex justify-center text-black">
            <VerificationCodeInput value={code} onChange={(value) => setCode(value)} />
          </div>
          <div className="mb-4 mt-3 flex justify-between text-xs">
            <div>
              收不到验证码？
              {timing ? (
                <span className="text-color-weak">重新获取（{count} s）</span>
              ) : (
                <span className="text-primary" onClick={handleReGetCode}>
                  重新获取
                </span>
              )}
            </div>
          </div>
          <Button
            className="bg-custom-gray mt-5"
            block
            type="primary"
            disabled={code.length !== 6}
            loading={isPending}
            onClick={handleLogin}
          >
            确定
          </Button>
        </div>
      </div>
    </Page>
  );
}
