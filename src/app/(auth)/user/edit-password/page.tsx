'use client';

import { useState, useTransition } from 'react';
import { Form, Input, Toast } from 'antd-mobile';
import { EyeInvisibleOutline, EyeOutline } from 'antd-mobile-icons';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { PASSWORDREGEX, encryptPassword } from '@/utils';
import { changePassword } from '@/server/actions/user';
import { useLogout } from '@/hooks/useLogout';
import { useQueryClient } from '@tanstack/react-query';

export default function Forgot({ searchParams }: { searchParams: { phone: string } }) {
  const [form] = Form.useForm();
  const [_, startTransition] = useTransition();
  const [isPending, startTransitionSignup] = useTransition();
  const [oldPwdVisible, setOldPwdVisible] = useState(false);
  const [pwdVisible, setPwdVisible] = useState(false);
  const [confirmPwdVisible, setConfirmPwdVisible] = useState(false);
  const { phone } = searchParams;
  const queryClient = useQueryClient();
  const logout = useLogout();

  const onFinish = (values: any) => {
    const { oldPassword, password, confirmPassword } = values;
    if (password !== confirmPassword) {
      return Toast.show('新密码两次输入不一致');
    }
    startTransitionSignup(async () => {
      const result = await changePassword({
        phone,
        oldPassword: encryptPassword(oldPassword),
        password: encryptPassword(password),
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        queryClient.clear();
        logout();
      }
    });
  };

  return (
    <Page title="修改密码">
      <div className="p-4">
        <div className="text-4xl font-medium">修改密码</div>
        <div className="mt-3">密码为6-26位字母或数字组合</div>
      </div>
      <div className="px-1">
        <Form
          form={form}
          layout="horizontal"
          mode="card"
          onFinish={onFinish}
          footer={
            <Form.Subscribe to={['oldPassword', 'password', 'confirmPassword']}>
              {({ oldPassword, password, confirmPassword }) => {
                return (
                  <Button
                    type="primary"
                    block
                    htmlType="submit"
                    loading={isPending}
                    disabled={
                      !(
                        oldPassword &&
                        PASSWORDREGEX.test(oldPassword) &&
                        password &&
                        PASSWORDREGEX.test(password) &&
                        confirmPassword &&
                        PASSWORDREGEX.test(confirmPassword)
                      )
                    }
                  >
                    确定
                  </Button>
                );
              }}
            </Form.Subscribe>
          }
        >
          <Form.Item
            label="原密码"
            name="oldPassword"
            extra={
              oldPwdVisible ? (
                <EyeOutline onClick={() => setOldPwdVisible(false)} />
              ) : (
                <EyeInvisibleOutline onClick={() => setOldPwdVisible(true)} />
              )
            }
          >
            <Input
              placeholder="6-26位数字或字母"
              type={oldPwdVisible ? 'text' : 'password'}
              autoComplete="new-password"
            />
          </Form.Item>

          <Form.Header />
          <Form.Item
            label="新密码"
            name="password"
            extra={
              pwdVisible ? (
                <EyeOutline onClick={() => setPwdVisible(false)} />
              ) : (
                <EyeInvisibleOutline onClick={() => setPwdVisible(true)} />
              )
            }
          >
            <Input
              placeholder="6-26位数字或字母"
              type={pwdVisible ? 'text' : 'password'}
              autoComplete="new-password"
            />
          </Form.Item>
          <Form.Item
            label="确认密码"
            name="confirmPassword"
            extra={
              confirmPwdVisible ? (
                <EyeOutline onClick={() => setConfirmPwdVisible(false)} />
              ) : (
                <EyeInvisibleOutline onClick={() => setConfirmPwdVisible(true)} />
              )
            }
          >
            <Input
              placeholder="请确保两次输入一致"
              autoComplete="new-password"
              type={confirmPwdVisible ? 'text' : 'password'}
            />
          </Form.Item>
          <Form.Header />
        </Form>
      </div>
    </Page>
  );
}
