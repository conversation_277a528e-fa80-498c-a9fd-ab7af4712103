'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState, useEffect, useTransition } from 'react';
import { Toast } from 'antd-mobile';
import Page from '@/components/Page';
import VerificationCodeInput from '@/components/VerificationCodeInput';
import Button from '@/components/Button';
import { changePhoneForCheck, getCodeByPhone } from '@/server/actions/user';
import { encryptCode } from '@/utils';

export default function EditPhone({ searchParams }: { searchParams: { phone: string } }) {
  const router = useRouter();

  const [_, startTransition] = useTransition();
  const [isPending, startTransitionSignup] = useTransition();
  const [count, setCount] = useState<number>(60);
  const [timing, setTiming] = useState(false);
  const { phone } = searchParams;
  const [code, setCode] = useState('');

  useEffect(() => {
    const timer = setTimeout(() => {
      if (phone) handleReGetCode();
    }, 100);
    return () => {
      clearTimeout(timer);
    };
  }, []);

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (timing) {
      interval = setInterval(() => {
        setCount((preCount) => {
          if (preCount <= 1) {
            setTiming(false);
            clearInterval(interval);
            return 60;
          }
          return preCount - 1;
        });
      }, 1000);
    }
    return () => {
      clearInterval(interval);
    };
  }, [timing]);

  // 获取验证码
  const handleReGetCode = () => {
    startTransition(async () => {
      const result = await getCodeByPhone({ phone, sign: encryptCode(phone) });
      console.log('[ result ] >', result);
      if (result?.error) {
        Toast.show(result.error);
      } else {
        setTiming(true);
        Toast.show('验证码已经发送');
      }
    });
  };

  // 确定
  const handleLogin = () => {
    startTransitionSignup(async () => {
      const result = await changePhoneForCheck({
        phone,
        code,
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        router.push(`/user/edit-phone-3?oldPhone=${phone}`);
      }
    });
  };

  return (
    <Page>
      <div className="p-4">
        <div className="text-4xl font-medium">验证原手机号</div>
        <div className="mt-3">
          已发送6位验证码至<span className="text-color-weak">{phone}</span>
        </div>
        <div className="mt-8 flex justify-center">
          <VerificationCodeInput value={code} onChange={(value) => setCode(value)} />
        </div>
        <div className="mb-4 mt-3 flex justify-between text-xs">
          <div>
            收不到验证码？
            {timing ? (
              <span className="text-color-weak">重新获取（{count} s）</span>
            ) : (
              <span className="text-primary" onClick={handleReGetCode}>
                重新获取
              </span>
            )}
          </div>
          <Link href={`/user/edit-phone-2?oldPhone=${phone}`} replace className="text-primary">
            手机号丢失或停用
          </Link>
        </div>
        <Button
          block
          type="primary"
          disabled={code.length !== 6}
          loading={isPending}
          onClick={handleLogin}
        >
          下一步
        </Button>
      </div>
    </Page>
  );
}
