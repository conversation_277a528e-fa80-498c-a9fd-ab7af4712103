'use client';

import { useState, useEffect, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { Form, Input, Toast } from 'antd-mobile';
import { EyeInvisibleOutline, EyeOutline } from 'antd-mobile-icons';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { PHONEREGEX, PASSWORDREGEX, encryptCode, encryptPassword } from '@/utils';
import { getCodeByPhone, passwordForget } from '@/server/actions/user';

export default function Forgot({ searchParams }: { searchParams: { phone: string } }) {
  const router = useRouter();
  const [count, setCount] = useState<number>(60);
  const [timing, setTiming] = useState(false);
  const [form] = Form.useForm();
  const [_, startTransition] = useTransition();
  const [isPending, startTransitionSignup] = useTransition();
  const [pwdVisible, setPwdVisible] = useState(false);
  const [confirmPwdVisible, setConfirmPwdVisible] = useState(false);

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (timing) {
      interval = setInterval(() => {
        setCount((preCount) => {
          if (preCount <= 1) {
            setTiming(false);
            clearInterval(interval);
            return 60;
          }
          return preCount - 1;
        });
      }, 1000);
    }
    return () => {
      clearInterval(interval);
    };
  }, [timing]);

  // 获取验证码
  const getCode = () => {
    const { phone } = form.getFieldsValue();
    if (!PHONEREGEX.test(phone)) {
      return Toast.show('手机号格式不正确');
    }
    startTransition(async () => {
      const result = await getCodeByPhone({ phone, sign: encryptCode(phone) });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        setTiming(true);
        Toast.show(' 验证码已经发送');
      }
    });
  };

  const onFinish = (values: any) => {
    const { phone, code, password, confirmPassword } = values;
    if (password !== confirmPassword) {
      return Toast.show('两次密码输入不一致');
    }
    startTransitionSignup(async () => {
      const result = await passwordForget({
        phone,
        code,
        password: encryptPassword(password),
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        router.back();
      }
    });
  };

  return (
    <Page title="忘记密码">
      <div className="px-1">
        <Form
          form={form}
          layout="horizontal"
          mode="card"
          initialValues={{ phone: searchParams.phone }}
          onFinish={onFinish}
          footer={
            <Form.Subscribe to={['phone', 'code', 'password', 'confirmPassword']}>
              {({ phone, code, password, confirmPassword }) => {
                return (
                  <Button
                    type="primary"
                    block
                    htmlType="submit"
                    loading={isPending}
                    disabled={
                      !(
                        PHONEREGEX.test(phone) &&
                        code &&
                        password &&
                        PASSWORDREGEX.test(password) &&
                        confirmPassword &&
                        PASSWORDREGEX.test(confirmPassword)
                      )
                    }
                  >
                    确定
                  </Button>
                );
              }}
            </Form.Subscribe>
          }
        >
          <Form.Item label="手机号" name="phone">
            <Input placeholder="请输入手机号" />
          </Form.Item>
          <Form.Item
            label="验证码"
            name="code"
            extra={
              timing ? (
                <span className="text-color-weak">{count}秒后重发</span>
              ) : (
                <span className="text-primary" onClick={getCode}>
                  获取验证码
                </span>
              )
            }
          >
            <Input placeholder="请输入验证码" />
          </Form.Item>
          <Form.Header />
          <Form.Item
            label="密码"
            name="password"
            extra={
              pwdVisible ? (
                <EyeOutline onClick={() => setPwdVisible(false)} />
              ) : (
                <EyeInvisibleOutline onClick={() => setPwdVisible(true)} />
              )
            }
          >
            <Input
              placeholder="6-26位数字或字母"
              type={pwdVisible ? 'text' : 'password'}
              autoComplete="new-password"
            />
          </Form.Item>
          <Form.Item
            label="确认密码"
            name="confirmPassword"
            extra={
              confirmPwdVisible ? (
                <EyeOutline onClick={() => setConfirmPwdVisible(false)} />
              ) : (
                <EyeInvisibleOutline onClick={() => setConfirmPwdVisible(true)} />
              )
            }
          >
            <Input
              placeholder="请确保两次输入一致"
              autoComplete="new-password"
              type={confirmPwdVisible ? 'text' : 'password'}
            />
          </Form.Item>
          <Form.Header />
        </Form>
      </div>
    </Page>
  );
}
