'use client';

import { useState, useTransition } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { Input, Checkbox, Toast } from 'antd-mobile';
import { CheckCircleFill } from 'antd-mobile-icons';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { useNative } from '@/lib/native';
import { PHONEREGEX } from '@/utils';
import { isRegister, thirdLogin } from '@/server/actions/user';
import cookies from 'js-cookie';

export default function Account({ searchParams }: { searchParams: { phone: string } }) {
  const router = useRouter();
  const pathname = usePathname();
  const [isPending, startTransition] = useTransition();
  const [phone, setPhone] = useState(searchParams.phone || '');
  const [checked, setChecked] = useState(false);
  const { clientId, isiOS, hasWechat } = useNative();

  const handleLogin = (loginType: string, openidKey: string) => {
    if (!checked) {
      return Toast.show('请阅读并同意用户协议和隐私政策');
    }
    window.jsBridgeHelper?.sendMessage(loginType).then((res: any) => {
      if (!res) return;
      // 判断是否注册
      startTransition(async () => {
        const result = await isRegister({ [openidKey]: res[openidKey] });
        if (typeof result === 'boolean') {
          if (result) {
            // 已注册进行登录
            const loginResult = await thirdLogin({
              clientId,
              [openidKey]: res[openidKey],
              clientType: isiOS ? 'ios' : 'android',
            });
            if (loginResult?.error) {
              Toast.show(loginResult.error);
            } else {
              window.jsBridgeHelper?.sendMessage('token', cookies.get('token'));
              window.jsBridgeHelper?.sendMessage('userInfo', { phone: cookies.get('phone') });
            }
          } else {
            const params = JSON.stringify({
              openid: res[openidKey],
              nickname: res.nickname,
              thirdType: res.type,
              unionid: res.wxUnionid || '',
              img: res.img || '',
            });
            // 未注册跳转到绑定手机号页面
            router.push(`/user/bind-phone?params=${params}`);
          }
        } else if (result.error) {
          Toast.show(result.error);
        }
      });
    });
  };

  return (
    <Page>
      <div className="  flex flex-col bg-[#EEF1F4] p-4" style={{ paddingBottom: 40, zIndex: 9 }}>
        <div className="text-4xl font-medium">你好！</div>
        <div className="mt-3">欢迎使用立马科技</div>
        <div className="mb-4 mt-8 rounded-lg bg-white px-4 py-3">
          <Input
            placeholder="请输入手机号"
            inputMode="numeric"
            maxLength={11}
            value={phone}
            onChange={(value) => {
              const updatedSearchParams = new URLSearchParams(searchParams);
              if (value) {
                updatedSearchParams.set('phone', value);
              } else {
                updatedSearchParams.delete('phone');
              }
              startTransition(() => {
                router.replace(`${pathname}?${updatedSearchParams.toString()}`);
              });
              setPhone(value);
            }}
          />
        </div>
        <Button
          block
          type="primary"
          disabled={!PHONEREGEX.test(phone)}
          onClick={() => {
            if (!checked) {
              return Toast.show('请阅读并同意用户协议和隐私政策');
            }
            router.push(`/user/password?phone=${phone}`);
          }}
        >
          下一步
        </Button>
        <div className="flex-1" />
      </div>
      <div className="fixed bottom-[env(safe-area-inset-bottom)] left-4 right-4">
        <div className="flex flex-col items-center">
          <div className="mb-8 flex space-x-8">
            {hasWechat && (
              <Image
                src="/images/icon-wx.png"
                alt="wx"
                width={48}
                height={48}
                onClick={() => handleLogin('wxLogin', 'wxOpenid')}
              />
            )}
            {isiOS && (
              <Image
                src="/images/icon-apple.png"
                alt="apple"
                width={48}
                height={48}
                onClick={() => handleLogin('appleLogin', 'appleOpenid')}
              />
            )}
          </div>
        </div>
        <div className="mb-8 flex items-center text-sm text-color-weak">
          <Checkbox
            icon={(checked) => {
              return checked ? (
                <CheckCircleFill color="#585C78" fontSize={22} />
              ) : (
                <div className="h-[22px] w-[22px]  rounded-full border border-color-light" />
              );
            }}
            checked={checked}
            onChange={(value) => setChecked(value)}
          />
          &nbsp;我已阅读同意
          <Link href="/me/about/agreement" className="text-color-text">
            《<span className="underline decoration-1">用户协议</span>》
          </Link>
          和
          <Link href="/me/about/privacy" className="text-color-text">
            《<span className="underline decoration-1">隐私政策</span>》
          </Link>
        </div>
      </div>
    </Page>
  );
}
