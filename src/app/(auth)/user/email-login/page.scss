.verify_login_page {
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  // min-height: 100vh;
  // position: relative;
  overflow: hidden;

  .backgroundVideo {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    object-fit: cover;
    z-index: -1;
    filter: brightness(50%);
  }

  .content {
    color: #fff;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    flex: 1;
    padding-bottom: 40px;
  }
  .title {
    font-size: 22px;
    text-align: left;
    letter-spacing: 2px;
    margin-top: 80px;
    margin-bottom: 10px;
  }
  .login_way {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    font-weight: 600;
    align-items: center;
  }
  .login_way .line {
    margin: 0 40px;
    font-weight: 0;
    font-size: 10px;
  }
  .login_way_active {
    color: #3867c3;
  }
  .button {
    display: flex;
    flex-direction: column;
    margin-top: 10px;
    width: 100%;
  }
  .button_item {
    width: 100%;
    margin-bottom: 20px;
    padding: 10px 0;
    border-radius: 15px;
    background-color: #fff;
    color: #000;
    border-color: #fff;
  }
  .button_item.switch {
    background-color: rgba(89, 98, 103, 0.6);
    color: #fff;
    border-color: rgba(89, 98, 103, 0.6);
  }

  .form {
    margin-top: 30px;
    color: #fff;
    width: 100%;
  }
  .form_input {
    color: #fff;
    letter-spacing: 4px;
    border-bottom: 1px solid rgba(89, 98, 103, 0.894);
    margin-bottom: 20px;
    padding-bottom: 10px;
    input {
      color: #fff;
      letter-spacing: 2px;
    }
    input::placeholder {
      color: rgba(174, 186, 192, 0.894);
    }
  }

  .password {
    position: relative;
    .eye {
      position: absolute;
      right: 10px;
      bottom: 0px;
    }
  }

  .text-color-text {
    color: #fff;
  }
  .link_text {
    margin-left: 10px;
  }

  .other_login {
    display: flex;
    justify-content: center;
    margin: 20px 0;
  }
  .other_login_item {
    padding: 0 10px;
  }
  .auth_login_text {
    font-size: 12px;
    color: #5b676d;
  }
  .line_style {
    margin-top: 100px;
  }
  .line_style::after,
  .line_style::before {
    border: 1px solid #5b676d;
    width: 300px;
  }

  .footer {
    position: relative;
    width: 100%;
    padding: 0;
    margin-top: 70px;
    margin-bottom: 30px;
  }
  .footer_container {
    width: 100%;
  }

  .registerBtn {
    // color: rgb(89 98 103);
    color: #fff;
    font-size: 14px;
    text-align: right;
  }

  @media (max-height: 600px) {
    .title {
      margin-top: 30px;
    }

    .form {
      margin-top: 20px;
    }

    .form_input {
      margin-bottom: 15px;
    }

    .button {
      margin-top: 5px;
    }

    .line_style {
      margin-top: 10px;
    }

    .footer {
      margin-bottom: 10px;
    }
  }
}
