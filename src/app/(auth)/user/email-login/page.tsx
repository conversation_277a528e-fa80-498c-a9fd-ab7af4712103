'use client';

import './page.scss';
import { Button, Divider, Input, Toast, Checkbox } from 'antd-mobile';
import { CheckCircleFill } from 'antd-mobile-icons';
import { useEffect, useState, useTransition } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import Page from '@/components/Page';
import Link from 'next/link';

import Image from 'next/image';
import { useNative } from '@/lib/native';
import { isRegister, thirdLogin, } from '@/server/actions/user';
import cookies from 'js-cookie';
import { EMAILREGEX } from '@/utils';

export default function EmailLogin() {
    const { clientId, isiOS, hasWechat } = useNative();
    const router = useRouter();
    const pathname = usePathname();
    const [isPending, startTransition] = useTransition();
    // const [phone, setPhone] = useState(searchParams.phone || '');

    const [checked, setChecked] = useState(false);
    const [userEmail, setUserEmail] = useState('');

    useEffect(() => {
        const handleResize = () => {
          window.scrollTo({
            top: window.scrollY,
            behavior: 'smooth'
          });
        };
    
        window.addEventListener('resize', handleResize);
        
        return () => {
          window.removeEventListener('resize', handleResize);
        };
      }, []);

    const handleLogin = (loginType: string, openidKey: string) => {
        if (!checked) {
            Toast.show('请阅读并同意用户协议和隐私政策');
            return;
        }
        window.jsBridgeHelper?.sendMessage(loginType).then((res: any) => {
            if (!res) return;
            // 判断是否注册
            startTransition(async () => {
                const result = await isRegister({ [openidKey]: res[openidKey] });
                if (typeof result === 'boolean') {
                    if (result) {
                        // 已注册进行登录
                        const loginResult = await thirdLogin({
                            clientId,
                            [openidKey]: res[openidKey],
                            clientType: isiOS ? 'ios' : 'android',
                        });
                        if (loginResult?.error) {
                            Toast.show(loginResult.error);
                        } else {
                            window.jsBridgeHelper?.sendMessage('token', cookies.get('token'));
                            window.jsBridgeHelper?.sendMessage('userInfo', { phone: cookies.get('phone') });
                        }
                    } else {
                        const params = JSON.stringify({
                            openid: res[openidKey],
                            nickname: res.nickname,
                            thirdType: res.type,
                            unionid: res.wxUnionid || '',
                            img: res.img || '',
                        });
                        // 未注册跳转到绑定手机号页面
                        router.push(`/user/bind-phone?params=${params}`);
                    }
                } else if (result.error) {
                    Toast.show(result.error);
                }
            });
        });
    };

    // 跳转邮箱验证码登录
    const verificationCode = () => {
        if (userEmail == '' || userEmail == null) {
            Toast.show('邮箱不能为空');
            return;
        }
        if (!checked) {
            Toast.show('请阅读并同意用户协议和隐私政策');
            return;
          }
        router.push(`/user/email-code?email=${userEmail}`);
    };

    return (
        <Page nav={true} title="验证码登录||密码登录" showTitle={false}>
            <div className="verify_login_page">
                <Image
                    alt="welcome_gif"
                    layout="fill"
                    src="/images/limaGif.gif"
                    className="backgroundVideo"
                />

                <div className="content">
                    <div className="title">欢迎登录立马科技</div>
                    <div className="form">
                        <Input
                            placeholder="请输入邮箱"
                            value={userEmail}
                            onChange={(val) => {
                                setUserEmail(val);
                            }}
                            className="form_input"
                            type="email"
                        />
                    </div>

                    <div className="button">
                        <Button
                            block
                            shape="rounded"
                            color="primary"
                            className="button_item switch"
                            onClick={() => verificationCode()}
                            disabled={!EMAILREGEX.test(userEmail)}
                        >
                            获取邮箱验证码
                        </Button>
                    </div>

                    <div className="mb-8  flex  h-[30px] items-center text-[12px] text-color-weak">
                        <Checkbox
                            icon={(checked) => {
                                return checked ? (
                                    <CheckCircleFill color="#FFFFFFFF" fontSize={22} />
                                ) : (
                                    <div className="h-[22px] w-[22px]  rounded-full border border-color-light " />
                                );
                            }}
                            checked={checked}
                            onChange={(value) => setChecked(value)}
                        />
                        <div className="link_text">
                            我已阅读并同意
                            <Link href="/me/about/agreement" className="text-color-text">
                                《<span className="underline decoration-1">用户协议</span>》
                            </Link>
                            和
                            <Link href="/me/about/privacy" className="text-color-text">
                                《<span className="underline decoration-1">隐私政策</span>》
                            </Link>
                        </div>
                    </div>

                    <div className="footer">
                        <div className="footer_container">
                            <Divider className="line_style">其他登录方式</Divider>
                            <div className="other_login">
                                {hasWechat && (
                                    <div className="other_login_item">
                                        <Image
                                            src="/images/icon-wx.png"
                                            alt="wx"
                                            width={48}
                                            height={48}
                                            onClick={() => handleLogin('wxLogin', 'wxOpenid')}
                                        />
                                    </div>
                                )}
                                {isiOS && (
                                    <div className="other_login_item">
                                        <Image
                                            src="/images/icon-apple.png"
                                            alt="apple"
                                            width={48}
                                            height={48}
                                            onClick={() => handleLogin('appleLogin', 'appleOpenid')}
                                        />
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Page>
    );
}
