'use client';

import Link from 'next/link';
import { useState, useEffect, useTransition } from 'react';
import { Toast } from 'antd-mobile';
import Page from '@/components/Page';
import VerificationCodeInput from '@/components/VerificationCodeInput';
import Button from '@/components/Button';
import { useNative } from '@/lib/native';
import { codeLogin, getCodeByPhone } from '@/server/actions/user';
import { encryptCode } from '@/utils';
import cookies from 'js-cookie';
import './page.scss';
import Image from 'next/image';

export default function Code({ searchParams }: { searchParams: { phone: string } }) {
  const [_, startTransition] = useTransition();
  const [isPending, startTransitionSignup] = useTransition();
  const [count, setCount] = useState<number>(60);
  const [timing, setTiming] = useState(false);
  const { phone } = searchParams;
  const [code, setCode] = useState('');
  const { clientId, isiOS } = useNative();

  useEffect(() => {
    const timer = setTimeout(() => {
      handleReGetCode();
    }, 100);
    return () => {
      clearTimeout(timer);
    };
  }, []);

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (timing) {
      interval = setInterval(() => {
        setCount((preCount) => {
          if (preCount <= 1) {
            setTiming(false);
            clearInterval(interval);
            return 60;
          }
          return preCount - 1;
        });
      }, 1000);
    }
    return () => {
      clearInterval(interval);
    };
  }, [timing]);

  // 获取验证码
  const handleReGetCode = () => {
    startTransition(async () => {
      const result = await getCodeByPhone({ phone, sign: encryptCode(phone) });
      console.log('[ result ] >', result);
      if (result?.error) {
        Toast.show(result.error);
      } else {
        setTiming(true);
        Toast.show('验证码已经发送');
      }
    });
  };

  // 登录
  const handleLogin = () => {
    startTransitionSignup(async () => {
      const result = await codeLogin({
        phone,
        code,
        clientId: clientId || 'adebff4108e552fab03138f0c528d0fd',
        appType: isiOS ? 'ios' : 'android',
      });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        window.jsBridgeHelper?.sendMessage('token', cookies.get('token'));
        window.jsBridgeHelper?.sendMessage('userInfo', { phone: cookies.get('phone') });
      }
    });
  };

  return (
    <Page title="验证码登录" showTitle={false}>
      <div className="code_login_container">
        {/*<video autoPlay muted loop webkit-playsinline playsInline className="backgroundVideo">*/}
        {/*  <source src="/images/limaVideo.mp4" type="video/mp4" />*/}
        {/*</video>*/}
        <Image
          alt="welcome_gif"
          layout="fill"
          src="/images/limaGif.gif"
          className="backgroundVideo"
        />

        <div className="p-4 text-white">
          <div className="title">验证码登录</div>
          <div className="mt-3">
            已发送6位验证码至 &nbsp;
            <span className="text-gray-300 underline underline-offset-4">{phone}</span>
          </div>
          <div className="mt-8 flex justify-center text-black">
            <VerificationCodeInput value={code} onChange={(value) => setCode(value)} />
          </div>
          <div className="mb-4 mt-3 flex justify-between text-xs">
            <div>
              收不到验证码？
              {timing ? (
                <span className="text-color-weak">重新获取（{count} s）</span>
              ) : (
                <span className="text-primary" onClick={handleReGetCode}>
                  重新获取
                </span>
              )}
            </div>
            {/* <Link href={`/user/password?phone=${phone}`} replace className="text-primary">
            密码登录
          </Link> */}
            <Link
              href={`/user/verify-login?phone=${phone}&loginType=passwordLogin`}
              replace
              className="text-white"
            >
              密码登录
            </Link>
          </div>
          <Button
            className="bg-custom-gray mt-5"
            block
            type="primary"
            disabled={code.length !== 6}
            loading={isPending}
            onClick={handleLogin}
          >
            确定
          </Button>
        </div>
      </div>
    </Page>
  );
}
