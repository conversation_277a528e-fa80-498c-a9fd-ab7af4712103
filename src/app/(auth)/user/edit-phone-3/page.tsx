'use client';

import { useState, useEffect, useTransition } from 'react';
import { Input, Toast, Form } from 'antd-mobile';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { PHONEREGEX, encryptCode } from '@/utils';
import { changePhone, getCodeByPhone } from '@/server/actions/user';
import { useLogout } from '@/hooks/useLogout';
import { useQueryClient } from '@tanstack/react-query';

export default function EditPhone3({ searchParams }: { searchParams: { oldPhone: string } }) {
  const [_, startTransition] = useTransition();
  const [isPending, startTransitionSignup] = useTransition();
  const [count, setCount] = useState<number>(60);
  const [timing, setTiming] = useState(false);
  const { oldPhone } = searchParams;
  const [form] = Form.useForm();
  const phone = Form.useWatch('phone', form);
  const queryClient = useQueryClient();
  const logout = useLogout();

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (timing) {
      interval = setInterval(() => {
        setCount((preCount) => {
          if (preCount <= 1) {
            setTiming(false);
            clearInterval(interval);
            return 60;
          }
          return preCount - 1;
        });
      }, 1000);
    }
    return () => {
      clearInterval(interval);
    };
  }, [timing]);

  // 获取验证码
  const handleGetCode = () => {
    if (!PHONEREGEX.test(phone)) {
      return Toast.show('手机号格式不正确');
    }
    startTransition(async () => {
      const result = await getCodeByPhone({ phone, sign: encryptCode(phone) });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        setTiming(true);
        Toast.show('验证码已发送');
      }
    });
  };

  const handleSubmit = (values: any) => {
    startTransitionSignup(async () => {
      const result = await changePhone({
        phone: values.phone,
        code: values.code,
        oldPhone,
      });
      console.log('[ result ] >', result);
      if (result?.error) {
        Toast.show(result.error);
      } else {
        queryClient.clear();
        logout();
      }
    });
  };

  return (
    <Page>
      <div className="flex flex-col p-4">
        <div className="text-4xl font-medium">绑定新手机号</div>
      </div>
      <Form
        form={form}
        layout="horizontal"
        mode="card"
        requiredMarkStyle="none"
        onFinish={handleSubmit}
        footer={
          <Form.Subscribe to={['phone', 'code']}>
            {({ phone = '', code = '' }) => {
              return (
                <Button
                  type="primary"
                  block
                  htmlType="submit"
                  loading={isPending}
                  disabled={!(PHONEREGEX.test(phone) && code.length === 6)}
                >
                  确定
                </Button>
              );
            }}
          </Form.Subscribe>
        }
      >
        <Form.Item label="手机号" name="phone">
          <Input placeholder="请输入手机号" inputMode="numeric" maxLength={11} />
        </Form.Item>
        <Form.Header />
        <Form.Item
          label="验证码"
          name="code"
          extra={
            timing ? (
              <span className="text-color-weak">{count}秒后重发</span>
            ) : (
              <span className="text-primary" onClick={handleGetCode}>
                获取验证码
              </span>
            )
          }
        >
          <Input placeholder="请输入验证码" inputMode="numeric" maxLength={6} />
        </Form.Item>
      </Form>
    </Page>
  );
}
