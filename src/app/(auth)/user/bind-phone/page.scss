.bind_phone_container {
  padding: 0 20px;
  height: 200vh;

  .title {
    font-size: 22px;
    text-align: left;
    letter-spacing: 2px;
    margin-top: 80px;
  }

  .backgroundVideo {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    object-fit: cover;
    z-index: -1;
    filter: brightness(50%);
  }

  .bg-custom-gray {
    background: rgba(89, 98, 103, 0.6);
  }

  .form {
    margin-top: 30px;
    color: #fff;
    width: 100%;

    div {
      background: none;
    }

    .form_input {
      font-size: 14px;
      color: #fff;
      letter-spacing: 4px;
      border-bottom: 1px solid rgba(89, 98, 103, 0.894);
      margin-bottom: 20px;
      padding-bottom: 10px;
      input {
        color: #fff;
        letter-spacing: 2px;
        font-size: 16px;
      }
      input::placeholder {
        color: rgba(174, 186, 192, 0.894);
      }
    }
  }

  .read_text {
    font-size: 12px;
  }
}

