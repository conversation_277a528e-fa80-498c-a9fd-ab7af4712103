'use client';

import { useState, useEffect, useTransition } from 'react';
import { Input, Toast, Form } from 'antd-mobile';
import Page from '@/components/Page';
import Button from '@/components/Button';
import { PHONEREGEX, encryptCode } from '@/utils';
import { getCodeByPhone, thirdRegister, emailRegister } from '@/server/actions/user';
import cookies from 'js-cookie';
import { useNative } from '@/lib/native';
import './page.scss';
import Image from 'next/image';

export default function BindPhone({
  searchParams,
}: {
  searchParams: { params: string | undefined, email: string | undefined };
}) {
  const [_, startTransition] = useTransition();
  const [isPending, startTransitionSignup] = useTransition();
  const [count, setCount] = useState<number>(60);
  const [timing, setTiming] = useState(false);
  const [form] = Form.useForm();
  const phone = Form.useWatch('phone', form);
  const { clientId, isiOS } = useNative();

  const { params, email } = searchParams;

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (timing) {
      interval = setInterval(() => {
        setCount((preCount) => {
          if (preCount <= 1) {
            setTiming(false);
            clearInterval(interval);
            return 60;
          }
          return preCount - 1;
        });
      }, 1000);
    }
    return () => {
      clearInterval(interval);
    };
  }, [timing]);

  // 获取验证码
  const handleGetCode = () => {
    if (!PHONEREGEX.test(phone)) {
      return Toast.show('手机号格式不正确');
    }
    startTransition(async () => {
      const result = await getCodeByPhone({ phone, sign: encryptCode(phone) });
      if (result?.error) {
        Toast.show(result.error);
      } else {
        setTiming(true);
        Toast.show('验证码已发送');
      }
    });
  };

  const handleSubmit = (values: any) => {
    const parseParams = JSON.parse(params || '{}');
    startTransitionSignup(async () => {
      if (email === undefined || email === '') {
        const result = await thirdRegister({
          phone: values.phone,
          code: values.code,
          type: 0, // 普通用户
          ...parseParams,
        });
        if (result?.error) {
          Toast.show(result.error);
        } else {
          window.jsBridgeHelper?.sendMessage('token', cookies.get('token'));
          window.jsBridgeHelper?.sendMessage('userInfo', { phone: cookies.get('phone') });
        }
      } else {
        const result = await emailRegister({
          phone: values.phone,
          phoneCode: values.code,
          email: email,
          clientId: clientId || 'adebff4108e552fab03138f0c528d0fd',
          appType: isiOS ? 'ios' : 'android',
        });
        if (result?.error) {
          Toast.show(result.error);
        } else {
          window.jsBridgeHelper?.sendMessage('token', cookies.get('token'));
          window.jsBridgeHelper?.sendMessage('userInfo', { phone: cookies.get('phone'), email: cookies.get('email') });
        }
      }
    });
  };

  return (
    <Page title="绑定手机号" showTitle={false}>
      <div className="bind_phone_container">
        <Image
          alt="welcome_gif"
          layout="fill"
          src="/images/limaGif.gif"
          className="backgroundVideo"
        />
        <div className="px-1 text-white ">
          <div className="title">绑定手机号</div>
          <div className="mt-3">
            根据国家互联网法规，进行下一步操作需进行手机号认证，感谢你的理解及支持！
          </div>
          <Form
            form={form}
            layout="horizontal"
            mode="card"
            requiredMarkStyle="none"
            footer={
              <Form.Subscribe to={['phone', 'code']}>
                {({ phone, code }) => {
                  return (
                    <Button
                      type="primary"
                      className="bg-custom-gray mt-5"
                      block
                      htmlType="submit"
                      loading={isPending}
                      disabled={
                        !(
                          PHONEREGEX.test(phone) && code
                        )
                      }
                    >
                      确定
                    </Button>
                  );
                }}
              </Form.Subscribe>
            }
            onFinish={handleSubmit}
          >
            <div className="form">
              <Form.Item name="phone">
                <Input
                  placeholder="请输入手机号"
                  inputMode="numeric"
                  maxLength={11}
                  className="form_input bg-transparent"
                />
              </Form.Item>
              <Form.Item
                name="code"
                extra={
                  timing ? (
                    <span className="text-color-weak">{count}秒后重发</span>
                  ) : (
                    <span className="text-xs text-white" onClick={handleGetCode}>
                      获取验证码
                    </span>
                  )
                }
              >
                <Input
                  placeholder="请输入验证码"
                  inputMode="numeric"
                  maxLength={6}
                  className="form_input"
                />              
              </Form.Item>
            </div>
          </Form>
        </div>
      </div>
    </Page>
  );
}
