'use client';

import Image from 'next/image';
import { Toast } from 'antd-mobile';
import { RightOutline, AddOutline } from 'antd-mobile-icons';
import Page from '@/components/Page';
import List from '@/components/List';
import Button from '@/components/Button';
import * as AspectRatio from '@radix-ui/react-aspect-ratio';

export default function Me() {
  return (
    <Page nav={false}>
      <div
        className="absolute left-0 top-0 -z-10 flex h-[350px] w-full  items-center
      justify-center bg-[url('/images/G/<EMAIL>')] bg-cover bg-center bg-no-repeat"
      />
      <div className="space-y-3 px-3 py-6">
        <div
          className="flex items-center  px-5 py-8 text-white"
          onClick={() => {
            // 点击
            Toast.show('请先绑车后体验');
          }}
        >
          <div className="h-[70px] w-[70px] rounded-[70px] bg-[url('/images/G/<EMAIL>')] bg-cover bg-center  bg-no-repeat " />
          <div className="ml-5">
            <div className="text-2xl">体验用户</div>
            <div className="text-xs" style={{ color: 'rgba(255, 255, 255, .6)' }}>
              1806798****8888
            </div>
          </div>

          <div className="ml-[auto] p-5">
            <RightOutline />
          </div>
        </div>

        <div className="rounded-2xl  bg-white p-4">
          <div className="mb-2 text-xl ">我的车辆</div>

          <div className="flex items-center  px-2 py-2">
            <div className="h-[70px] w-[70px] rounded-md bg-[#f6f7f8]">
              <div className="relative w-[90px] -translate-x-[10px]">
                <AspectRatio.Root ratio={482 / 375}>
                  <Image src="/images/<EMAIL>" alt="车型图" fill priority />
                </AspectRatio.Root>
              </div>
            </div>
            <div className="ml-5">
              <div className="text-2xl text-[#30333F]">立马V9飞跃版</div>
              <div className="text-xs text-[#908F94]">V9飞跃版</div>
            </div>

            <div
              className="ml-[auto]  px-2 py-1 text-3xs text-[#FF6430]"
              style={{ background: 'rgba(255, 208, 192, 0.60)', borderRadius: '10px' }}
            >
              车主
            </div>
          </div>
        </div>
        <Button
          block
          type="primary"
          onClick={() => {
            Toast.show('请先绑车后体验');
          }}
        >
          <div className="flex h-full w-full items-center justify-center text-white">
            <AddOutline className=" mr-3 text-3xl " />
            添加车辆 
          </div>
        </Button>
        <List
          items={[
            {
              key: 1,
              icon: '/images/G/<EMAIL>',
              title: '我的收藏',
              onClick: () => {
                Toast.show('请先绑车后体验');
              },
            },
            {
              key: 3,
              icon: '/images/G/<EMAIL>',
              title: '设置',
              onClick: () => {
                Toast.show('请先绑车后体验');
              },
            },
            {
              key: 4,
              icon: '/images/G/<EMAIL>',
              title: '关于',
              onClick: () => {
                Toast.show('请先绑车后体验');
              },
            },
          ]}
        />
        <div className="h-[calc(49px+env(safe-area-inset-bottom))]" />
      </div>
    </Page>
  );
}
