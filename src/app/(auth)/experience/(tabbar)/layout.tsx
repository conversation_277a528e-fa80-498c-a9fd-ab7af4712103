'use client';

import { useMemo } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import Image from 'next/image';
import { TabBar } from 'antd-mobile';
import { useNative } from '@/lib/native';

export default function Layout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const router = useRouter();
  const { safeArea } = useNative();

  const tabs = useMemo(
    () => [
      {
        key: '/experience',
        title: (active: boolean) => (
          <span className={active ? 'font-medium text-color-text' : ' text-color-weak'}>车况</span>
        ),
        icon: (active: boolean) => (
          <Image
            src={`/images/tab-vehicle-${active ? 's' : 'n'}@2x.png`}
            alt="home"
            width={24}
            height={24}
          />
        ),
      },
      {
        key: '/experience/message',
        title: (active: boolean) => (
          <span className={active ? 'font-medium text-color-text' : ' text-color-weak'}>消息</span>
        ),
        icon: (active: boolean) => (
          <Image
            src={`/images/tab-news-${active ? 's' : 'n'}@2x.png`}
            alt="message"
            width={24}
            height={24}
          />
        ),
      },
      {
        key: '/experience/news',
        title: (active: boolean) => (
          <span className={active ? 'font-medium text-color-text' : ' text-color-weak'}>资讯</span>
        ),
        icon: (active: boolean) => (
          <Image
            src={`/images/tab-consulting-${active ? 's' : 'n'}@2x.png`}
            alt="news"
            width={24}
            height={24}
          />
        ),
      },
      {
        key: '/experience/me',
        title: (active: boolean) => (
          <span className={active ? 'font-medium text-color-text' : ' text-color-weak'}>我的</span>
        ),
        icon: (active: boolean) => (
          <Image
            src={`/images/tab-my-${active ? 's' : 'n'}@2x.png`}
            alt="me"
            width={24}
            height={24}
          />
        ),
      },
    ],
    []
  );

  return (
    <div className="relative h-full min-h-screen">
      <div className="h-full">{children}</div>
      <div className="fixed bottom-0 left-0 right-0 bg-white">
        <TabBar
          activeKey={pathname}
          onChange={(key) => {
            router.push(key);
          }}
        >
          {tabs.map((item) => (
            <TabBar.Item key={item.key} title={item.title} icon={item.icon} />
          ))}
        </TabBar>
        <div style={{ height: safeArea.bottom }} />
      </div>
    </div>
  );
}
