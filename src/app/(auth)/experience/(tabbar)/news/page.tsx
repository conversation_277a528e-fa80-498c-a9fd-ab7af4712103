'use client';

import { useRouter } from 'next/navigation';
import { Image, SpinLoading } from 'antd-mobile';
import Page from '@/components/Page';
import { getNews } from '@/server/api/news';
import { timestampToDate, toPhpImg } from '@/utils';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useInfiniteQuery } from '@tanstack/react-query';

export default function Message() {
  const router = useRouter();

  const { data, isFetchingNextPage, hasNextPage, fetchNextPage } = useInfiniteQuery({
    queryKey: ['news'],
    queryFn: ({ pageParam }) => getNews({ page: pageParam, pageSize: 10 }),
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages, lastPageParam) => {
      if (lastPage?.pages === lastPageParam) {
        return undefined;
      }
      return lastPageParam + 1;
    },
  });

  const flattenData = data?.pages ? data.pages.flatMap((page) => [...page.records]) : [];

  return (
    <Page title="资讯" backArrow={<></>} navBgColor="#fff">
      <div className="space-y-3 px-3 py-6">
        <InfiniteScroll
          dataLength={flattenData.length}
          endMessage={
            flattenData.length > 0 ? (
              <div className="pb-8 text-center text-3xs text-color-weak">到底了~</div>
            ) : null
          }
          hasMore={!!hasNextPage}
          loader={
            isFetchingNextPage ? (
              <div className="flex items-center justify-center space-x-1">
                <SpinLoading style={{ '--size': '24px' }} />
                <span className="text-3xs text-color-weak">加载中...</span>
              </div>
            ) : null
          }
          next={() => {
            fetchNextPage();
          }}
        >
          {flattenData.map((_: any) => (
            <div
              key={_.id}
              className="mb-6 w-full overflow-hidden rounded-2xl bg-white"
              onClick={() => router.push(`news/${_.id}`)}
            >
              <Image src={toPhpImg(_.image)} alt="img" style={{ width: '100%', height: 'auto' }} />
              <div className="p-4">
                <div className="line-clamp-1 text-2xl">{_.title}</div>
                <div className="mb-2 mt-2 line-clamp-2 text-xs text-color-weak">{_.introduce} </div>
                <div className="text-right text-2xs text-[#C9CDD3]">
                  {timestampToDate(_.gmtRelease)}
                </div>
              </div>
            </div>
          ))}
        </InfiniteScroll>
        {flattenData.length === 0 && (
          <div className="w-full pt-20 text-center">
            <div className="h-[300px] w-full  bg-[url('/images/empty-info.png')] bg-contain bg-center bg-no-repeat " />
            <span className="text-xl text-color-weak">暂无资讯</span>
          </div>
        )}
        <div className="h-[calc(49px+env(safe-area-inset-bottom))]" />
      </div>
    </Page>
  );
}
