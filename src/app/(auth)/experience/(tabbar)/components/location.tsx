'use client';

import { useEffect } from 'react';
import Image from 'next/image';
import { Toast } from 'antd-mobile';
import { useAmap } from '@/hooks/useAmap';

export default function Location() {
  const { elRef, map } = useAmap();

  useEffect(() => {
    if (map) {
      // 删除地图上所有的覆盖物
      map.clearMap();
      const marker = new AMap.Marker({
        position: [121.529114, 28.586875],
        icon: new AMap.Icon({
          size: new AMap.Size(34, 44),
          imageSize: new AMap.Size(34, 44),
          image: '/images/<EMAIL>',
        }),
        offset: new AMap.Pixel(-17, -44),
      });
      map.add([marker]);
      map.setFitView();
      map.setStatus({ dragEnable: false, zoomEnable: false });
    }
  }, [map]);

  return (
    <div className="rounded-2xl bg-white p-4">
      <div className="mb-2 text-xl font-medium">车辆定位</div>
      <div className="mb-2 flex items-center">
        <Image src="/images/<EMAIL>" alt="location" height={20} width={20} />
        <span className="ml-1 line-clamp-1 text-xs text-color-weak">立马车业集团</span>
      </div>

      <div
        className="relative h-24 overflow-hidden rounded-md"
        onClick={() => {
          Toast.show('请先绑车后体验');
        }}
      >
        <div className="h-32 rounded-md" ref={elRef} />
        <Image
          src="/images/<EMAIL>"
          alt="丢失模式"
          width={24}
          height={24}
          className="absolute right-2 top-2"
        />
      </div>
    </div>
  );
}
