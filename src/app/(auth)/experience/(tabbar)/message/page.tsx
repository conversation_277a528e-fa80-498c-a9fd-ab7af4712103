'use client';

import React, { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Tabs, Badge, Image } from 'antd-mobile';
import Page from '@/components/Page';
import { timestampToDate, toImg } from '@/utils';

const news = [
  {
    id: 1673,
    deviceId: 'L1ZL1ZEH8P0503159',
    deviceName: '立马V9飞跃版',
    content: 8,
    contentName: '您的爱车立马V9飞跃版发生BMS通讯故障，请及时查看',
    data: null,
    createTime: 1706600060000,
    userId: 548,
    isShow: 0,
  },
  {
    id: 1667,
    deviceId: 'L1ZL1ZEH8P0503159',
    deviceName: '立马V9飞跃版',
    content: 0,
    contentName: '您的爱车立马V9飞跃版发生异常摇动，请及时查看',
    data: null,
    createTime: 1706600060000,
    userId: 548,
    isShow: 0,
  },
];

export default function Message({ searchParams }: { searchParams: { [key: string]: string } }) {
  const router = useRouter();
  const pathname = usePathname();
  const activeKey = searchParams.tab || '3';

  const [list, setList] = useState(news);

  const tabItems = [
    { key: '3', name: '车辆', img: '/images/<EMAIL>', dot: false },
    { key: '0', name: '通知', img: '/images/<EMAIL>', dot: false },
    { key: '1', name: '活动', img: '/images/<EMAIL>', dot: false },
    { key: '2', name: '其他', img: '/images/<EMAIL>', dot: false },
  ];

  const onChange = (key: string) => {
    if (key === '3') {
      setList(news);
    } else {
      setList([]);
    }
    const updatedSearchParams = new URLSearchParams(searchParams);
    updatedSearchParams.set('tab', key);
    router.push(`${pathname}?${updatedSearchParams.toString()}`);
  };
  return (
    <Page title="消息" backArrow={<></>} navBgColor="#fff">
      <div className="mb-4 bg-white pt-4">
        <Tabs activeKey={activeKey} onChange={onChange}>
          {tabItems.map((_: any, index: any) => (
            <Tabs.Tab
              title={
                <div className="relative flex flex-col items-center justify-center">
                  <div
                    className="mb-1 h-[30px] w-[30px] bg-contain bg-center bg-no-repeat"
                    style={{ backgroundImage: "url('" + _.img + "')" }}
                  />
                  {_.dot && (
                    <Badge
                      color="#E33939"
                      content={Badge.dot}
                      style={{ position: 'absolute', right: '-10px', top: 0 }}
                    />
                  )}
                  {_.name}
                </div>
              }
              key={_.key}
            />
          ))}
        </Tabs>
      </div>
      <div className="space-y-3 px-3 py-6">
        {list.map((_: any) => (
          <div key={_.id} className="w-full" style={{ marginBottom: '1.5rem' }}>
            <div className="mb-2 text-center text-2xs text-color-weak">
              {timestampToDate(_.createTime)}
            </div>
            <div className="w-full overflow-hidden rounded-2xl bg-white">
              {_.img && (
                <Image src={toImg(_.img)} alt="img" style={{ width: '100%', height: 'auto' }} />
              )}
              <div className="p-4">
                <div className="line-clamp-1 text-2xl">{_.title || _.deviceName}</div>
                <div className="mt-2 line-clamp-2 text-xs text-color-weak" />
                {_.contentName || _.descrip}
              </div>
            </div>
          </div>
        ))}
        {list.length === 0 && (
          <div className="w-full pt-20 text-center">
            <div className="h-[300px] w-full bg-[url('/images/empty-content.png')] bg-contain bg-center bg-no-repeat " />
            <span className="text-xl text-color-weak">暂无消息</span>
          </div>
        )}
        <div className="h-[calc(49px+env(safe-area-inset-bottom))]" />
      </div>
    </Page>
  );
}
