'use client';

import Link from 'next/link';
import Image from 'next/image';
import dynamic from 'next/dynamic';
import { Toast } from 'antd-mobile';
import Page from '@/components/Page';

const Location = dynamic(() => import('./components/location'), { ssr: false });

export default function HomePage() {
  return (
    <Page
      backArrow={
        <span
          className="flex items-center text-color-text"
          onClick={() => {
            // 点击
            Toast.show('请先绑车后体验');
          }}
        >
          <span className="ml-2 text-3xl font-semibold">立马V9飞跃版</span>
          <Image src="/images/<EMAIL>" alt="arrow" width={24} height={24} />
        </span>
      }
      right={
        <Link href="/user/landing">
          <button className="rounded-2xl bg-gradient-to-b from-[#2FB8FF] from-[20%] to-[#9EECD9] to-[97%] px-3 py-1 text-sm text-white">
            退出体验
          </button>
        </Link>
      }
      className="bg-[url('/images/img_carhome_bg.png')] bg-cover bg-no-repeat"
      onBack={() => {}}
    >
      <div className="relative">
        <div className="px-4 py-2">
          <div className="relative z-10 flex">
            <div className="flex-1">
              <div className="text-7xl font-bold">
                50<span className="text-xs font-medium text-color-weak">&nbsp;km</span>
              </div>
              <div className="text-color-weak">剩余里程</div>
            </div>
            <div className="flex flex-col items-end">
              <div className="relative z-[1] h-11 w-32 overflow-hidden rounded-lg border-2 border-white/40 shadow-[0_0_8px_3px_rgba(51,51,51,0.05)]">
                <span className="absolute bottom-0 left-0">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    height="210"
                    width={(128 * 86) / 100}
                    viewBox="0 0 110 420"
                    fill="none"
                    preserveAspectRatio="none"
                  >
                    <path
                      d="M106.1 419.9C103.1 414.3 99.3 409.9 99.3 399C99.3 380.7 110 380.7 110 362.4C110 344.1 99.3 344.1 99.3 325.8C99.3 307.5 110 307.6 110 289.3C110 271 99.3 271 99.3 252.7C99.3 234.4 109.8 234.4 110 216.4V216.2C109.9 198.2 99.3 198.1 99.3 179.9C99.3 161.7 110 161.6 110 143.4C110 125.2 99.3 125.1 99.3 106.8C99.3 88.4998 110 88.5 110 70.2C110 51.9 99.3 51.8999 99.3 33.5999C99.3 15.2999 108.8 15.4 109.9 0H0V420L106.1 419.9Z"
                      fill="url(#paint0_linear_2866_13023)"
                    />
                    <defs>
                      <linearGradient
                        id="paint0_linear_2866_13023"
                        x1="0"
                        y1="307.5"
                        x2="148"
                        y2="307.5"
                        gradientUnits="userSpaceOnUse"
                      >
                        <stop offset="0.135135" stopColor="#2FB8FF" />
                        <stop offset="1" stopColor="#9EECD9" />
                      </linearGradient>
                    </defs>
                  </svg>
                </span>
                <span className="absolute left-0 top-0 rounded-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    height="210"
                    width={(128 * 86) / 100}
                    viewBox="0 0 112 420"
                    fill="none"
                    preserveAspectRatio="none"
                  >
                    <path
                      d="M112 346.6C112 328.2 101 328.2 101 309.9C101 291.6 112 291.6 112 273.2C112 254.8 101 254.9 101 236.5C101 218.1 111.8 218.2 112 200.2V200C111.8 181.9 101 181.8 101 163.6C101 145.4 112 145.3 112 126.9C112 108.5 101 108.6 101 90.2C101 71.7999 112 71.8999 112 53.5999C112 35.2999 101 35.1999 101 16.8999C100.848 10.938 102.447 5.06245 105.6 0H0V420H112C112 401.7 101 401.7 101 383.3C101 364.9 112 364.9 112 346.6Z"
                      fill="url(#paint0_linear_2866_13022)"
                      fillOpacity="0.4"
                    />
                    <defs>
                      <linearGradient
                        id="paint0_linear_2866_13022"
                        x1="0"
                        y1="324.5"
                        x2="136"
                        y2="324.5"
                        gradientUnits="userSpaceOnUse"
                      >
                        <stop offset="0.0699088" stopColor="#2FB8FF" />
                        <stop offset="1" stopColor="#9EECD9" />
                      </linearGradient>
                    </defs>
                  </svg>
                </span>
                <div className="absolute bottom-0 left-0 right-0 top-0 flex items-center justify-center">
                  <span className="mr-1 text-lg font-medium text-color-text">86%</span>
                </div>
              </div>
              <div className="-mr-1.5 mt-1">
                <Image
                  src={`/images/<EMAIL>`}
                  alt="bluetooth"
                  width={39}
                  height={39}
                />
                <Image
                  src={`/images/<EMAIL>`}
                  alt="gps"
                  width={39}
                  height={39}
                  className="-mt-1.5"
                />

                <Image
                  src={`/images/<EMAIL>`}
                  alt="gms"
                  width={39}
                  height={39}
                  className="-mt-1.5"
                />
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="mt-28 flex h-44 flex-col-reverse items-center rounded-2xl bg-white">
              <div className="mb-4 flex space-x-4">
                <div
                  className="flex flex-col text-center"
                  onClick={() => {
                    // 点击
                    Toast.show('请先绑车后体验');
                  }}
                >
                  <Image src={`/images/<EMAIL>`} alt="power" width={60} height={60} />
                  <span className="mt-3 text-xs text-color-weak">开机</span>
                </div>

                <div
                  className="flex flex-col text-center"
                  onClick={() => {
                    // 点击
                    Toast.show('请先绑车后体验');
                  }}
                >
                  <Image src={`/images/<EMAIL>`} alt="power" width={60} height={60} />
                  <span className="mt-3 text-xs text-color-weak">撤防</span>
                </div>

                <div
                  className="flex flex-col text-center"
                  onClick={() => {
                    // 点击
                    Toast.show('请先绑车后体验');
                  }}
                >
                  <Image
                    src={`/images/<EMAIL>`}
                    alt="power"
                    width={60}
                    height={60}
                  />
                  <span className="mt-3 text-xs text-color-weak">座桶锁</span>
                </div>

                <div
                  className="flex flex-col text-center"
                  onClick={() => {
                    // 点击
                    Toast.show('请先绑车后体验');
                  }}
                >
                  <Image
                    src={`/images/<EMAIL>`}
                    alt="power"
                    width={60}
                    height={60}
                  />
                  <span className="mt-3 text-xs text-color-weak">寻车铃</span>
                </div>
              </div>
              <Image src="/images/<EMAIL>" alt="car" priority width={382} height={273} />
            </div>

            <Location />
            <div className="rounded-2xl bg-white p-4">
              <div className="mb-2 flex">
                <span className="flex-1 text-xl font-medium">骑行记录</span>
                <div className="flex items-center">
                  <span
                    className="text-xs font-medium text-color-light"
                    onClick={() => {
                      // 点击
                      Toast.show('请先绑车后体验');
                    }}
                  >
                    更多
                  </span>
                  <Image
                    src="/images/<EMAIL>"
                    alt="arrow"
                    width={24}
                    height={24}
                  />
                </div>
              </div>

              <div className="flex items-center">
                <Image
                  src="/images/riding-thumbnail.png"
                  alt="缩略图"
                  width={82}
                  height={82}
                  className="rounded-md"
                />
                <div className="ml-2 flex flex-col space-y-3 text-3xs text-color-weak">
                  <div className="flex space-x-3">
                    <div className="flex items-center space-x-1">
                      <Image
                        src="/images/<EMAIL>"
                        alt="distance"
                        width={16}
                        height={16}
                      />
                      <span className="space-x-1">
                        <span className="text-base font-bold text-color-text">6.3</span>
                        <span>km</span>
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Image src="/images/<EMAIL>" alt="time" width={16} height={16} />
                      <span className="space-x-1">
                        <span className="text-base font-bold text-color-text">18</span>
                        <span>min</span>
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Image src="/images/<EMAIL>" alt="power" width={16} height={16} />
                      <span className="space-x-1">
                        <span className="text-base font-bold text-color-text">6</span>
                        <span>wh</span>
                      </span>
                    </div>
                  </div>
                  <div>11-10 08:48-09:06</div>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div
                className="h-40 w-full rounded-2xl bg-[url('/images/<EMAIL>')] bg-cover bg-center bg-no-repeat p-4"
                onClick={() => {
                  // 点击
                  Toast.show('请先绑车后体验');
                }}
              >
                <div className="text-xl font-medium">服务与帮助</div>
                <div className="mt-1 text-3xs text-color-weak">建议反馈、新手教程</div>
              </div>
              <div
                className="h-40 w-full rounded-2xl bg-[url('/images/<EMAIL>')] bg-cover bg-center bg-no-repeat p-4"
                onClick={() => {
                  // 点击
                  Toast.show('请先绑车后体验');
                }}
              >
                <div className="text-xl font-medium">共享</div>
                <div className="mt-1 text-3xs text-color-weak">邀请家人朋友共享车辆</div>
              </div>
            </div>
            <div
              className="h-20 rounded-2xl bg-[url('/images/<EMAIL>')] bg-cover bg-center bg-no-repeat p-4"
              style={{ marginBottom: 40 }}
              onClick={() => {
                // 点击
                Toast.show('请先绑车后体验');
              }}
            >
              <div className="text-xl font-medium">更多功能</div>
              <div className="mt-1 text-3xs text-color-weak">车辆设置、电池管理、设备管理等</div>
            </div>
            <div className="h-[calc(49px+env(safe-area-inset-bottom))]" />
          </div>
        </div>
      </div>
    </Page>
  );
}
