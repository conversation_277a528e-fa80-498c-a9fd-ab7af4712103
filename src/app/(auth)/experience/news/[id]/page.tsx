'use client';

import { Toast } from 'antd-mobile';
import Page from '@/components/Page';
import { getNewsInfo } from '@/server/api/news';
import { timestampToDate, toPhpContent } from '@/utils';
import { useQuery } from '@tanstack/react-query';

export default function Message({ params }: { params: { id: string } }) {
  const { data } = useQuery({
    queryKey: ['news', params.id],
    queryFn: () => getNewsInfo(params.id),
    enabled: !!params.id,
  });

  return (
    <Page
      title="资讯详情"
      style={{ background: '#fff' }}
      navBgColor="#fff"
      right={
        <div className="flex w-full items-center justify-end">
          <div
            className="h-[30px] w-[30px] bg-contain bg-center bg-no-repeat"
            style={{
              backgroundImage: "url('/images/<EMAIL>')",
            }}
            onClick={() => {
              Toast.show('请先绑车后体验');
            }}
          />
          <div
            className="ml-2  h-[26px] w-[26px]  bg-[url('/images/<EMAIL>')]  bg-contain bg-center bg-no-repeat"
            onClick={() => {
              Toast.show('请先绑车后体验');
            }}
          />
        </div>
      }
    >
      <div className="bg-white px-6 py-8">
        <div className="mb-4 text-center  text-2xl">{data?.title}</div>
        <div className="mb-8 text-right text-xs text-[#C9CDD3]">
          {timestampToDate(data?.gmtRelease)}
        </div>
        <div
          className=" mb-10  text-xs text-color-weak"
          dangerouslySetInnerHTML={{ __html: toPhpContent(data?.content) }}
        />
      </div>
    </Page>
  );
}
