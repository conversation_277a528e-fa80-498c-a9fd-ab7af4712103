import { proxy, useSnapshot } from 'valtio';
import { devtools } from 'valtio/utils';

type Location = {
  latitude: string;
  longitude: string;
  address: string;
  province: string;
  city: string;
};

interface State {
  bind: Record<string, any>;
  location: Location;
  signup: Record<string, any>;
  communicateTypes: number[];
  hasBleLimit: boolean;
}

const state = proxy<State>({
  bind: {}, // 绑定车辆的一些数据
  location: {
    latitude: '',
    longitude: '',
    address: '',
    province: '',
    city: '',
  },
  signup: {}, // 注册页面表单数据
  communicateTypes: [], // 设备连接方式
  hasBleLimit: false, // 是否受限制的蓝牙型号
});

/// 受限制的蓝牙型号, 不能使用寻车铃/座桶锁/音效设置/自动锁车/开机密码/nfc设置等功能
const limitBleType = ['13910210', '14070100', '14090200', '13820500', '13580312'];

export const setHasBleLimit = (deviceNo: string, deviceVersion: string) => {
  console.log('设备型号 :>> ', deviceNo, deviceVersion);
  const idx = limitBleType.findIndex((item) => deviceNo.includes(item));
  console.log('是否受限制 :>> ', idx, Number.parseInt(deviceVersion.slice(-2)) < 20);
  if (idx != -1 && Number.parseInt(deviceVersion.slice(-2)) < 20) {
    state.hasBleLimit = true;
  } else {
    state.hasBleLimit = false;
  }
};

export { state, useSnapshot };

export const setBind = (data: Record<string, any>) => {
  Object.keys(data).forEach((key) => {
    state.bind[key] = data[key];
  });
};

export const setLocation = (data: Location) => {
  state.location = data;
};

export const resetBind = () => {
  state.bind = {};
};

export const setSignup = (data: Record<string, any>) => {
  state.signup = data;
};

export const setCommunicateTypes = (data: number[]) => {
  state.communicateTypes = data;
};

const unsub = devtools(state, { name: 'valtio', enabled: true });
