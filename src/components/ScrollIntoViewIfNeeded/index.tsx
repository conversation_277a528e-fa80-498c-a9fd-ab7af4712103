import { useRef, useEffect } from 'react';
import scrollIntoView from 'scroll-into-view-if-needed';
import { useSpring, config } from '@react-spring/web';

// create a component when the active prop is true, it will scroll into viewport
export default function ScrollIntoViewIfNeeded({
  active,
  children,
}: {
  active: boolean;
  children: React.ReactNode;
}) {
  const ref = useRef<HTMLDivElement>(null);
  const [_, api] = useSpring(() => ({ scrollTop: 0 }));

  useEffect(() => {
    if (active && ref.current) {
      scrollIntoView(ref.current, {
        scrollMode: 'if-needed',
        block: 'start',
        behavior: (instructions) => {
          if (instructions.length === 0) return;
          console.log(instructions);
          const [{ el, top }] = instructions;
          api.start({
            from: { scrollTop: el.scrollTop },
            to: { scrollTop: top },
            onChange: (props) => {
              el.scrollTop = props.value.scrollTop;
            },
            config: config.stiff,
          });
        },
      });
    }
  }, [active, api]);

  return <div ref={ref}>{children}</div>;
}
