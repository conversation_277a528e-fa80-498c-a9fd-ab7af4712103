import { Fragment } from 'react';
import ListItem from './ListItem';
import type { ListItemProps } from './ListItem';

export type ListProps = {
  header?: React.ReactNode;
  items: ListItemProps[];
  size?: 'normal' | 'small' | 'large';
};

export default function List({ header, items = [], size = 'normal' }: ListProps) {
  const itemList = items.map(({ key, ...item }, index) => {
    return (
      <Fragment key={key || index}>
        <ListItem {...item} size={size} />
      </Fragment>
    );
  });

  if (items.every((item) => item.show === false)) {
    return null;
  }
  return (
    <>
      {header && <div className="py-2 text-sm text-color-weak">{header}</div>}
      <div className="rounded-2xl bg-white py-1">{itemList}</div>
    </>
  );
}
