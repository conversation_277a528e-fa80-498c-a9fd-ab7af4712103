/*
 * @Author: <PERSON><PERSON>
 * @Email: <EMAIL>
 * @Date: 2025-03-03 10:56:17
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-03-19 14:28:56
 */
// @ts-nocheck
'use client'

import List from '@/components/List'
import Slider from '@/components/Slider'
import Switch from '@/components/Switch'
import { toMarksArray } from '@/utils'
import { Dialog, Input } from 'antd-mobile'
import Image from 'next/image'
import { useState } from 'react'

const ListGroup = ({ list = [], data = {}, handleSubmit, scheme = [], disabled = false }) => {
  const [inputInfo, setInputInfo] = useState({
    open: false,
    key: '',
    value: '',
    name: ''
  })
  const renderValue = (item, disabled) => {
    switch (item.type) {
      case 'switch':
        return (
          <Switch
            disabled={disabled}
            checked={data[item.key] === '1' || data[item.key] === 1 || item.defaultValue}
            onChang<PERSON>={(checked) => handleSubmit(item.key, checked ? 1 : 0)}
          />
        )
      case 'input':
        return (
          <span
            className="flex justify-between items-center"
            onClick={() => {
              setInputInfo({ open: true, key: item.key, value: data[item.key], name: item.title })
            }}
          >
            <span>{data[item.key]}</span>
            <Image
              src="/images/<EMAIL>"
              alt="编辑"
              width={16}
              height={16}
              className="ml-3 h-4 w-4"
            />
          </span>
        )
      case 'slider':
        return <>{item.rightShow && data[item.key] ? item.rightShow(data[item.key]) : null}</>
      default:
        return null
    }
  }

  const renderFooter = (item, disabled) => {
    if (item.type === 'slider') {
      return (
        <div className="px-6">
          <Slider
            min={item.min}
            max={item.max}
            disabled={disabled}
            step={item.step || 1}
            defaultValue={[
              data[item.key]
                ? item.marksData.indexOf(Number(data[item.key])) + 1
                : item.defaultValue
            ]}
            marks={toMarksArray(item.marks || {})}
            onValueCommit={(value) =>
              handleSubmit(item.key, item.marksData[value[0].toFixed(0) - 1])
            }
          />
        </div>
      )
    }
    return null
  }

  const isShow = (item, data, scheme) => {
    // 检查是否有scheme限制
    if (item.scheme && scheme && !scheme.includes(item.scheme)) {
      return false
    }
    // 检查是否有show条件
    if (item.show) {
      const value = data[item.show]
      return value === 1 || value === '1'
    }
    return true
  }

  return (
    <>
      {list.map((items, index) => (
        <List
          key={index}
          items={items.map((item) => ({
            key: item.key,
            title: item.title,
            value: renderValue(item, disabled),
            footer: renderFooter(item, disabled),
            show: isShow(item, data, scheme)
          }))}
        />
      ))}
      <Dialog
        visible={inputInfo.open}
        onCancel={() => setInputInfo({ open: false, key: '', value: '' })}
        content={
          <div className="flex flex-col items-center justify-center px-2 text-xl">
            <div className="text-center">{inputInfo.name}</div>
            <Input
              maxLength={10}
              placeholder={'请输入' + inputInfo.name}
              className="mb-2 mt-3 h-10 rounded border border-[#e5e5e5] px-2"
              value={inputInfo.value}
              onChange={(value) => setInputInfo({ ...inputInfo, value })}
            />
          </div>
        }
        actions={[
          [
            {
              key: 'cancel',
              text: '取消'
            },
            {
              key: 'confirm',
              text: '确认'
            }
          ]
        ]}
        onAction={async (action) => {
          if (action.key === 'confirm') {
            setInputInfo({ open: false, key: '', value: '' })
            handleSubmit(inputInfo.key, inputInfo.value)
          } else {
            setInputInfo({ open: false, key: '', value: '' })
          }
        }}
        afterClose={() => setInputInfo({ open: false, key: '', value: '' })}
      />
    </>
  )
}

export default ListGroup
