import * as PopoverPrimitive from '@radix-ui/react-popover';

export type PopoveProps = {
  children: React.ReactNode;
  content: React.ReactNode;
};

export default function Popove(props: PopoveProps) {
  return (
    <PopoverPrimitive.Root>
      <PopoverPrimitive.Trigger>{props.children}</PopoverPrimitive.Trigger>
      <PopoverPrimitive.Portal>
        <PopoverPrimitive.Content
          className="w-[260px] rounded-lg bg-white p-4 shadow-[0_10px_38px_-10px_hsla(206,22%,7%,.35),0_10px_20px_-15px_hsla(206,22%,7%,.2)] will-change-[transform,opacity] data-[state=open]:data-[side=bottom]:animate-slideUpAndFade data-[state=open]:data-[side=left]:animate-slideRightAndFade data-[state=open]:data-[side=right]:animate-slideLeftAndFade data-[state=open]:data-[side=top]:animate-slideDownAndFade"
          sideOffset={5}
          collisionPadding={8}
        >
          {props.content}
          <PopoverPrimitive.Arrow className="fill-white" />
        </PopoverPrimitive.Content>
      </PopoverPrimitive.Portal>
    </PopoverPrimitive.Root>
  );
}
