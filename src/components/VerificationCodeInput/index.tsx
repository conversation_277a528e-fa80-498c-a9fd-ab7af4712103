import React, { useState, useRef, useEffect } from 'react';

interface VerificationCodeInputProps {
  length?: number;
  value?: string;
  defaultValue?: string;
  onChange?: (value: string) => void;
}

const VerificationCodeInput: React.FC<VerificationCodeInputProps> = ({
  length = 6,
  value,
  defaultValue,
  onChange,
}) => {
  const [innerValue, setInnerValue] = useState(() => {
    if (value !== undefined) {
      return value;
    }
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    return '';
  });
  const [focused, setFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (value !== undefined) {
      setInnerValue(value);
    }
  }, [value]);

  const code = Array.from({ length }, (_, i) => innerValue[i] || '');

  const onFocus = () => {
    inputRef.current?.focus();
    setFocused(true);
  };

  const onBlur = () => {
    setFocused(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value.slice(0, length);
    if (value === undefined) {
      setInnerValue(val);
    }
    onChange?.(val);
  };

  return (
    <div className="relative flex" tabIndex={0} onFocus={onFocus} onBlur={onBlur} role="button">
      <input
        ref={inputRef}
        className="absolute left-[-200vw] top-0 block"
        value={innerValue}
        type="text"
        inputMode="numeric"
        aria-hidden
        onChange={handleChange}
      />
      <div className="flex space-x-2">
        {code.map((digit, index) => (
          <div
            key={index}
            className="flex h-12 w-12 items-center justify-center rounded-md bg-white text-xl"
          >
            {digit}
            {focused && innerValue.length === index && (
              <span
                className="ml-[1px] h-5 w-[2px]  animate-[cursor_1s_linear_infinite] bg-color-text "
                aria-hidden
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default VerificationCodeInput;
