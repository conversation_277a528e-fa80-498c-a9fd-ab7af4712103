import React, { useEffect, useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { LeftOutline, RightOutline } from 'antd-mobile-icons';
import * as PopoverPrimitive from '@radix-ui/react-popover';

type MonthPickerProps = {
  defaultValue?: Dayjs;
  value?: Dayjs;
  children: React.ReactNode;
  disabledDate?: (current: Dayjs) => boolean;
  onChange?: (value: Dayjs) => void;
};

const MonthPicker = ({
  defaultValue,
  value,
  onChange,
  disabledDate,
  children,
}: MonthPickerProps) => {
  const [day, setDay] = useState<Dayjs>(() => {
    if (value !== undefined) {
      return value;
    }
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    return dayjs();
  });
  const [innerValue, setInnerValue] = useState<Dayjs>(() => {
    if (value !== undefined) {
      return value;
    }
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    return dayjs();
  });
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (value !== undefined) {
      setInnerValue(value);
    }
  }, [value]);

  const handlePrevYear = () => {
    setDay((prevValue) => prevValue.subtract(1, 'year'));
  };

  const handleNextYear = () => {
    setDay((prevValue) => prevValue.add(1, 'year'));
  };

  const handleClick = (month: Dayjs, disabled: boolean) => {
    if (disabled) return;
    if (value === undefined) {
      setInnerValue(month);
    }
    onChange?.(month);
    setOpen(false);
  };

  const months = Array.from({ length: 12 }, (_, index) => day.clone().month(index));

  return (
    <PopoverPrimitive.Root
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
      }}
    >
      <PopoverPrimitive.Trigger>{children}</PopoverPrimitive.Trigger>
      <PopoverPrimitive.Portal>
        <PopoverPrimitive.Content
          className="w-[260px] rounded-lg bg-white shadow-[0_10px_38px_-10px_hsla(206,22%,7%,.35),0_10px_20px_-15px_hsla(206,22%,7%,.2)] will-change-[transform,opacity] data-[state=open]:data-[side=bottom]:animate-slideUpAndFade"
          sideOffset={4}
          collisionPadding={8}
        >
          <div className="relative">
            <div className="flex justify-center space-x-2 border-b border-color-border p-2">
              <button onClick={handlePrevYear}>
                <LeftOutline fontSize={14} />
              </button>
              <div className="text-center">
                <span className="text-14">{day.format('YYYY年')}</span>
              </div>
              <button onClick={handleNextYear}>
                <RightOutline fontSize={14} />
              </button>
            </div>
            <div className="p-2">
              <table className="h-[192px] w-full table-fixed border-collapse text-center">
                <tbody>
                  {Array.from({ length: 4 }, (_, rowIndex) => (
                    <tr key={rowIndex}>
                      {Array.from({ length: 3 }, (_, colIndex) => {
                        const monthIndex = rowIndex * 3 + colIndex;
                        const month = months[monthIndex];
                        const isCurrentMonth =
                          month.format('YYYY-MM') === innerValue.format('YYYY-MM');
                        // 不可选择的日期
                        const disabled = disabledDate ? disabledDate(month) : false;
                        return (
                          <td
                            key={colIndex}
                            title={month.format('YYYY-MM')}
                            onClick={() => handleClick(month, disabled)}
                          >
                            <div
                              className={`relative inline-block h-6 w-14 rounded px-1 leading-6 transition delay-75 ${
                                isCurrentMonth ? 'bg-primary text-white' : ''
                              } ${disabled ? 'text-gray-400' : ''}`}
                            >
                              {month.format('M月')}
                            </div>
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <PopoverPrimitive.Arrow className="fill-white" />
        </PopoverPrimitive.Content>
      </PopoverPrimitive.Portal>
    </PopoverPrimitive.Root>
  );
};

export default MonthPicker;
