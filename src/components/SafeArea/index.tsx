'use client';

import { useNative } from '@/lib/native';

export type SafeAreaProps = {
  position: 'top' | 'bottom';
};

export default function SafeArea({ position }: SafeAreaProps) {
  const { safeArea } = useNative();

  return (
    <div
      className="block w-full"
      style={
        position === 'top'
          ? { paddingTop: safeArea.top + 'px' }
          : { paddingBottom: safeArea.bottom + 'px' }
      }
    />
  );
}
