'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import cookies from 'js-cookie';
const currentEnv = process.env.NODE_ENV;
export type PageProps = {
  title?: string;
};

const trackPageView = async (url: string, href: string, title: string) => {
  // if(currentEnv !== "development") return;
  console.log('📌 埋点记录:', title, url, cookies.get('phone'), currentEnv, href);
  const params = {
    eventType: 'PAGE_VIEW',
    pageTitle: title,
    pageName: url,
    pagePath: href,
    // pre_page_title: '',
    // pre_page_name: '',
    // pre_page_path: '',
    elementName: '',
    enterProgm: '',
    currProgm: '立马科技',
    deviceMode: '',
    appVersion: currentEnv == 'development' ? '测试|开发版本' : '',
    visitor: cookies.get('phone'),
    visitorName: '',
  };

  fetch('https://lmhdxcx.lima-info.com:8082/api/tracking/evtTracking/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });
};

export default function ClientTracker({ title }: PageProps) {
  const pathname = usePathname();
  useEffect(() => {
    if (cookies.get('phone')) {
      if (pathname) {
        trackPageView(pathname, window.location.href, title || '未配置title');
      }
    }
  }, [pathname]);

  return null;
}
