'use client';

import { useEffect } from 'react';
import cookies from 'js-cookie';
const currentEnv = process.env.NODE_ENV;
export type PageProps = {
  title?: string;
};
const trackEvent = (eventName: string, title: any) => {
  console.log('📌 埋点事件点击:', eventName, window.location.href);
  const params = {
    eventType: 'BTN_CLICK',
    pageTitle: title,
    pageName: window.location.href,
    pagePath: window.location.href,
    // pre_page_title: '',
    // pre_page_name: '',
    // pre_page_path: '',
    elementName: eventName,
    enterProgm: '',
    currProgm: '立马科技',
    deviceMode: '',
    appVersion: currentEnv == 'development' ? '测试|开发版本' : '',
    visitor: cookies.get('phone'),
    visitorName: '',
  };

  console.log("params", params)

  fetch('https://lmhdxcx.lima-info.com:8082/api/tracking/evtTracking/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });
};

export default function ClientTrackerClick({ title }: PageProps) {
  useEffect(() => {
    const handleClick = (event: Event) => {
      const target = (event.target as HTMLElement).closest('[data-track]');
      if (target) {
        const eventName = target.getAttribute('data-track') || '未知埋点';
        trackEvent(eventName, title);
      }
    };

    // 监听点击事件
    document.addEventListener('click', handleClick);

    // 清理函数
    return () => {
      document.removeEventListener('click', handleClick);
    };
  }, []);

  return null;
}