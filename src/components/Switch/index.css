/* reset */
button {
  all: unset;
}

.SwitchRoot {
  width: 51px;
  height: 31px;
  /*background: linear-gradient(84deg, #585c78 0%, #30333f 100%);*/
  background: linear-gradient(84deg, rgb(164, 174, 190) 0%, rgb(170, 177, 195) 100%);
  border-radius: 16px;
  position: relative;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.SwitchRoot[data-state='checked'] {
  /*background: linear-gradient(142deg, #2fb8ff 0%, #9eecd9 100%);*/
  background: linear-gradient(142deg, rgb(94, 101, 139) 0%, rgb(109, 116, 144) 100%);
}

.SwitchThumb {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  background: rgb(254, 254, 255);
  border-radius: 50%;
  transition: transform 100ms;
  transform: translateX(4px);
  will-change: transform;
}

.SwitchThumb[data-state='checked'] {
  transform: translateX(24px);
  background: linear-gradient(228deg, #ffffff 0%, #eef1f4 100%);
}

.SwitchThumbIcon {
  width: 10px;
  height: 10px;
  /*background: linear-gradient(214deg, #848897 0%, #7d808e 100%);*/
  border-radius: 50%;
}

.SwitchThumbIcon[data-state='checked'] {
  background: linear-gradient(142deg, #2fb8ff 0%, rgb(80, 231, 151) 100%);
}
