/*
 * @Date: 2025-02-20 10:36:53
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-03-12 10:22:16
 */
// @ts-nocheck
import * as SwitchPrimitive from '@radix-ui/react-switch'
import './index.css'

export type SwitchProps = {
  defaultChecked?: boolean
  checked?: boolean
  disabled?: boolean
  onChange?: (checked: boolean) => void
}

export default function Switch({
  defaultChecked,
  checked,
  disabled = false,
  onChange,
  className
}: SwitchProps) {
  return (
    <SwitchPrimitive.Root
      className={`SwitchRoot ${className}`}
      defaultChecked={defaultChecked}
      checked={checked}
      disabled={disabled}
      onCheckedChange={onChange}
    >
      <SwitchPrimitive.Thumb className="SwitchThumb">
        <SwitchPrimitive.Thumb className="SwitchThumbIcon" />
      </SwitchPrimitive.Thumb>
    </SwitchPrimitive.Root>
  )
}
