.SliderRoot {
  position: relative;
  display: flex;
  align-items: center;
  user-select: none;
  touch-action: none;
  width: 100%;
  min-width: 100px;
  height: 16px;
}

.SliderTrack {
  background-color: #c9cdd3;
  position: relative;
  flex-grow: 1;
  border-radius: 9999px;
  height: 2px;
}

.SliderRange {
  position: absolute;
  background-color: #c9cdd3;
  border-radius: 9999px;
  height: 100%;
}

.SliderThumb {
  display: block;
  width: 16px;
  height: 16px;
  background-color: #2fb8ff;
  border-radius: 8px;
}

.SliderThumb:focus {
  outline: none;
}

.SliderTicks,
.SliderMarks {
  position: absolute;
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.SliderTick {
  width: 8px;
  height: 8px;
  background-color: #c9cdd3;
  border-radius: 4px;
}

.SliderMarks {
  top: 24px;
}

.SliderMark {
  font-size: 13px;
  color: #30333f;
}

.SliderMarkActive {
  color: #2fb8ff;
}
