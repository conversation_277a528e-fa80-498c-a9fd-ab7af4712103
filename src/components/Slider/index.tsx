import { useState, useEffect } from 'react'
import * as SliderPrimitive from '@radix-ui/react-slider'
import './index.css'

export type SliderProps = {
  defaultValue?: number[]
  value?: number[]
  min?: number
  max: number
  disabled?: boolean
  onValueCommit?(value: number[]): void
  marks?: {
    value: number
    label: string
  }[]
}

export default function Slider(props: SliderProps) {
  const { min = 0, onValueCommit, ...rest } = props

  const [innerValue, setInnerValue] = useState(() => {
    if (props.value !== undefined) {
      return props.value
    }
    if (props.defaultValue !== undefined) {
      return props.defaultValue
    }
    return [min]
  })

  useEffect(() => {
    if (props.value !== undefined) {
      setInnerValue(props.value)
    }
  }, [props.value])

  const handleValueCommit = (value: number[]) => {
    if (props.value === undefined) {
      setInnerValue(value)
    }
    onValueCommit?.(value)
  }

  const ticks = []
  const marks = []
  for (let i = min; i <= props.max; i++) {
    ticks.push(<span className="SliderTick" key={i}></span>)
    const label = props.marks?.find((item) => item.value === i)?.label
    marks.push(
      <span
        className={`SliderMark ${innerValue && innerValue[0] === i ? 'SliderMarkActive' : ''}`}
        key={i}
      >
        {label}
      </span>
    )
  }

  return (
    <div className="h-11">
      <SliderPrimitive.Root
        className="SliderRoot"
        step={1}
        {...rest}
        min={min}
        onValueCommit={handleValueCommit}
      >
        <SliderPrimitive.Track className="SliderTrack">
          <SliderPrimitive.Range className="SliderRange" />
        </SliderPrimitive.Track>
        <div className="SliderTicks">{ticks}</div>
        <div className="SliderMarks">{marks}</div>
        <SliderPrimitive.Thumb className="SliderThumb" aria-label="Volume" />
      </SliderPrimitive.Root>
    </div>
  )
}
