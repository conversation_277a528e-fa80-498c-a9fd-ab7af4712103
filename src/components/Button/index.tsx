'use client';

import Link from 'next/link';
import classNames from 'classnames';

export type ButtonProps = {
  children: React.ReactNode;
  type?: 'default' | 'primary' | 'black' | 'grey';
  shape?: 'default' | 'round';
  block?: boolean;
  disabled?: boolean;
  loading?: boolean;
  htmlType?: 'button' | 'submit' | 'reset';
  onClick?: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  className?: string;
  href?: string;
  style?: any;
};

export default function Button(props: ButtonProps) {
  const {
    children,
    disabled = false,
    type = 'default',
    htmlType = 'button',
    loading = false,
    style = {},
  } = props;
  const mergedDisabled = disabled || loading;

  const handleClick: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    if (!props.onClick || mergedDisabled) return;
    props.onClick(e);
  };

  const colorVariants = {
    default: 'border border-primary bg-white text-primary',
    primary: 'bg-gradient-to-r from-[#2FB8FF] from-[36%] to-[#9EECD9] to-[100%] text-white',
    black: 'bg-gradient-to-r from-[#585C78] from-[17%] to-[#30333F] to-[84%] text-white',
    grey: 'border border-color-text bg-gradient-to-l from-[#C9CDD3] via-[rgba(222,225,230,0.71)] via-[56%] to-[rgba(238,241,244,0.49)] text-color-text',
  };

  const cls = classNames(
    'relative inline-block rounded-lg p-3 text-center text-xl leading-6 box-border',
    colorVariants[type],
    props.className,
    {
      '!rounded-full': props.shape === 'round',
      ' w-full': props.block,
      'opacity-40': mergedDisabled,
    }
  );

  if (props.href !== undefined) {
    return (
      <Link className={cls} href={props.href}>
        <span>{children}</span>
      </Link>
    );
  }

  return (
    <button
      className={cls}
      style={style}
      onClick={handleClick}
      type={htmlType}
      disabled={mergedDisabled}
    >
      {loading && (
        <svg
          className="mr-2 h-5 w-5 animate-spin text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      <span>{children}</span>
      <span
        className={`absolute inset-0 active:bg-black/10 ${
          props.shape === 'round' ? 'rounded-full' : 'rounded-lg'
        } `}
      />
    </button>
  );
}
