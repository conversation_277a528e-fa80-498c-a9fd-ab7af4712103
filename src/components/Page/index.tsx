'use client';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { NavBar, Modal, Dialog } from 'antd-mobile';
import { LeftOutline } from 'antd-mobile-icons';
import classNames from 'classnames';
import SafeArea from '@/components/SafeArea';
import { useScroll, animated } from '@react-spring/web';
import { useNative } from '@/lib/native';
import ClientTracker from '@/components/clientTracker'; // 引入埋点组件
import ClientTrackerClick from '@/components/clientTrackerClick';

export type PageProps = {
  children: React.ReactNode;
  style?: React.CSSProperties;
  navDivStyle?: React.CSSProperties;
  className?: string;
  nav?: boolean;
  navStyle?: 'black' | 'white';
  navBgColor?: 'transparent' | string;
  backArrow?: React.ReactNode;
  onBack?: () => void;
  title?: string;
  right?: React.ReactNode;
  safeArea?: boolean;
  showTitle?: boolean;
};

export default function Page({
  children,
  style,
  className,
  nav = true,
  navDivStyle,
  navStyle = 'black',
  navBgColor = 'transparent',
  title,
  backArrow,
  onBack,
  right,
  safeArea,
  showTitle = true,
}: PageProps) {
  const router = useRouter();
  const {
    safeArea: { top },
  } = useNative();

  const handleBack = () => {
    // 自定义点击行为
    if (onBack) {
      onBack();
    } else {
      // 默认返回上一页
      router.back();
    }
  };

  const cls = classNames('relative flex h-screen flex-col', className);
  const { scrollY } = useScroll();

  useEffect(() => {
    const color = navStyle === 'black' ? 'dark' : 'light';
    window.jsBridgeHelper?.sendMessage('statusBarColor', { color });
  }, [navStyle]);

  useEffect(() => {
    // 清除弹窗
    return () => {
      Modal.clear();
      Dialog.clear();
    };
  }, []);

  return (
    <>
      <div className={cls} style={style}>
        {nav && (
          <div>
            <div style={{ height: 44 + top }} />
            <animated.div
              className="fixed left-0 right-0 top-0 z-50"
              style={{
                ...navDivStyle,
                backgroundColor: scrollY.to(
                  [0, 120],
                  navBgColor === 'transparent'
                    ? ['rgba(238, 241, 244, 0)', 'rgba(238, 241, 244, 1)']
                    : [navBgColor, navBgColor]
                ),
              }}
            >
              <SafeArea position="top" />
              <NavBar
                onBack={handleBack}
                backArrow={
                  backArrow || (
                    <LeftOutline fontSize={24} color={navStyle === 'white' ? '#fff' : '#333'} />
                  )
                }
                right={right}
                style={{ '--height': '44px' }}
              >
                <div className={`${navStyle === 'white' ? 'text-white' : 'text-[#333]'}`}>
                  {showTitle && title}
                </div>
              </NavBar>
            </animated.div>
          </div>
        )}
        {children}
        {safeArea && <SafeArea position="bottom" />}
      </div>
      <ClientTracker title={title} />
      <ClientTrackerClick title={title}/> 
    </>
  );
}
