@import './var.css';
@import './tailwind.css';

html,
body {
  height: 100%;
}

/* antd-mobile 主题 */
.adm-form .adm-form-footer {
  @apply !p-3;
}

.adm-list-item-content {
  @apply !border-t-0;
}

.adm-form-item-label {
  @apply !text-color-text;
}

/* 骑行记录 */
.riding .adm-tabs-header {
  @apply !border-b-0;
}

/* 无样式表单 */
.adm-list-default .adm-list-body {
  @apply !border-b-0 !border-t-0;
}
.adm-list-body {
  @apply !bg-transparent;
}

/* 左滑删除 */
.adm-swipe-action {
  @apply rounded-2xl;
}
.adm-swipe-action-actions {
  .adm-button:first-child {
    background-color: var(--adm-color-primary);
  }
  .adm-button:last-child {
    background-color: #ff453a !important;
  }
}

.page-type-container {
  /* margin-top:20px !important; */
  z-index: 10 !important;
  background: url(../../public/images/limaBg.png) center center/100% 100% no-repeat !important;
  /* background: url(../../public/images/limaBg2.png) center center/100% 100% no-repeat !important; */
}
/* // 此处可修改导航栏整体样式：字体大小，字体颜色，背景颜色等 */
.page-type-container .nav {
  padding: 4vh 4vw 0 4vw !important;
  color: #fff !important;
  /* padding-top: 20px !important; */
}

/* // 此处可修改导航栏返回按钮的样式: 高度、宽度等 */
.page-type-container .nav .nav-back-icon-img {
  color: #fff !important;
  filter: brightness(100);
}

/* // 此处可修改导航栏标题样式: 字体大小，颜色等 */
.page-type-container .nav .nav-title {
  color: #fff !important;
}

/* // 此处可修改logo容器的样式: 宽度 高度，圆角等 */
.page-type-container .logo {
  width: 50px !important;
}

/* // 此处修改电话号码的样式： 颜色、字体大小等 */
.page-type-container .number-con {
  color: #fff !important;
  font-size: 24px !important;
}

/* // 此处修改电话号码掩码的样式：宽度、高度、字体颜、色间距等 */
.page-type-container .number-con input {
  color: #fff !important;
  background-color: rgba(89, 98, 103, 0.5) !important;
  border-bottom-color: #fff !important;
  font-size: 8vmin !important;
}

/* // 此处修改协议勾选、选中时的颜色 */
.page-type-container .agreement .checke-1 svg g {
  fill: white !important;
}

/* // 此处修改协议区域的样式： 字体颜色，大小等 */
.page-type-container .agreement .agree-content {
}

/* // 此处修改协议名称的样式：颜色大小等 */
.page-type-container .agreement a {
  color: #fff !important;
}

/* // 此处修改登录按钮的样式：颜色大小背景色等 */
.page-type-container .submit-btn {
  background: #ff453a !important;
  border-radius: 15px !important;
  text-align: center !important;
}

/**协议议式**/
.dialog-review-container .privacy-alert-modal .privacy-alert-btn {
  background: #ff453a !important;
}
.privacy-alert-container .page-type-tabs .page-type-list-nav span.active {
  border-bottom: 1px solid #ff453a !important;
}

/**协议议式**/
.privacy-alert-container .privacy-alert-modal .privacy-alert-btn {
  background: #ff453a !important;
}
