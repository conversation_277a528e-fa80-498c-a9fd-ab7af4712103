'use server';

import request from '@/server/request';
import { getErrorMessage } from '@/server/utils';

// 子账号列表
export async function getShareList(deviceNo: string) {
  try {
    const res = await request(`/columbia/app/subAccount?deviceNo=${deviceNo}`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 删除子账号
export async function shareDel({ deviceNo, subUserId }: { deviceNo: string; subUserId: string }) {
  try {
    const res = await request(
      `/columbia/app/subAccount/delete?deviceNo=${deviceNo}&subUserId=${subUserId}`
    );
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 邀请子账号
export async function shareAdd(data: any) {
  try {
    const res = await request(`/columbia/app/subAccount`, {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}
