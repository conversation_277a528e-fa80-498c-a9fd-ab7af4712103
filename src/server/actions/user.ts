'use server';

import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import request from '@/server/request';
import { getErrorMessage } from '@/server/utils';

// 密码登录
export async function passwordLogin(data: any) {
  try {
    const res = await request<{ phone: string; token: string }>('/auth/login/app', {
      method: 'POST',
      body: data,
    });
    cookies().set('token', res.token);
    cookies().set('phone', res.phone);
  } catch (error) {
    return getErrorMessage(error);
  }
  redirect('/');
}

// 手机验证码登录
export async function codeLogin(data: any) {
  try {
    const res = await request<{ phone: string; token: string }>('/auth/app/v2/loginByCode', {
      method: 'POST',
      body: data,
    });
    cookies().set('token', res.token);
    cookies().set('phone', res.phone);
  } catch (error) {
    return getErrorMessage(error);
  }
  redirect('/');
}

// 获取手机验证码
export async function getCodeByPhone({ phone, sign }: { phone: string; sign: string }) {
  try {
    await request(`/challenger/auth/app/getcodeByPhone?phone=${phone}&sign=${sign}`);
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 忘记密码
export async function passwordForget(data: any) {
  try {
    await request<{ phone: string; token: string }>(`/auth/app/forgetPassword`, {
      method: 'POST',
      body: data,
    });
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 手机号注册
export async function phoneRegister(data: any) {
  try {
    const res = await request<{ phone: string; token: string }>('/auth/signIn/app', {
      method: 'POST',
      body: data,
    });
    cookies().set('token', res.token);
    cookies().set('phone', res.phone);
  } catch (error) {
    return getErrorMessage(error);
  }
  redirect('/');
}

// 第三方是否注册
export async function isRegister(data: { wxOpenid?: string; appleOpenid?: string }) {
  try {
    const res = await request<boolean>('/app/v2/third/isRegister', {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 第三方登录
export async function thirdLogin(data: any) {
  try {
    const res = await request<{ phone: string; token: string }>('/app/v2/third/login', {
      method: 'POST',
      body: data,
    });
    cookies().set('token', res.token);
    cookies().set('phone', res.phone);
  } catch (error) {
    return getErrorMessage(error);
  }
  redirect('/');
}

// 第三方注册
export async function thirdRegister(data: any) {
  try {
    const res = await request<{ phone: string; token: string }>('/app/v2/third/register', {
      method: 'POST',
      body: data,
    });
    cookies().set('token', res.token);
    cookies().set('phone', res.phone);
  } catch (error) {
    return getErrorMessage(error);
  }
  redirect('/');
}

// app用户修改手机
export async function changePhone(data: any) {
  try {
    const res = await request('/challenger/app/changePhone', {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 修改密码
export async function changePassword(data: any) {
  try {
    const res = await request('/challenger/app/changePassword', {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

//用户注销
export async function doCancellation(data: any) {
  try {
    const res = await request('/challenger/app/v2/doCancellation', {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

//用户修改手机校验
export async function changePhoneForCheck(data: any) {
  try {
    const res = await request('/challenger/app/changePhoneForCheck', {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 获取 accessToken, jwtToken
export async function getAuthTokenDypns() {
  try {
    const res = await request('/dypns/getAuthToken', {
      method: 'GET',
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 授权登录
export async function dypnsLogin(data: any) {
  try {
    const res = await request('/dypns/login', {
      method: 'POST',
      body: data,
    });
    cookies().set('token', res.token);
    cookies().set('phone', res.phone);
  } catch (error) {
    return getErrorMessage(error);
  }
  redirect('/');
}

// 邮箱验证码登录
export async function emailCodeLogin(data: any) {
  try {
    const res = await request<{ token: string; phone: string }>('/auth/app/bindPhoneByEmailAndCode', {
      method: 'POST',
      body: data,
    });
    cookies().set('token', res.token);
    cookies().set('phone', res.phone);
  } catch (error) {
    return getErrorMessage(error);
  }
  redirect('/');
}


// 获取邮箱验证码
export async function getCodeByEmail({ email }: { email: string }) {
  try {
    await request(`/auth/app/getcodeByEmail?email=${email}`);
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 邮箱绑定手机号
export async function emailRegister(data: any) {
  try {
    const res = await request<{ phone: string; token: string; }>('/auth/app/bindEmailAndPhone', {
      method: 'POST',
      body: data,
    });
    cookies().set('token', res.token);
    cookies().set('phone', res.phone);
    
  } catch (error) {
    return getErrorMessage(error);
  }
  redirect('/');
}
