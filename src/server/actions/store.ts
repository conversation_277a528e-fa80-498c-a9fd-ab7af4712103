'use server';

import request from '@/server/request';
import { getErrorMessage } from '@/server/utils';

// 网点收藏
export async function storeCollect(id: string) {
  try {
    await request(`/challenger/app/store/collect?storeId=${id}`);
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 网点取消收藏
export async function storeCancelCollect(id: string) {
  try {
    await request(`/challenger/app/store/cancleCollect?storeId=${id}`);
  } catch (error) {
    return getErrorMessage(error);
  }
}
