'use server';

import request from '@/server/request';
import { getErrorMessage } from '@/server/utils';

// 资讯详情
export async function getNewsInfo(newsId: string) {
  try {
    const res = await request(`/challenger/app/v2/appNewsInfo?newsId=${newsId}`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}
// 资讯收藏
export async function getNewsOpt(newsId: string, state: string) {
  try {
    const res = await request(`/challenger/app/v2/appNewsOpt?newsId=${newsId}&state=${state}`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}
