'use server';

import request from '@/server/request';
import { getErrorMessage } from '@/server/utils';
import type { InviteInfo } from '../api/types';

// 获取邀请信息
export async function getInfo() {
  const res = await request<InviteInfo>('/columbia/app/subAccount/me');
  return res;
}

// 绑定校验
export async function checkBind(deviceNo: string) {
  try {
    const res = await request<{
      existSn: boolean;
      isBind: boolean;
      identifier: string;
      deviceNo: string;
      isLowVersion: boolean;
    }>(`/columbia/app/v2/checkBind/${deviceNo}`, { method: 'POST' });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 车辆信息校验
export async function checkVehicleInfo(data: {
  deviceNo: string;
  identity: string;
  brandId: number;
}) {
  try {
    await request(`/columbia/app/v2/checkVehicle`, {
      method: 'POST',
      body: data,
    });
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 检查设备编号
export async function checkEquNo(equNo: string, deviceNo: string) {
  try {
    const res = await request<{ isLowVersion: boolean }>(
      `/columbia/app/v2/checkEqu?equNo=${equNo}&deviceNo=${deviceNo}`
    );
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 绑定
export async function bindVehicle(data: any) {
  try {
    await request('/columbia/app/v2/bind', {
      method: 'POST',
      body: data,
    });
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 下发指令
export async function sendCommand(data: { deviceNo: string; status: number; type: number }) {
  try {
    await request('/columbia/app/commandDown', {
      method: 'POST',
      body: data,
    });
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 蓝牙数据上报
export async function postBluetoothData<T>(data: any) {
  try {
    await request<T>('/columbia/app/v2/blueReport', {
      method: 'POST',
      body: data,
    });
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 上传
export async function uploadImage(data: any) {
  try {
    const res = await request<{ url: string }>('/mars/image/upload/challenger/applogo', {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}
