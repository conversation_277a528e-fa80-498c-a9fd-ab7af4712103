'use server';

import request from '@/server/request';
import { getErrorMessage } from '@/server/utils';

// 电池 详情
export async function getBatteryDetail(id: string, deviceNo: string) {
  try {
    const res = await request(`/columbia/app/batteryInfo?id=${id}&deviceNo=${deviceNo}`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 电池 列表
export async function getBatteryList(deviceNo: string) {
  try {
    const res = await request(`/columbia/app/batteryList?deviceNo=${deviceNo}`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 电池 电池详情汇总
export async function getBatterySum(deviceNo: string) {
  try {
    const res = await request(`/columbia/app/batteryInfoSum?deviceNo=${deviceNo}`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 电池 电量变化曲线
export async function getBatteryChart(data: any) {
  try {
    const res = await request(`/columbia/app/batteryAnanlysis`, {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 电池 解绑
export async function getBatteryUnBind(id: string) {
  try {
    const res = await request(`/columbia/app/batteryUnBind?id=${id}`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 电池 停止/解除充电
export async function getBatteryCharge({ deviceNo, state }: { deviceNo: string; state: number }) {
  try {
    const res = await request(`/columbia/app/optCharger?deviceNo=${deviceNo}&state=${state}`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 电池 电池规格
export async function getBatteryUpdate(data: any) {
  try {
    const res = await request(`/columbia/app/updateBattery`, {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 电池 提醒修改
export async function getBatteryOptWarn(data: any) {
  try {
    const res = await request(`/columbia/app/optWarn`, {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}
