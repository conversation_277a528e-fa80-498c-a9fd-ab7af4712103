'use server';

import request from '@/server/request';
import { getErrorMessage } from '@/server/utils';

//  开始 车辆体检
export async function vehicleMedical(deviceNo: string) {
  try {
    const res = await request(`/columbia/app/command/vehicleMedical?deviceNo=${deviceNo}`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 获取上一次体检报告
export async function getMedicalReport(deviceNo: string) {
  try {
    const res = await request(`/columbia/app/v1/getMedicalReport?deviceNo=${deviceNo}`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// app获取车辆到期时间
export async function getExpired(deviceNo: string) {
  try {
    const res = await request(`/columbia/app/v1/getExpired?dvcNo=${deviceNo}`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 说明书列表
export async function bookList(data: any) {
  try {
    const res = await request(`/columbia/app/manual/list`, {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 支付宝支付
export async function aliPay(data: any) {
  try {
    const res = await request<{ body: string }>(`/challenger/app/fund/alipay/app`, {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 微信支付
export async function wechatPay(data: any) {
  try {
    const res = await request<{
      package: string;
      appid: string;
      sign: string;
      partnerid: string;
      prepayid: string;
      noncestr: string;
      timestamp: number;
    }>(`/challenger/app/fund/wxpay/unifiedorder`, {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}
