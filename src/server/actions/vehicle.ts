/*
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-03-16 13:48:22
 * @Email: <EMAIL>
 */
'use server'

import request from '@/server/request'
import { getErrorMessage } from '@/server/utils'

// 车辆设置
export async function setVehicleSetting(data: {
  vehicleSetType?: number
  commandType: number
  [index: string]: string | number | undefined
}) {
  try {
    await request('/columbia/app/vehicleSet', { method: 'POST', body: data })
  } catch (error) {
    return getErrorMessage(error)
  }
}

// vcu车辆设置
export async function setVcuVehicleSetting(data: {
  vehicleSetType?: number
  commandType: number
  [index: string]: string | number | undefined
}) {
  try {
    await request('/columbia/app/vcu/vehicleSet', { method: 'POST', body: data })
  } catch (error) {
    return getErrorMessage(error)
  }
}

// 解绑车辆
export async function getBindCancel(data: any) {
  try {
    const res = await request(`/columbia/back/dvc/bindCancel`, {
      method: 'POST',
      body: data
    })
    return res
  } catch (error) {
    return getErrorMessage(error)
  }
}

// 修改车辆名称
export async function updateVehicleName(data: { deviceNo: string; nickName: string }) {
  try {
    await request(`/columbia/app/v1/changeNick`, {
      method: 'POST',
      body: data
    })
  } catch (error) {
    return getErrorMessage(error)
  }
}

// 修改车辆品牌
export async function getUpdateVehicleBrand({ id, brandId }: { id: number; brandId: string }) {
  try {
    const res = await request(`/columbia/app/updateVehicleBrand?id=${id}&brandId=${brandId}`)
    return res
  } catch (error) {
    return getErrorMessage(error)
  }
}

// 盲区监测-设置
export async function postblindZoneState<T>(data: any) {
  try {
    await request<T>('/columbia/app/v2/vehicle/blindZoneState', {
      method: 'POST',
      body: data
    })
  } catch (error) {
    return getErrorMessage(error)
  }
}

// 充电状态-设置
export async function postChargeState<T>(data: any) {
  try {
    await request<T>('/columbia/app/v2/battery/charge', {
      method: 'POST',
      body: data
    })
  } catch (error) {
    return getErrorMessage(error)
  }
}

// 自定义模式-添加
export async function postCustomMode<T>(data: any) {
  try {
    await request<T>('/columbia/app/v2/vehicle/rideMode', {
      method: 'POST',
      body: data
    })
  } catch (error) {
    return getErrorMessage(error)
  }
}

// 自定义模式-更新
export async function updateCustomMode<T>(data: any) {
  try {
    await request<T>('/columbia/app/v2/vehicle/rideMode', {
      method: 'POST',
      body: data
    })
  } catch (error) {
    return getErrorMessage(error)
  }
}

// 自定义模式-删除
export async function deleteCustomMode<T>(data: any) {
  try {
    await request<T>('/columbia/app/v2/vehicle/rideMode/del', {
      method: 'POST',
      body: data
    })
  } catch (error) {
    return getErrorMessage(error)
  }
}

// 指纹-添加（蓝牙版）
export async function postFingerBluetooth<T>(data: any) {
  try {
    await request<T>('/columbia/app/v2/dvcKey/finger/bluetooth', {
      method: 'POST',
      body: data
    })
  } catch (error) {
    return getErrorMessage(error)
  }
}

// 指纹-指纹名称修改
export async function updateFingerName<T>(data: any) {
  try {
    await request<T>('/columbia/app/v2/dvcKey/finger/name', {
      method: 'POST',
      body: data
    })
  } catch (error) {
    return getErrorMessage(error)
  }
}
// 指纹-删除
export async function deleteFinger<T>(data: any) {
  try {
    await request<T>('/columbia/app/v2/dvcKey/finger/net/del', {
      method: 'POST',
      body: data
    })
  } catch (error) {
    return getErrorMessage(error)
  }
}

// 整车模式-出差模式开关
export async function postSortMode<T>(data: any) {
  try {
    await request<T>('/columbia/app/v2/vehicle/sortMode', {
      method: 'POST',
      body: data
    })
  } catch (error) {
    return getErrorMessage(error)
  }
}
