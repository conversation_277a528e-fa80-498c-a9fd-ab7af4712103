'use server';

import request from '@/server/request';
import { getErrorMessage } from '@/server/utils';

// 推送设置
export async function getPushSet(data: any) {
  try {
    const res = await request(`/challenger/app/pushSet`, {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 用户个人信息
export async function getUserInfo() {
  try {
    const res = await request(`/challenger/app/getUserInfo`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}
// 编辑用户信息
export async function toEditInfo(data: any) {
  try {
    const res = await request(`/challenger/app/editInfo`, {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 添加反馈
export async function addFeedback(data: any) {
  try {
    const res = await request('/challenger/app/feedback/add', {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 收藏网点获取

export async function getStoreCollect(lng: string, lat: string) {
  try {
    const res = await request(`/challenger/app/store/getCollect?lng=${lng}&lat=${lat}`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 收藏的资讯
export async function getNewsCollect() {
  try {
    const res = await request(`/challenger/app/v2/appNewsCollect`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 获取原用户推送设置
export async function getNoticeSet() {
  try {
    const res = await request(`/challenger/app/noticeSet/get`);
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 判断第三方是否已经注册
export async function toIsRegister() {
  try {
    const res = await request('/app/v2/third/isRegister', {
      method: 'POST',
      body: {},
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 版本更新
export async function getAppUp(appOpration: string, vesionCode: string) {
  try {
    const res = await request(
      `/challenger/app/appversion/up?appOpration=${appOpration}&vesionCode=${vesionCode}`
    );
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 第三方账号绑定
export async function toBindThird(data: any) {
  try {
    const res = await request('/challenger/app/v2/third/bindThird', {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 身份证二要素认证
export async function id2MetaVerify(data: any) {
  try {
    const res = await request('/challenger/app/v2/id2MetaVerify', {
      method: 'POST',
      body: data,
    });
    return res;
  } catch (error) {
    return getErrorMessage(error);
  }
}
