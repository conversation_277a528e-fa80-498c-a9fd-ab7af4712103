import apiFetch from '@/server/client';

// 获取车辆到期时间
export async function getExpireTime(deviceNo: string) {
  const res = await apiFetch<any>(`/columbia/app/v1/getExpired?dvcNo=${deviceNo}`);
  return res;
}

// 获取智能服务套餐
export async function getServiceList() {
  const res = await apiFetch<any>('/columbia/app/v1/servingList', {
    method: 'POST',
    body: {
      page: 1,
      pageSize: 100,
    },
  });
  return res;
}
