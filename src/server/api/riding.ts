import apiFetch from '@/server/client';

// 获取骑行记录
export async function getRidingRecord(data: {
  deviceNo: string;
  searchBgnTime: string;
  searchEndTime: string;
}) {
  const res = await apiFetch<{
    avgSpeed: number;
    dayHistory: any[];
    haveTime: number;
    highSpeed: number;
    history: any[];
    mileage: number;
    time: number;
    totalMileage: number;
    powerConsume: number;
    dayMap: Record<string, number>;
  }>('/columbia/app/v1/newRidingData', {
    method: 'POST',
    body: data,
  });
  return res;
}

// 获取骑行轨迹
export async function getRidingTrack(id: string) {
  const res = await apiFetch<{
    powerConsumer: number;
    mile: number;
    retGpsList: string[];
    rideTime: number;
    highSpeed: number;
    gmtStart: number;
    avgSpeed: number;
  }>(`/columbia/app/v1/getTrack?id=${id}`);
  return res;
}
