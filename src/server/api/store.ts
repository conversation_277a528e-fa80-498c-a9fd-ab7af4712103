import apiFetch from '@/server/client';

// 获取服务网点
export async function getStore(lng: number, lat: number, storeName?: string) {
  const res = await apiFetch<any[]>(`/challenger/dealer/getStoreByGps?lng=${lng}&lat=${lat}${storeName ? '&storeName=' + storeName : ''}`);
  return res;
}

// 网点详情
export async function getStoreDetail(id: string) {
  const res = await apiFetch<{ store: Record<string, any> }>(
    `/challenger/dealer/getStoreDetail?storeId=${id}`
  );
  return res;
}
