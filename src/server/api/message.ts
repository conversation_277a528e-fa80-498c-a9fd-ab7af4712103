import apiFetch from '@/server/client';

// 消息
export async function getMessage(data: any) {
  const res = await apiFetch<any>(`/challenger/app/notice/page`, {
    method: 'POST',
    body: data,
  });
  return res;
}

// 车辆消息
export async function getMessageDvc(data: any) {
  const res = await apiFetch<any>(`/challenger/app/dvcNotice`, {
    method: 'POST',
    body: data,
  });
  return res;
}

// 是否有新的消息
export async function getNewNotice() {
  const res = await apiFetch<{
    other: boolean;
    activity: boolean;
    notify: boolean;
    device: boolean;
  }>(`/challenger/app/newNotice`);
  return res;
}

// 是否有新的消息 (设备)
export async function getDeviceNewNotice() {
  const res = await apiFetch<{
    bindDevice: any[];
    dot: string[];
    // 允许任意其他属性，值类型为 unknown
    [key: string]: any;
  }>(`/challenger/app/v2/newDeviceNotice`);
  return res;
}

// 是否有新的消息 (非设备)
export async function getSimpleNewNotice() {
  const res = await apiFetch<{
    // 是否有新消息
    other: any;
    activity: any;
    notify: any;
    device: any;
    news: any;
    dot: string[];
  }>(`/challenger/app/v2/newSimpleNotice`);
  return res;
}

// 全部已读
export async function readAllNotice(data: {deviceDots: string[], simpleDots: string[]}) {
  return await apiFetch<{}>(`/challenger/app/v2/readAllNotice`, {
    method: 'POST',
    body: data,
  });
}

