/*
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-03-12 10:56:35
 */
import apiFetch from '@/server/client'
import type { VehicleDetail, VehicleStatus } from './types'

// 获取车辆设置
export async function getVehicleSetting<T>(deviceNo: string) {
  const res = await apiFetch<T>(`/columbia/app/vehicleSetInfo?deviceNo=${deviceNo}`)
  return res
}

// 获取车辆详情
export async function getVehicleDetail(deviceNo: string) {
  const res = await apiFetch<VehicleDetail>(
    `/columbia/app/v1/vehicleManegeDetail?deviceNo=${deviceNo}`
  )
  return res
}

// 获取车况信息
export async function getVehicleStatus(deviceNo: string) {
  const res = await apiFetch<VehicleStatus>(
    `/columbia/app/v1/vehicleCondition?deviceNo=${deviceNo}`
  )
  return res
}

// 盲区监测-状态
export async function getblindZoneState(params: { deviceNo: string; clientId: string }) {
  const res = await apiFetch<any>(
    `/columbia/app/v2/vehicle/blindZoneState?deviceNo=${params.deviceNo}&clientId=${params.clientId}`
  )
  return res
}

// 充电设置-状态
export async function getChargeState(params: { deviceNo: string; clientId: string }) {
  const res = await apiFetch<any>(
    `/columbia/app/v2/battery/charge?deviceNo=${params.deviceNo}&clientId=${params.clientId}`
  )
  return res
}

// 轮胎-胎压胎温实时数据
export async function getTirePressureData(params: { deviceNo: string }) {
  const res = await apiFetch<any>(`/columbia/app/v2/vehicle/tireInfo?deviceNo=${params.deviceNo}`)
  return res
}

// 轮胎-胎压胎温历史数据
export async function getTirePressureHistoryData(params: { deviceNo: string }) {
  const res = await apiFetch<any>(
    `/columbia/app/v2/vehicle/tireHistoryInfo?deviceNo=${params.deviceNo}`
  )
  return res
}

// 自定义模式X列表-状态
export async function getCustomModeList(params: { deviceNo: string; clientId: string }) {
  const res = await apiFetch<any>(
    `/columbia/app/v2/vehicle/rideMode?deviceNo=${params.deviceNo}&clientId=${params.clientId}`
  )
  return res
}

// 自定义模式X单个-状态
export async function getCustomMode(params: { deviceNo: string; id: string }) {
  const res = await apiFetch<any>(
    `/columbia/app/v2/vehicle/rideModeDetail?deviceNo=${params.deviceNo}&id=${params.id}`
  )
  return res
}

//指纹列表
export async function getFingerList(params: { deviceNo: string; clientId: string }) {
  const res = await apiFetch<any>(
    `/columbia/app/v2/dvcKey/finger?deviceNo=${params.deviceNo}&clientId=${params.clientId}`
  )
  return res
}

// 指纹-添加（网络版）
export async function postFingerNet(params: { deviceNo: string; clientId: string }) {
  const res = await apiFetch<any>(
    `/columbia/app/v2/dvcKey/finger/net?deviceNo=${params.deviceNo}&clientId=${params.clientId}`
  )
  return res
}

// SOS 获取车辆详情
export async function getVehicleDetailSos(deviceNo: string) {
  const res = await apiFetch<VehicleDetail>(
    `/columbia/app/v2/sos/vehicleManegeDetail?deviceNo=${deviceNo}`
  )
  return res
}

// SOS 获取车况信息
export async function getVehicleStatusSos(deviceNo: string) {
  const res = await apiFetch<VehicleStatus>(
    `/columbia/app/v2/sos/vehicleCondition?deviceNo=${deviceNo}`
  )
  return res
}
