import apiFetch from '@/server/client';
import { getErrorMessage } from '@/server/utils';
import type {
  InviteInfo,
  VehicleItem,
  VehicleDetail,
  VehicleStatus,
  VehicleRideRecord,
} from './types';

// 获取当前车辆和邀请信息
export async function getInfo() {
  const res = await apiFetch<InviteInfo>('/columbia/app/subAccount/me');
  return res;
}

// 同意/拒绝邀请
export async function replyInvite(params: { deviceNo: string; type: number }) {
  const res = await apiFetch(
    `/columbia/app/subAccount/reply?deviceNo=${params.deviceNo}&type=${params.type}`
  );
  return res;
}

// 切换车辆
export async function switchVehicle(deviceNo: string) {
  const res = await apiFetch(`/challenger/app/carSet?dvcNo=${deviceNo}`);
  return res;
}

// 绑定
export async function bindVehicle(data: any) {
  try {
    await apiFetch('/columbia/app/v2/bind', {
      method: 'POST',
      body: data,
    });
  } catch (error) {
    return getErrorMessage(error);
  }
}

// 车辆列表
export async function getVehicles() {
  const res = await apiFetch<VehicleItem[]>('/columbia/app/v1/bindList');
  return res;
}

// 车辆详情
export async function getVehicleDetail(deviceNo: string) {
  const res = await apiFetch<VehicleDetail>(
    `/columbia/app/v1/vehicleManegeDetail?deviceNo=${deviceNo}`
  );
  return res;
}

// 车况
export async function getVehicleStatus(deviceNo: string) {
  const res = await apiFetch<VehicleStatus>(
    `/columbia/app/v1/vehicleCondition?deviceNo=${deviceNo}`
  );
  return res;
}

// 最近一条骑行记录
export async function getLatestRideRecord(deviceNo: string) {
  const res = await apiFetch<VehicleRideRecord>(
    `/columbia/app/v1/historyLast?deviceNo=${deviceNo}`
  );
  return res;
}

// 获取原用户推送设置
export async function getNoticeSet() {
  const res = await apiFetch<any>(`/challenger/app/noticeSet/get`);
  return res;
}

// 蓝牙数据上报
export async function postBluetoothData(data: any) {
  const res = await apiFetch('/columbia/app/v2/blueReport', {
    method: 'POST',
    body: data,
  });
  return res;
}

// 获取品牌列表
export async function getBrands() {
  const res = await apiFetch<{ id: number; brandName: string }[]>('/columbia/app/v1/getAllBrand');
  return res;
}

// 钥匙列表
export async function getKeys(deviceNo: string) {
  const res = await apiFetch<any[]>(`/columbia/app/v2/dvcKey/list/${deviceNo}`);
  return res;
}

// 添加钥匙
export async function addKey(data: { deviceNo: string; name: string; type: number; mac: string }) {
  const res = await apiFetch('/columbia/app/v2/dvcKey/add', { method: 'POST', body: data });
  return res;
}

// 删除钥匙
export async function deleteKey(id: string) {
  const res = await apiFetch(`/columbia/app/v2/dvcKey/delete/${id}`);
  return res;
}

// 修改钥匙
export async function updateKey(data: any) {
  const res = await apiFetch('/columbia/app/v2/dvcKey/edit', {
    method: 'POST',
    body: data,
  });
  return res;
}

// 获取感应解锁状态
export async function getHidSet(params: { deviceNo: string; clientId: string }) {
  const res = await apiFetch<any>(
    `/columbia/app/v2/unlockAutoGet?deviceNo=${params.deviceNo}&clientId=${params.clientId}`
  );
  return res;
}

// 设备信息
export async function getDeviceInfo(deviceNo: string) {
  const res = await apiFetch<any>(`/columbia/app/equInfo?deviceNo=${deviceNo}`);
  return res;
}

// 更换设备
export async function updateDevice(data: {
  oldEquId?: string;
  newEquNo: string;
  bluetoothMac?: string;
  dvcId: string;
}) {
  const res = await apiFetch('/columbia/app/updateEqu', { method: 'POST', body: data });
  return res;
}

// 删除设备
export async function deleteDevice(id: number) {
  const res = await apiFetch(`/columbia/app/deleteEqu?id=${id}`);
  return res;
}

// 检查设备是否有固件更新
export async function checkOta(deviceNo: string) {
  const res = await apiFetch<any>(`/columbia/app/v1/checkOta?dvcNo=${deviceNo}`);
  return res;
}

// 触发固件升级任务
export async function startOta(params: {
  dvcNo: string;
  otaNo: string;
  version: string;
  otaId: number;
}) {
  const res = await apiFetch<any>(
    `/columbia/app/v1/otaUpdate?dvcNo=${params.dvcNo}&otaNo=${params.otaNo}&version=${params.version}&otaId=${params.otaId}`
  );
  return res;
}

// 查询irk
export async function getIrk(params: { deviceNo: string; irk: string }) {
  const res = await apiFetch<any>(
    `/columbia/app/v2/deviceIrkDeal?deviceNo=${params.deviceNo}&irk=${params.irk}`
  );
  return res;
}

// 删除irk
export async function deleteIrk(params: { deviceNo: string; irkString: string }) {
  const res = await apiFetch<any>(
    `/columbia/app/v2/deleteIrk?deviceNo=${params.deviceNo}&irkString=${params.irkString}`
  );
  return res;
}

/// 蓝牙中控电池数据上报
export async function updateBleBatteryData(params: { equiNo: string; cmd: string }) {
  const res = await apiFetch<any>(`/data/app/uplink?equiNo=${params.equiNo}&cmd=${params.cmd}`);
  return res;
}
