import cookies from 'js-cookie';

interface CodeMessage {
  [key: number]: string;
}

const codeMessage: CodeMessage = {
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

interface ApiRequest extends Omit<RequestInit, 'body'> {
  body?: BodyInit | Record<string, any>;
}

/**
 * Requests a URL, returning a promise.
 *
 * @param  path The URL we want to request
 * @param  options The options we want to pass to "fetch"
 * @return An object containing either "data" or "err"
 */
export default async function request<T>(path: string, options?: ApiRequest) {
  const url = `${process.env.NEXT_PUBLIC_APP_URL}${path}`;

  const newOptions: ApiRequest = { ...options };

  if (cookies.get('token')) {
    const token = cookies.get('token');
    newOptions.headers = {
      Authorization: `${token}`,
      ...newOptions.headers,
    };
  }

  if (
    newOptions.method === 'POST' ||
    newOptions.method === 'PUT' ||
    newOptions.method === 'DELETE'
  ) {
    if (!(newOptions.body instanceof FormData)) {
      newOptions.headers = {
        Accept: 'application/json',
        'Content-Type': 'application/json; charset=utf-8',
        ...newOptions.headers,
      };
      // console.log('请求参数 :>> ', JSON.stringify(newOptions.body));
      newOptions.body = JSON.stringify(newOptions.body);
    } else {
      // newOptions.body is FormData
      newOptions.headers = {
        Accept: 'application/json',
        ...newOptions.headers,
      };
    }
  }

  const response = await fetch(url, newOptions as RequestInit);

  const json = await response.json();
  console.group();
  console.log('[请求地址] :>>', url);
  // console.log('[请求头] :>> ', JSON.stringify(newOptions.headers));
  console.log('[请求参数] :>> ', JSON.stringify(newOptions.body));
  console.log('[请求结果] :>> ', JSON.stringify(json));
  console.groupEnd();

  // the status code is out of the range of 200-299
  if (!response.ok) {
    return Promise.reject({
      code: response.status,
      message: codeMessage[response.status] || response.statusText,
    });
  }

  if ('code' in json && json.code !== '8001') {
    return Promise.reject({ code: json.code, message: json.msg });
  }

  return json.data as T;
}
