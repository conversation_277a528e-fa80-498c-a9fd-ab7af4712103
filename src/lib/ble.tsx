'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import type { BLEClient } from './ble-jssdk1.0';
import BLE from './ble-jssdk1.0';

export const BLEContext = createContext<BLEClient | undefined>(undefined);

export const useBLEClient = () => {
  const client = useContext(BLEContext);
  if (!client) {
    throw new Error('No BLEClient set, use BleProvider to set one');
  }
  return client;
};

export const useBLEState = () => {
  const client = useBLEClient();
  const [connected, setConnected] = useState(client.isConnected());
  const [connecting, setConnecting] = useState(false);

  useEffect(() => {
    const unlistener = client.onBLEDataChange((type, result) => {
      if (type === 'connectionState') {
        setConnecting(result.connecting);
        setConnected(result.connected);
      }
    });
    return () => {
      unlistener();
    };
  }, [client]);

  return { connecting, connected };
};

export type BLEProviderProps = {
  children?: React.ReactNode;
};

export const BLEProvider = ({ children }: BLEProviderProps) => {
  const [client] = useState(() => new BLE());

  useEffect(() => {
    // Cleanup function
    return () => {
      client.disconnect();
    };
  }, [client]);

  return <BLEContext.Provider value={client}>{children}</BLEContext.Provider>;
};
