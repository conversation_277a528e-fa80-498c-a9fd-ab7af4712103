import AES from 'crypto-js/aes';
import encUft8 from 'crypto-js/enc-utf8';
import encHex from 'crypto-js/enc-hex';
import encBase64 from 'crypto-js/enc-base64';
import ECB from 'crypto-js/mode-ecb';
import nopadding from 'crypto-js/pad-nopadding';

// 加密
export function encrypt(data: string, key: string) {
  return AES.encrypt(encHex.parse(data), encHex.parse(key), {
    mode: ECB,
    iv: encUft8.parse('0000000000000000'),
    padding: nopadding,
  }).ciphertext.toString(encHex);
}

// 解密
export function decrypt(hex: string, key: string) {
  const bytes = AES.decrypt(encBase64.stringify(encHex.parse(hex)), encHex.parse(key), {
    mode: ECB,
    iv: encUft8.parse('0000000000000000'),
    padding: nopadding,
  });
  return bytes.toString();
}

/**
 * ArrayBuffer->hex
 */
export function ab2hex(buffer: ArrayBuffer): string {
  const hexArr = Array.from(new Uint8Array(buffer), (bit) => {
    return ('00' + bit.toString(16)).slice(-2);
  });
  return hexArr.join('');
}

/**
 * hex->ArrayBuffer
 */
export function hex2ab(hex: string): ArrayBuffer {
  const typedArray = new Uint8Array(
    hex.match(/[\da-f]{2}/gi)?.map((h) => {
      return parseInt(h, 16);
    }) || []
  );
  const buffer = typedArray.buffer;
  return buffer;
}

/**
 * NOT buffer 取非
 */
export function notab(buffer: ArrayBuffer): ArrayBuffer {
  const array = Array.from(new Uint8Array(buffer), (bit) => ~bit);

  const arrayBuffer = new Uint8Array(array).buffer;
  return arrayBuffer;
}

/**
 * xor 异或
 * @param buffer
 * @param key
 */
export function xorab(buffer: ArrayBuffer, key: ArrayBuffer): ArrayBuffer {
  const keyArray = new Uint8Array(key);
  const array = Array.from(
    new Uint8Array(buffer),
    (bit, index) => bit ^ keyArray[index % keyArray.length]
  );

  const arrayBuffer = new Uint8Array(array).buffer;
  return arrayBuffer;
}
