type ConnectData = {
  deviceMac: string;
  deviceName: string;
  serviceUid: string;
  writeUid: string;
  notifyUid: string;
};

type BLEConnectCallback = (res: { code: string; msg: string }) => void;
type BLEConnectionStateCallback = (res: {
  deviceName: string;
  deviceMac: string;
  state: 0 | 1 | 3;
}) => void;
type BLEDataChangeCallback = (res: { deviceName: string; deviceMac: string; data: string }) => void;
type BluetoothDevice = {
  deviceName: string;
  advertisementData: {
    manufacturerData: {
      key: string;
      value: string;
    }[];
  };
};

// 发起蓝牙搜索
export const searchBLE = (timeout: number): void => {
  window.jsBridgeHelper?.sendMessage('scan', { timeout });
};

// 停止蓝牙搜索
export const stopSearchBLE = (): void => {
  window.jsBridgeHelper?.sendMessage('stopScan');
};

// 监听蓝牙搜索结果
export const onBLEDeviceFound = (callback: (res: BluetoothDevice) => void): void => {
  window.jsBridgeHelper?.registerHandler('scanResult', callback);
};

// 创建蓝牙连接
export const connectBLE = (data: ConnectData, callback?: BLEConnectCallback): void => {
  window.jsBridgeHelper
    ?.sendMessage('connectDevice', data)
    .then((res: { code: string; msg: string }) => {
      callback?.(res);
    });
};

// 断开蓝牙连接
export const disconnectBLE = (): void => {
  window.jsBridgeHelper?.sendMessage('disconnectDevice');
};

// 监听蓝牙连接状态
export const onBLEConnectionStateChange = (callback: BLEConnectionStateCallback): void => {
  window.jsBridgeHelper?.registerHandler('bleConnectState', callback);
};

// 监听蓝牙数据
export const onBLEDataChange = (callback: BLEDataChangeCallback): void => {
  window.jsBridgeHelper?.registerHandler('bleReceiveData', callback);
};

// 发送蓝牙数据
export const sendBLEData = (data: string): void => {
  window.jsBridgeHelper?.sendMessage('sendBleData', data);
};
