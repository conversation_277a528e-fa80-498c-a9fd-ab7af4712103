import { encrypt, decrypt, notab, hex2ab, ab2hex } from './utils';

describe('decrypt', () => {
  it('should decrypt the encrypted data using the given key', () => {
    const encryptedData = '7A1E14A8903A3510B86286351399C4E2';
    const key = '4c494d41323030335148e51f2d30605c';

    const decryptedData = decrypt(encryptedData, key);

    expect(decryptedData).toBeDefined();
    expect(decryptedData).toEqual('55555555000000000000000000000000');
  });
});

describe('encrypt', () => {
  it('should encrypt the data using the given key', () => {
    const data = '55555555000000000000000000000000';
    const key = '4c494d41323030335148e51f2d30605c';

    const notabData = ab2hex(notab(hex2ab('55555555')));
    expect(notabData).toBeDefined();
    expect(notabData).toEqual('aaaaaaaa');

    const encrypted = encrypt(notabData.padEnd(32, '0'), key);
    expect(encrypted).toBeDefined();
    expect(encrypted).toEqual('72f0451e46bbeaf500eaff9d5987aa3c');
  });
});

describe('decrypt', () => {
  it('should decrypt the encrypted data using the given key', () => {
    const encryptedData = 'c507abd706694c66a370817d822feb6a';
    const key = '4c494d413230303351484c5e44030001';

    const decryptedData = decrypt(encryptedData, key);

    expect(decryptedData).toBeDefined();
    expect(decryptedData).toEqual('00000000000000000000000000000000');
  });
});
