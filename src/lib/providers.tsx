'use client';

// We can not useState or useRef in a server component, which is why we are
// extracting this part out into it's own file with 'use client' on top
import { useState } from 'react';
import { QueryClient, QueryClientProvider, QueryCache, MutationCache } from '@tanstack/react-query';
import { Toast } from 'antd-mobile';
import { useLogout } from '@/hooks/useLogout';

export default function Providers({ children }: { children: React.ReactNode }) {
  const logout = useLogout();

  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // With SSR, we usually want to set some default staleTime
            // above 0 to avoid refetching immediately on the client
            staleTime: 1 * 1000,
            retry: false,
          },
        },
        queryCache: new QueryCache({
          onError: (error: any) => {
            if (error?.code === '401' || error?.code === '405') {
              queryClient.clear();
              logout();
            } else {
              Toast.show(error.message);
            }
          },
        }),
        mutationCache: new MutationCache({
          onError: (error: any) => {
            if (error?.code === '401' || error?.code === '405') {
              queryClient.clear();
              logout();
            } else {
              Toast.show(error.message);
            }
          },
        }),
      })
  );

  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
}
