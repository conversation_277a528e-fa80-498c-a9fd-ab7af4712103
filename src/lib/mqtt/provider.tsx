import { useEffect, useState } from 'react';
import mqtt from 'mqtt';
import type { MqttClient } from 'mqtt';
import { MqttContext } from './MqttContext';
import cookies from 'js-cookie';

export default function MqttProvider({ children }: { children: React.ReactNode }) {
  const [mqttClient, setMqttClient] = useState<MqttClient>();

  useEffect(() => {
    const phone = cookies.get('phone');
    const token = cookies.get('token');
    // 如果phone或token不存在，则不尝试连接
    if (!phone || !token) {
      return;
    }

    const incomingStore = new mqtt.Store({ clean: false });

    console.log('[MQTT]: Connecting');
    const client = mqtt.connect(process.env.NEXT_PUBLIC_WEBSOCKET_URL!, {
      port: Number(process.env.NEXT_PUBLIC_WEBSOCKET_PORT),
      path: '/mqtt',
      username: phone,
      password: token,
      clientId: 'mqttjs_' + phone,
      clean: false,
      incomingStore,
    });

    client.on('connect', () => {
      console.log('[MQTT]: Connected');
      setMqttClient(client);
    });

    client.on('message', (topic: string, message: Buffer) => {
      console.log('[MQTT]: Received', topic, message.toString());
      const msg = JSON.parse(message.toString());
      if (msg.message.hasOwnProperty('homeNotice')) {
        // status 1表示异常，0表示恢复
        if (msg.message.status === 1) {
          window.jsBridgeHelper?.sendMessage('webMsgApi', {
            type: 'add',
            key: msg.topic,
            data: msg.message.homeNotice,
          });
        } else {
          window.jsBridgeHelper
            ?.sendMessage('webMsgApi', { type: 'get', key: msg.topic })
            .then((res: string | undefined) => {
              // 如果推送的恢复消息和当前异常一致，则移除
              if (res === msg.message.homeNotice) {
                window.jsBridgeHelper?.sendMessage('webMsgApi', {
                  type: 'remove',
                  key: msg.topic,
                });
              }
            });
        }
      }
    });

    client.on('end', () => {
      console.log('[MQTT]: End');
    });

    return () => {
      console.log('[MQTT]: Disconnecting');
      client.end();
    };
  }, []);

  return <MqttContext.Provider value={mqttClient}>{children}</MqttContext.Provider>;
}
