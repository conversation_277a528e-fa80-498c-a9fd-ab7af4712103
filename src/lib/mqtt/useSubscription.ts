import { useContext, useEffect, useRef, useCallback } from 'react';
import { MqttContext } from './MqttContext';

export default function useSubscription(
  topic: string,
  fn: (topic: string, message: string) => void
) {
  const mqttClient = useContext(MqttContext);
  const fnRef = useRef(fn);

  useEffect(() => {
    fnRef.current = fn;
  }, [fn]);

  const messageHandler = useCallback(
    (receivedTopic: string, receivedMessage: Buffer) => {
      if (topic === receivedTopic) {
        console.log('message', receivedTopic, receivedMessage.toString());
        fnRef.current(receivedTopic, receivedMessage.toString());
      }
    },
    [topic]
  );

  useEffect(() => {
    if (mqttClient && mqttClient.connected) {
      mqttClient.subscribe(topic, { qos: 2 }, (err) => {
        if (!err) {
          console.log('subscribed', topic);
        }
      });

      mqttClient.on('message', messageHandler);
    }

    return () => {
      mqttClient?.removeListener('message', messageHandler);
    };
  }, [mqttClient, messageHandler, topic]);
}
