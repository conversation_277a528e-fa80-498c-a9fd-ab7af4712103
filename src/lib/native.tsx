'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import cookies from 'js-cookie';
import { useRouter } from 'next/navigation';

export type NativeProps = {
  // 安全区域
  safeArea: {
    top: number;
    bottom: number;
  };
  isAndroid: boolean;
  isiOS: boolean;
  // 个推cid
  clientId: string;
  // 是否安装微信
  hasWechat: boolean;
  // 是否安装支付宝
  hasAlipay: boolean;
};

export const NativeContext = createContext<NativeProps | undefined>(undefined);

export const useNative = () => {
  const context = useContext(NativeContext);
  if (!context) {
    throw new Error('Please use NativeProvider first');
  }
  return context;
};

export type NativeProviderProps = {
  children?: React.ReactNode;
};

export const NativeProvider = ({ children }: NativeProviderProps) => {
  const [safeArea, setSafeArea] = useState({
    top: 0,
    bottom: 0,
  });
  const [clientId, setClientId] = useState('');
  const [isAndroid, setIsAndroid] = useState(false);
  const [isiOS, setIsIOS] = useState(false);
  const router = useRouter();
  const hasWechat = cookies.get('wxInstalled') === 'true';
  const hasAlipay = cookies.get('aliPayInstalled') === 'true';

  useEffect(() => {
    // 获取安全区域
    window.jsBridgeHelper
      ?.sendMessage('safeArea')
      .then((res: { top: number; bottom: number; platform: string }) => {
        console.log('[APP] safeArea:>> ', JSON.stringify(res));
        const { top, bottom, platform } = res;
        // 顶部安全区域默认37
        setSafeArea({ top: top || 37, bottom });
        setIsAndroid(platform === 'Android');
        setIsIOS(platform === 'iOS');
      });
    // 获取个推cid
    window.jsBridgeHelper?.sendMessage('getuiCid').then((res: string | null) => {
      console.log('[APP] getuiCid:>> ', res);
      setClientId(res || '');
    });
  }, [router]); // Empty dependency array ensures useEffect runs only once on mount

  return (
    <NativeContext.Provider value={{ safeArea, isAndroid, isiOS, clientId, hasWechat, hasAlipay }}>
      {children}
    </NativeContext.Provider>
  );
};
