import { useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useBLEClient } from '@/lib/ble';

// 切换车辆后需要做一些逻辑处理
export function useAfterSwitchVehicle() {
  const queryClient = useQueryClient();
  const bleClient = useBLEClient();

  return useCallback(() => {
    queryClient.clear();
    // Disconnect the BLE connection
    bleClient.disconnect();
  }, [queryClient, bleClient]);
}
