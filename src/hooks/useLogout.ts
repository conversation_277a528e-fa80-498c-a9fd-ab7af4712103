import { useCallback } from 'react';
import { useRouter } from 'next/navigation';
import cookies from 'js-cookie';

// 登出时需要做一些逻辑处理
export function useLogout() {
  const router = useRouter();

  return useCallback(() => {
    cookies.remove('token');
    cookies.remove('phone');
    window.jsBridgeHelper?.sendMessage('clearAppCache', ['token', 'userInfo']);
    router.push('/user/landing');
  }, [router]);
}
