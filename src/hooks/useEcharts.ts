import type {
  BarSeriesOption,
  LineSeriesOption,
  PieSeriesOption,
  ScatterSeriesOption,
  PictorialBarSeriesOption,
  RadarSeriesOption,
  GaugeSeriesOption,
} from 'echarts/charts';
import type {
  TitleComponentOption,
  LegendComponentOption,
  TooltipComponentOption,
  GridComponentOption,
  ToolboxComponentOption,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { useEffect, useRef, useCallback } from 'react';

export type ECOption = echarts.ComposeOption<
  | BarSeriesOption
  | LineSeriesOption
  | PieSeriesOption
  | ScatterSeriesOption
  | PictorialBarSeriesOption
  | RadarSeriesOption
  | GaugeSeriesOption
  | TitleComponentOption
  | LegendComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | ToolboxComponentOption
>;

export function useEcharts(components: Parameters<typeof echarts.use>[0], option: ECOption) {
  const elRef = useRef<HTMLDivElement>(null);
  const echartRef = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    const instance = echarts.getInstanceByDom(elRef.current!);
    if (!instance) {
      // Register the required components
      echarts.use(components);
      // Initialize echarts instance
      echartRef.current = echarts.init(elRef.current!);
      echartRef.current.setOption(option);
    }
  }, [components, option]);

  useEffect(() => {
    return () => {
      if (echartRef.current) {
        echartRef.current.dispose();
      }
    };
  }, []);

  const setOption = useCallback((option: ECOption) => {
    if (echartRef.current) {
      echartRef.current.setOption(option);
    }
  }, []);

  return {
    elRef,
    setOption,
  };
}
