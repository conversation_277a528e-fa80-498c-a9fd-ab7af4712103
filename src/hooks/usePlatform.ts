import { useEffect, useState } from 'react';

export const canUseDom = !!(
  typeof window !== 'undefined' &&
  typeof document !== 'undefined' &&
  window.document &&
  window.document.createElement
);

export const usePlatform = () => {
  const [isAndroid, setIsAndroid] = useState(false);
  const [isIOS, setIsIOS] = useState(false);

  useEffect(() => {
    if (canUseDom) {
      const userAgent = window.navigator.userAgent.toLowerCase();
      setIsAndroid(/android/.test(userAgent));
      setIsIOS(/iphone|ipad|ipod|ios/.test(userAgent));
    }
  }, []);
  return { isAndroid, isIOS };
};
