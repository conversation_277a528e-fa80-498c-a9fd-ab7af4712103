<svg width="68" height="70" viewBox="0 0 68 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle opacity="0.4" cx="34" cy="37" r="33" fill="url(#paint0_linear_891_4837)" fill-opacity="0.5"/>
<g filter="url(#filter0_d_891_4837)">
<circle cx="34" cy="37" r="27" transform="rotate(-180 34 37)" fill="url(#paint1_linear_891_4837)"/>
</g>
<defs>
<filter id="filter0_d_891_4837" x="0.333333" y="0.666666" width="67.3333" height="67.3333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.66667"/>
<feGaussianBlur stdDeviation="3.33333"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_891_4837"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_891_4837" result="shape"/>
</filter>
<linearGradient id="paint0_linear_891_4837" x1="19" y1="10.5" x2="52" y2="70" gradientUnits="userSpaceOnUse">
<stop stop-color="#C9CDD3"/>
<stop offset="1" stop-color="#EEEEEE"/>
</linearGradient>
<linearGradient id="paint1_linear_891_4837" x1="13.5" y1="10" x2="56" y2="60.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#EFEFF1"/>
<stop offset="0.838542" stop-color="white"/>
</linearGradient>
</defs>
</svg>
