<svg width="68" height="70" viewBox="0 0 68 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle opacity="0.4" cx="34" cy="37" r="33" fill="url(#paint0_linear_891_4849)" fill-opacity="0.5"/>
<g filter="url(#filter0_d_891_4849)">
<circle cx="34" cy="37" r="27" transform="rotate(-180 34 37)" fill="#32D74B"/>
</g>
<defs>
<filter id="filter0_d_891_4849" x="0.333333" y="0.666666" width="67.3333" height="67.3333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.66667"/>
<feGaussianBlur stdDeviation="3.33333"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_891_4849"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_891_4849" result="shape"/>
</filter>
<linearGradient id="paint0_linear_891_4849" x1="19" y1="10.5" x2="52" y2="70" gradientUnits="userSpaceOnUse">
<stop stop-color="#32D74B"/>
<stop offset="1" stop-color="#32D74B" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
